我是一个软件工程师，我需要编写软件设计文档。
目前我有一个现成的软件及其代码，我如何使用AI工具协助我编写这个软件的概要设计、数据库设计、详细设计文档，
我的期望效果是由AI工具完成文档初稿，我手工进行审核修改。
请推荐具体的方案以及我需要提供的数据，以及需要做什么资料准备或者需要提供什么工具。
请先不要开始输出设计文件，我们先对方案达成一致后，我们再开始分析代码，输出文档等。
已知我目前能提供：
1、源代码，见文件夹@E:\AITest\EAIDoc\Cursor\CADWSAddin。
2、软件功能概述：
生成的结果文件dwg 包含了多个子dwg文件，该总dwg文件构成方式是多层级的父子关系，
最顶层是根dwg文件，按不同的视图向下层有一至多个子dwg文件，
子dwg文件中有一个动态块的块参照。
程序是先打开根dwg文件，从父文件到子文件逐层打开, 驱动动态块. 再将驱动完成的子文件的块参照插入到父文件中,直到全部插入到根文件。
工作过程：
启动AutoCAD软件后加载插件。
弹出取任务的窗口， 取一条任务， 获取该任务对应的命令。
命令中会含有要驱动的文件的原模型路径、要生成的成果物名称及要对原文件执行的变化。
模型文件有父子关系，父文件中有带名称的点，子文件插入到父文件中将通过找指定名称的点来确定其插入位置。 
按照父子结构，先打开根模型文件进行尺寸驱动后，
再打开子文件，将子文件进行尺寸驱动后， 将子文件数据库插入到父文件的模型空间中。
将指定的子文件全部插入父文件后， 保存父文件。
3、软件开发使用的技术：
.NET & C#
AutoCAD API
4、没有直接使用数据库，是使用其他组件的API获取数据。
5、设计文档的内容结构，可以按照业界通用结构，在我们得到初稿以后，可以沟通进行调整。
文档格式期望是word格式的文档，文档的详细程度期望面向开发人员的技术文档，越详细越好。
6、软件有配置文件，也有外部依赖的API，这个软件只是我们一个应用系统的一个子模块，
所以它会调用其他组件的API，请在代码分析时确定。外部依赖的API只需要提供调用是的输入和输出说明接口，不需要分析或反编译其内部功能。
7、没有特别需要关注的模块，做成一致的详细程度。
8、详细设计文档的要求：
1）遍历所有目录，做详细代码分析。
2）需要含类图、时序图，部分比较复杂的方法需要放伪代码。
3）每个类包含属性定义和描述，每个类的方法需要包含方法参数的定义和说明，方法体的逻辑说明。
