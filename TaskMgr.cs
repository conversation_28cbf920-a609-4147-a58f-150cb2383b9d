﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;
using Zxtech.CADTaskServer.Contract;

namespace CADWSAddin
{
    public class TaskMgr
    {
        //int taskId
        public int taskId = new int();
        //图纸命名
        public List<string> DrawingNamels = new List<string>();
        //文字驱动使用
        public List<string> SourceTextls = new List<string>();
        public List<string> TargetTextls = new List<string>();
        //图层驱动使用
        public List<string> LayerNamels = new List<string>();
        public List<string> LayerValuels = new List<string>();
        //表格驱动使用
        public List<string> TableNamels = new List<string>();
        public List<string> TableValuels = new List<string>();
        //标注驱动使用
        public List<string> DimValuels = new List<string>();
        //文字样式替换使用
        public List<string> TextSourceStylels = new List<string>();
        public List<string> TextTargetStylels = new List<string>();
        //标注样式替换使用
        public List<string> DimSourceStylels = new List<string>();
        public List<string> DimTargetStylels = new List<string>();
        public Component ParseTaskFile(XDocument xdoc)
        {
            //if (!File.Exists(xmlFile))
            //    return null;

            Component rootComp = null;
            try
            {
                //XDocument xdoc = XDocument.Load(xmlFile);
                XElement xele = xdoc.Element("Node");
                rootComp = ParseNodeXML(xele);
            }
            catch (Exception ex)
            {
                Application.ShowAlertDialog(ex.ToString());
                return null;
            }
            return rootComp;
        }


        private Component ParseNodeXML(XElement xele)
        {
            Component comp = new Component ();

            XElement e1 = xele.Element("ModelName");
            comp.ModelName = e1.Value;

            XElement e2 = xele.Element("BlockName");
            comp.BlockName = e2.Value;

            XElement e3 = xele.Element("Parameters");
            foreach (XElement e in e3.Elements())
            {
                Parameter p = new Parameter();
                p.ParseXML(e);
                comp.AddParameter(p);
            }

            XElement e4 = xele.Element("Functions");
            foreach (XElement e in e4.Elements())
            {
                Function f = new Function();
                f.ParseXML(e);
                comp.AddFunction(f);
            }

            XElement e5 = xele.Element("ChildNodes");
            foreach (XElement e in e5.Elements())
            {
                Component child = ParseNodeXML( e);               
                comp.AddChildNode(child);
            }

            return comp;
        }
        //private void ParseNodeList(Component comp, List<CADTaskCode> ls,int i)
        //{

        //    String e1 = ls[i].Para3;//相对路径需解析
        //    comp.ModelName = e1;

        //    String e2 = ls[i].Para1;//根据'DWG-IB@对重','对重'解析
        //    comp.BlockName = e2;

        //    String e3 = ls[i].Para2;//需要解析数字变量及可见性、翻转状态
        //    foreach (XElement e in e3.Elements())
        //    {
        //        Parameter p = new Parameter();
        //        p.ParseXML(e);
        //        comp.AddParameter(p);
        //    }

        //    XElement e4 = xele.Element("Functions");
        //    foreach (XElement e in e4.Elements())
        //    {
        //        Function f = new Function();
        //        f.ParseXML(e);
        //        comp.AddFunction(f);
        //    }

        //    XElement e5 = xele.Element("ChildNodes");
        //    foreach (XElement e in e5.Elements())
        //    {
        //        Component child = new Component();
        //        ParseNodeXML(child, e);

        //        comp.AddChildNode(child);
        //    }
        //}

        public string ParsePara3(string Para3)
        {
            string filePath = null;
            if (Para3!=null)
            {
                filePath = @""+Environment.CurrentDirectory.ToString() + Para3;//.Substring(1, Para3.Length-1);
                 return filePath;
            }
            else
                return filePath;

        }
    }
}
