﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using Autodesk.AutoCAD.EditorInput;
using AcadApp = Autodesk.AutoCAD.ApplicationServices;

using Autodesk.AutoCAD.DatabaseServices;
using System.Drawing;
using Autodesk.AutoCAD.Geometry;
using CADWSAddin;
using CADWSAddin.Tools;
using CADWSAddin.XML;
using Zxtech.CADTaskServer;
using System.Xml;
using Zxtech.CADTaskServer.Contract;
using System.Xml.Linq;
using Zxtech.PdsConstant;
using System.Threading;
using System.Configuration;
using System.Data.OleDb;
using System.Diagnostics;
using System.Globalization;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Security.Policy;
using System.ServiceModel;
using System.ServiceModel.Description;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.Interop.Common;
using Autodesk.AutoCAD.PlottingServices;
//using CADWSAddin.RevitServiceFun;
using iTextSharp.text;
using Microsoft.Office.Interop.Excel;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Org.BouncyCastle.Asn1.Microsoft;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;
using Arc = Autodesk.AutoCAD.DatabaseServices.Arc;
using Color = Autodesk.AutoCAD.Colors.Color;
using Exception = System.Exception;
using Line = Autodesk.AutoCAD.DatabaseServices.Line;
using Region = Autodesk.AutoCAD.DatabaseServices.Region;
using SingleQuoteStringParser = Zxtech.CADTaskServer.SingleQuoteStringParser;
using System.Web.Script.Serialization;

namespace CADWSAddin
{
    public class IniConfigInfo
    {
        public bool IsMergeDwgFiles { get; set; }
        //public string MergeDwgFileName { get; set; }
        public float GapDistance { get; set; }
        public bool IsMergePdfFiles { get; set; }
        public bool IsHoldOnDimenPoints { get; set; }
        public bool IsHoldOnUnusedFiles { get; set; }
        public bool IsNoRenamePoints { get; set; }
        public double DimMinPinLength { get; set; }

        public bool IsFuzzyMatchPointName { get; set; }
        public int UserInterval { get; set; }
    }

    public class Palettes
    {
        public static bool IsWorking { get; set; }

        private static List<string> _constNoDeleteExts = new List<string>() { ".dwl", ".dwl2" };

        public static CADTaskServer taskServer;
        public static CADWorkStationInfo stationInfo;
        public static string NoStandardDimJson;
        public static double CarryingCapacity ; //承载重量
        public static IniConfigInfo IniConfigData { get; set; }

        //private static CADTaskServiceClient _CADServiceClient;
        //public static CADTaskServiceClient CADServiceClient
        //{
        //    get 
        //    {
        //        if (_CADServiceClient == null)
        //        {
        //            _CADServiceClient = GetTaskServer();

        //        }
        //        return _CADServiceClient;
        //    }

        //}

        private static List<string> _holdOnFiles = new List<string>(); //保留文件
        private static List<string> _noMergeFiles = new List<string>(); //保留文件

        private static int _assListCount = 0;
        public  static int _assListIndex = 0;

        public static List<List<CADTaskCode>> _listTaskCodes = null;
        public static List<CADTaskCode> TaskCodes = null;
        public static List<CADCommandCache> totalCommands = new List<CADCommandCache>();

        private static string _curTaskFoldName = string.Empty;
        private static string _curTaskMergeFileName = string.Empty;

        private string ModelID = string.Empty;
        private int language;
        

        public static void AddHoldOnFiles(string file)
        {
            if (!_holdOnFiles.Exists(p => p == file)) _holdOnFiles.Add(file);
        }

        public static void AddNoMergeFiles(string file)
        {
            if (!_noMergeFiles.Exists(p => p == file)) _noMergeFiles.Add(file);
        }

        
        public static void AddHoldOnFiles(string path,IList<string> files)
        {
            foreach (var file in files)
            {
                string fileAllPath = path + file;
                if (!_holdOnFiles.Exists(p => p == fileAllPath)) _holdOnFiles.Add(fileAllPath);
            }
        }
       

        public static void ClearHoldOnFiles()
        {
            _holdOnFiles.Clear();
        }

        public static void DeleteFixExtendFiles(string ext)
        {
            for (int i = _holdOnFiles.Count - 1; i > -1; --i)
            {
                if (Path.GetExtension(_holdOnFiles[i]) == ext) _holdOnFiles.RemoveAt(i);
            }
        }

        [CommandMethod("JD")]
        public void ShowCADWorkstation()
        {
            ////自加载工作站界面
            //CADWorkstationFace frm = new CADWorkstationFace();

            //frm.GetModelData += frm_GetModelData;
            //frm.RTEXTHandler += frm_RTEXTHandler;
            //AcadApp.ShowModelessDialog(frm);
            taskServer = new CADTaskServer();
            taskServer.GetCADTask += taskServer_GetCADTask;
            taskServer.RunCADCodeEvent += taskServer_RunCADCodeEvent;
            taskServer.GetPropertysEvent += taskServer_GetProperties;
            taskServer.GetDimlistEvent += taskServer_GetDimlist;
            taskServer.GetChildPartEvent += taskServer_GetChildPart;
            stationInfo = taskServer.Load(language, 200);
            if (taskServer.formCADTaskServer != null)
                if (taskServer.formCADTaskServer.ServiceConfigInfo != null)
                    Utility.BendAllowanceDefaultPath = taskServer.formCADTaskServer.ServiceConfigInfo.BendAllowanceDefaultPath;
            //  var station = taskServer.Load(0,200);

            
        }
        public List<string> taskServer_GetChildPart(string filePath, string configName)
        {
            var code1 = GetOpenModelCommand(filePath, configName);
            code1 = taskServer.RunCADCode(code1);


            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para2 = CadItemType.asmComp + "";
            code.Para1 = filePath;
            code = taskServer.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            code.Para2 = CadItemType.asmCompModelItem + "";
            code = taskServer.RunCADCode(code);
            result = code.Para3;
            var lst1 = new List<string>();
            SingleQuoteStringParser.Unpack(result, out lst1);
            lst.AddRange(lst1);
            return lst;
        }
        public List<string> taskServer_GetDimlist(string modelFileName, string configName)
        {
            var code = new CADTaskCode();
            code.Para1 = modelFileName;
            code.CadCodeId = 6;
            code.Para2 = CadItemType.dimItem + "";
            code = taskServer.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            return lst;
        }


        private static CADTaskCode GetOpenModelCommand(string filePath, string configName)
        {
            var code1 = new CADTaskCode();
            code1.CadCodeId = 1;
            code1.Para1 = filePath;
            code1.Para2 = configName;
            //ModelId
            code1.Para3 = "0";
            //FileType
            code1.Para4 = "1";
            //ShowWin
            code1.Para5 = "Y";
            return code1;
        }
        public Dictionary<string, string> taskServer_GetProperties(string filePath, string refConfig = null)
        {
            var code1 = GetOpenModelCommand(filePath, refConfig);
            code1 = taskServer.RunCADCode(code1);



            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para1 = filePath;
            code.Para2 = CadItemType.propItem + "";
            code = taskServer.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            var dic = new Dictionary<string, string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            foreach (string s in lst)
            {
                dic[s] = "";
            }
            return dic;
        }

        //public static byte[] GetDrawing(string drawingNo)
        //{

        //    var find = CADServiceClient.GetCurrentModelFile(drawingNo, null, "RD", false, "dwg", false);
        //    if (find != null)
        //        return find.FileContent;
        //    return null;

        //}
        /// <summary>
        /// 
        /// </summary>
        /// 
        [CommandMethod("test1")]
        public void DrawingFan()
        {
            var database = Application.DocumentManager.MdiActiveDocument.Database;
            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                Polyline poly = new Polyline();
                Point3d a = new Point3d();
                Point3d b = new Point3d(50, 25, 0);
                Point3d c = new Point3d(100, 0, 0);
                Arc yuan = new Arc(a, 50, 45, 135);
                Line lin1 = new Line(yuan.EndPoint, a);
                Line lin = new Line(yuan.StartPoint, a);
                database.AddToModelSpace(lin, lin1, yuan);
                double minx = Math.Min(a.X, b.X);
                double miny = Math.Min(a.Y, b.Y);
                double maxx = Math.Max(a.X, b.X);
                double maxy = Math.Max(a.Y, b.Y);
                Point2dCollection ptcePoint2DCollection = new Point2dCollection();
                ptcePoint2DCollection.Add(new Point2d(minx, miny));
                ptcePoint2DCollection.Add(new Point2d(minx, maxy));
                ptcePoint2DCollection.Add(new Point2d(maxx, maxy));
                ptcePoint2DCollection.Add(new Point2d(maxx, miny));
                //poly.c
                trans.Commit();
            }

        }

        [CommandMethod("gdt")]
        public void gdt()
        {

            var _database = AcadApp.Application.DocumentManager.MdiActiveDocument.Database;
            using (Transaction trans = _database.TransactionManager.StartTransaction())
            {
                //var t = _database.GetAllDynBlockReferences(blkName);
                // var t2 = _database.GetAllBlockReferences(blkName);
                BlockTable bt = (BlockTable)_database.BlockTableId.GetObject(OpenMode.ForRead);
                //trans.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;

                    // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                    // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                    BlockTableRecord btr = trans.GetObject(bt["井道装配"], OpenMode.ForWrite) as BlockTableRecord;



                    foreach (ObjectId blockId in btr)
                    {
                        Palettes.WriteLog(blockId.ObjectClass.DxfName);
                        if (string.Compare(blockId.ObjectClass.DxfName, "INSERT", true) != 0) continue;
                        BlockReference childBlock = blockId.GetObject(OpenMode.ForRead) as BlockReference;
                        if (childBlock == null) continue;
                        if (!childBlock.IsDynamicBlock) continue;
                        //  if (!childBlock.IsDynamicBlock || childBlock.Name != blkName) continue;//匿名块问题

                            for (int j = 0; j < childBlock.DynamicBlockReferencePropertyCollection.Count; j++)
                            {
                                Palettes.WriteLog(childBlock.DynamicBlockReferencePropertyCollection[j].PropertyName);

                            }

                    }
                    trans.Commit();

            }
        }

        //代码整理过程中进行了注释 modify by xqxqx 2022-1-26
        /////OLE操作
        ///// 
        ///// 
        //[CommandMethod("OLE")]
        //public void OleOper()
        //{
        //    try
        //    {
        //        string filePath = @"C:\test";
        //        var dwgFilelist = new DirectoryInfo(filePath).GetFiles("*.dwg", SearchOption.AllDirectories);
        //        foreach (var dwgItem in dwgFilelist)
        //        {
        //            if (!File.Exists(dwgItem.FullName))
        //            {
        //                Palettes.WriteLog(string.Format("{0}没有找到", dwgItem.FullName)); 
        //                continue;
        //            }
        //            AcadApp.Document AssemDoucument = null;
        //            AssemDoucument = AcadApp.Application.DocumentManager.Add(dwgItem.FullName);
        //            Database database = AssemDoucument.Database;

        //           string  info= OLEOper(database, dwgItem.FullName);
        //            AssemDoucument.CloseAndSave(dwgItem.FullName);
        //           /* MessageBox.Show(Path.Combine(Path.GetDirectoryName(dwgItem.FullName), Path.GetFileNameWithoutExtension(dwgItem.FullName) + ".txt") + "\n" + info);
        //            FileStream fs = new FileStream(Path.Combine(Path.GetDirectoryName(dwgItem.FullName),Path.GetFileNameWithoutExtension(dwgItem.FullName) + ".txt"), FileMode.Create);
        //            //获得字节数组
        //            byte[] data = System.Text.Encoding.Default.GetBytes(info);
        //            //开始写入
        //            fs.Write(data, 0, data.Length);
        //            //清空缓冲区、关闭流
        //            fs.Flush();
        //            fs.Close();*/

        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        MessageBox.Show(string.Format("错误：{0}", ex.ToString()+ex.StackTrace));
        //    }
        //}
        private string OLEOper(Database datebase, string name)
        {



            using (Transaction trans = datebase.TransactionManager.StartTransaction())
            {
                try
                {
                    BlockTableRecord blcRecord = datebase.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                    foreach (ObjectId blcokId in blcRecord)
                    {
                        if (blcokId.ObjectClass.Name == "AcDbOle2Frame")
                        {
                            Ole2Frame oleFrame = trans.GetObject(blcokId, OpenMode.ForRead) as Ole2Frame;
                            if (oleFrame.UserType == "Picture (Metafile)")
                            {
                                continue;
                            }
                            if (oleFrame.OleObject is Microsoft.Office.Interop.Excel.Workbook)
                            {
                                Microsoft.Office.Interop.Excel.Workbook wb = oleFrame.OleObject as Microsoft.Office.Interop.Excel.Workbook;
                                //wb.SaveAs(Path.GetFileNameWithoutExtension(name) + ".xlsx");
                                Microsoft.Office.Interop.Excel.Worksheet ws = (Worksheet)wb.ActiveSheet;

                                Microsoft.Office.Interop.Excel.Range range = ws.UsedRange;

                                // Microsoft.Office.Interop.Excel.Application newEcel = new Microsoft.Office.Interop.Excel.ApplicationClass();
                                // newEcel.Visible = false;
                                // Workbook newwb = newEcel.Workbooks.Add(true);
                               // Worksheet newsheet = newwb.Worksheets[1] as Worksheet;




                                for (int row = 1; row <= range.Rows.Count; row++)
                                {
                                    string tableinfo = null;
                                    for (int col = 1; col <= range.Columns.Count; col++)
                                    {
                                        Range range1 = range.Cells[row, col] as Microsoft.Office.Interop.Excel.Range;
                                        if (range1 != null)
                                           // newsheet.Cells[row, col] = range1.Value2;
                                            tableinfo += Convert.ToString(range1.Value2) + ",";
                                    }
                                   string logPath = Path.Combine(Path.GetDirectoryName(name), Path.GetFileNameWithoutExtension(name)) + ".txt";
                                    FileStream logTxt = new FileStream(logPath, FileMode.OpenOrCreate | FileMode.Append);
                                    var write = new StreamWriter(logTxt, Encoding.UTF8);
                                    write.WriteLine(tableinfo + "\n");
                                    write.Flush();
                                    logTxt.Close();

                                }
                               /* newEcel.DisplayAlerts = false;
                                newEcel.AlertBeforeOverwriting = false;
                                newwb.Save();
                                newEcel.Save(Path.Combine(Path.GetDirectoryName(name), Path.GetFileNameWithoutExtension(name)) + ".xlsx");
                                newEcel.SaveWorkspace(Path.Combine(Path.GetDirectoryName(name), Path.GetFileNameWithoutExtension(name)) + ".xlsx");
                                newEcel.Quit();*/


                            }


                        }


                        /* estat=acdbOpenAcDbEntity(pEnt, insert_ID, AcDb::kForRead);
                        AcDbOle2Frame *pBlock = AcDbOle2Frame::cast(pEnt);

                       COleClientItem *d=(COleClientItem*)(pBlock->getOleObject()); 

                       CDocument *a=(CDocument*)d->GetDocument();
                        void fun=CDocument::OnFileSaveAs();*/
                        //a->OnFileSaveAs();
                        //d->GetDocument()->OnFileSaveAs();//Protected member function

                        //d->GetDocument()->OnFileSendMail();
                        //d->CopyToClipboard(TRUE);//这个只是复制这个ole对象，只能粘贴回cad 

                    }
                    trans.Commit();
                    trans.Dispose();
                }
                catch (Exception ex)
                {

                    return "";
                }

            }

            return "";

        }
        /* 代码整理过程中进行了注释 modify by xqxqx 2022-1-26
        /// <summary>
        /// DWG文件批量生成PDF
        /// </summary>
         [CommandMethod("dtp")]
        public void DwgtoPDF()
        {
            
                string filePath = @"C:\test";
                string Des = @"C:\dwg_pdf";
                if (!Directory.Exists(Des))
                {
                    Directory.CreateDirectory(Des);
                }
                var dwgFilelist = new DirectoryInfo(filePath).GetFiles("*.dwg", SearchOption.AllDirectories);
               
                foreach (var dwgItem in dwgFilelist)
                {
                  try
                 {
                    if (!File.Exists(dwgItem.FullName))
                    {
                        Palettes.WriteLog(string.Format("{0}没有找到", dwgItem.FullName));
                        continue;
                    }
                    AcadApp.Document AssemDoucument = null;
              
                    AssemDoucument = AcadApp.Application.DocumentManager.Add(dwgItem.FullName);
                    
                    Database database = AssemDoucument.Database;

                   // string info = OLEOper(database, dwgItem.FullName);
                    string pdfFile = dwgItem.FullName.Replace(dwgItem.Extension,".pdf"); ;
                    Palettes.WriteLog(string.Format("生成文件 {0}", pdfFile));
                    // PlotTools.PlotToPDF(pdfFile);
                    AssemDoucument.Database.SaveAs(dwgItem.FullName, DwgVersion.AC1024);
                   beginPrint(AssemDoucument, pdfFile, null, null);
                    AssemDoucument.CloseAndSave(dwgItem.FullName);
                    
                    Palettes.WriteLog(string.Format("复制DWG文件{0}，到文件 {1}", dwgItem.FullName,Path.Combine(Des,dwgItem.Name)));
                    File.Copy(dwgItem.FullName,Path.Combine(Des,dwgItem.Name));
                    Palettes.WriteLog(string.Format("复制PDF文件{0}，到文件 {1}", pdfFile, Path.Combine(Des, Path.GetFileName(pdfFile))));
                    File.Copy(pdfFile, Path.Combine(Des, Path.GetFileName(pdfFile)));
                    Palettes.WriteLog(string.Format("删除PDF文件{0}，到文件 {1}", dwgItem.FullName, pdfFile));
                    File.Delete(dwgItem.FullName);
                    File.Delete(pdfFile);
                 }
                  catch (Exception ex)
                  {

                      Palettes.WriteLog(string.Format("图纸：{0}生成PDF错误：{1}", dwgItem.FullName, ex.ToString() + ex.StackTrace),2);
                      continue;
                  }
                }
            
        }

         public PlotRotation rotAng = PlotRotation.Degrees000;
         public string devName = "DWG To PDF.pc3";
         public string devPaper = "ISO_full_bleed_A3_(420.00_x_297.00_MM)";

         //开始打印
         private void beginPrint(AcadApp.Document documentitem, string fileName, string devpapertype, string scale)
         {
             for (; ; )
             {
                 if (PlotFactory.ProcessPlotState == ProcessPlotState.NotPlotting)
                 {
                     break;
                 }
             }
             if (fileName == null)
             {
                 return;
             }
             if (devpapertype != null && devpapertype != "")
             {
                 if (devpapertype.ToUpper() == "A0")
                 {
                     devPaper = "ISO_full_bleed_A0_(841.00_x_1189.00_MM)";
                 }
                 else if (devpapertype.ToUpper() == "A1")
                 {
                     devPaper = "ISO_full_bleed_A1_(841.00_x_594.00_MM)";
                 }
                 else if (devpapertype.ToUpper() == "A2")
                 {
                     devPaper = "ISO_full_bleed_A2_(594.00_x_420.00_MM)";
                 }
                 else if (devpapertype.ToUpper() == "A3")
                 {
                     devPaper = "ISO_full_bleed_A3_(420.00_x_297.00_MM)";
                 }
                 else if (devpapertype.ToUpper() == "A4")
                 {
                     devPaper = "ISO_full_bleed_A4_(297.00_x_210.00_MM)";
                 }
             }
             Database db = null;
             AcadApp.Document doc = null;
             if (documentitem == null)
             {
                 doc = Application.DocumentManager.MdiActiveDocument;
                 Application.SetSystemVariable("BACKGROUNDPLOT", 0);            //否则打印速度会很慢

                 Editor ed = doc.Editor;
                 db = doc.Database;

             }
             else
             {
                 doc = documentitem;
                 Application.SetSystemVariable("BACKGROUNDPLOT", 0);            //否则打印速度会很慢

                 Editor ed = doc.Editor;
                 db = doc.Database;

             }

             #region 事务范围
             using (Transaction tr = db.TransactionManager.StartTransaction())
             {
                 // We'll be plotting the current layout
                 #region 打印设置
                 BlockTableRecord btr = (BlockTableRecord)tr.GetObject(db.CurrentSpaceId, OpenMode.ForRead);
                 Layout lo = (Layout)tr.GetObject(btr.LayoutId, OpenMode.ForRead);

                 // We need a PlotInfo object
                 // linked to the layout

                 PlotInfo pi = new PlotInfo();
                 pi.Layout = btr.LayoutId;

                 // We need a PlotSettings object based on the layout settings which we then customize

                 PlotSettings ps = new PlotSettings(lo.ModelType);
                 ps.CopyFrom(lo);
                 ps.SetShadePlot(PlotSettingsShadePlotType.Wireframe, btr.Id);  //by zld

                 // The PlotSettingsValidator helps
                 // create a valid PlotSettings object

                 PlotSettingsValidator psv = PlotSettingsValidator.Current;

                 // We'll plot the extents, centered and 
                 // scaled to fit
                 psv.SetPlotRotation(ps, rotAng);
                 psv.SetPlotType(ps, Autodesk.AutoCAD.DatabaseServices.PlotType.Extents);

                 if (scale != null && scale != "")
                 {
                     try
                     {
                         CustomScale Scale = new CustomScale(double.Parse(scale.Split(':')[0]), double.Parse(scale.Split(':')[1]));
                         psv.SetCustomPrintScale(ps, Scale);
                     }
                     catch (System.Exception ex)
                     {
                         Palettes.WriteLog(ex.Message, 2);
                         Palettes.WriteLog(ex.StackTrace, 2);
                     }
                 }
                 else
                 {
                     psv.SetUseStandardScale(ps, true);
                     psv.SetStdScaleType(ps, StdScaleType.ScaleToFit);
                 }
                 psv.SetPlotCentered(ps, true);
                 if (devpapertype != null && devpapertype != "")
                 {
                     if (devpapertype.ToUpper() == "A0")
                     {
                         psv.SetPlotRotation(ps, PlotRotation.Degrees090);
                     }
                 }
                 psv.SetPlotConfigurationName(ps, devName, devPaper);
                 psv.SetPlotPaperUnits(ps, PlotPaperUnit.Millimeters);

                 // We need to link the PlotInfo to the PlotSettings and then validate it
                 pi.OverrideSettings = ps;
                 PlotInfoValidator piv = new PlotInfoValidator();
                 piv.MediaMatchingPolicy = MatchingPolicy.MatchEnabled;
                 piv.Validate(pi);

                 #endregion

                 // A PlotEngine does the actual plotting(can also create one for Preview)

                 using (PlotEngine pe = PlotFactory.CreatePublishEngine())
                 {
                     // Create a Progress Dialog to provide info and allow thej user to cancel 
                     #region Create a Progress Dialog
                     using (PlotProgressDialog ppd = new PlotProgressDialog(false, 1, true))
                     {
                         ppd.set_PlotMsgString(PlotMessageIndex.DialogTitle, "正在打印");
                         ppd.set_PlotMsgString(PlotMessageIndex.CancelJobButtonMessage, "取消当前打印");
                         ppd.set_PlotMsgString(PlotMessageIndex.CancelSheetButtonMessage, "取消本页打印");
                         ppd.set_PlotMsgString(PlotMessageIndex.SheetSetProgressCaption, "打印集合进度");
                         ppd.set_PlotMsgString(PlotMessageIndex.SheetProgressCaption, "打印页进度");

                         ppd.LowerPlotProgressRange = 0;
                         ppd.UpperPlotProgressRange = 100;
                         ppd.PlotProgressPos = 0;

                         // Let's start the plot, at last

                         ppd.OnBeginPlot();
                         ppd.IsVisible = false;
                         pe.BeginPlot(ppd, null);

                         // We'll be plotting a single document

                         pe.BeginDocument(pi, doc.Name, null, 1, true, fileName);

                         // Which contains a single sheet
                         ppd.OnBeginSheet();

                         ppd.LowerSheetProgressRange = 0;
                         ppd.UpperSheetProgressRange = 100;
                         ppd.SheetProgressPos = 0;

                         PlotPageInfo ppi = new PlotPageInfo();
                         pe.BeginPage(ppi, pi, true, null);
                         pe.BeginGenerateGraphics(null);
                         pe.EndGenerateGraphics(null);

                         // Finish the sheet
                         pe.EndPage(null);
                         ppd.SheetProgressPos = 100;
                         ppd.OnEndSheet();

                         // Finish the document

                         pe.EndDocument(null);

                         // And finish the plot

                         ppd.PlotProgressPos = 100;
                         ppd.OnEndPlot();
                         pe.EndPlot(null);

                     }
                     #endregion
                 }

                 tr.Commit();
             }
             #endregion

         }
        */
        ///<>
        /// 图纸提取
        /// 
        /// /////
        [CommandMethod("dwgc")]
        public void GetDwgInfo()
        {
            try
            {
                string basePath = System.IO.Path.GetDirectoryName(typeof(FormCADTaskServer).Assembly.CodeBase.Replace("file:///", ""));
                String cfgClientFileName = basePath + "\\CADTaskClientConfig.xml";
                string configinfo = File.ReadAllText(cfgClientFileName);
                string workdir = GetElement(configinfo, "WorkPath") + "\\Dwginfo\\";
                Stopwatch watchtime = new Stopwatch();
                watchtime.Start();
                AcadApp.Document ceruntdoc = AcadApp.Application.DocumentManager.MdiActiveDocument;
                Database datebase = ceruntdoc.Database;



                List<string> textStyleNamels = new List<string>();//文本样式列表
                List<string> tableNamels = new List<string>();//表格列表列表
                //MessageBox.Show(@"尺寸名：A井道高度，尺寸值：52.5o，参数值类型出现错误，请重新修改。");
                //MessageBox.Show(@"尺寸名：AR1，尺寸值：4000，不满足参数范围：[5000，20000]，请重新修改。");
                List<Diminfos> diminfo = GetDimStyleName(datebase, ceruntdoc);
                List<Textinfos> textNamels = GetMText(ceruntdoc);   //文本列表
                textStyleNamels = GetTextStyleName(datebase);
                tableNamels = GetTableNames(datebase);
                readtabledata(datebase);
                watchtime.Stop();
                string usetime = (watchtime.ElapsedMilliseconds / 1000).ToString() + "s";
                Dwginfo dwginfo = new Dwginfo();
                dwginfo.DimInfos = diminfo;
                dwginfo.TextInfos = textNamels;
                if (!Directory.Exists(workdir))
                {
                    Directory.CreateDirectory(workdir);
                }
                string xmlFile = workdir + Path.GetFileNameWithoutExtension(ceruntdoc.Name) + "_" + DateTime.Now.ToString(CultureInfo.InvariantCulture).Replace("/", "_").Replace(":", "_") + ".xml";
                string JsonFile = workdir + Path.GetFileNameWithoutExtension(ceruntdoc.Name) + "_" + DateTime.Now.ToString(CultureInfo.InvariantCulture).Replace("/", "_").Replace(":", "_") + ".txt";
               // List<Dictionary<string, string>> ListInfo = new List<Dictionary<string, string>>();
               // ListInfo.Add(dwginfo.Diminfo);
               // ListInfo.Add(dwginfo.Txtinfo);
               // DataContractSerializeHelper<List<Dictionary<string, string>>>.PackToFile(xmlFile, ListInfo);
                getJsonByObject(JsonFile, dwginfo);
                //"dwg文件提取成功->用时:{0}"
                MessageBox.Show(string.Format(LanguageHelper.GetString("Msg_DwgFileExtractionSucceededTaketime"), usetime));
            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.ToString());
            }

        }
        private T EcsGetservice<T>(string Url)
        {
            try
            {
                var edpTcp = new EndpointAddress(Url);

                // 创建Binding  
                var tcpBinding = new NetTcpBinding(SecurityMode.None);
                // 创建通道  
                var factory = new ChannelFactory<T>(tcpBinding);
                var channel = factory.CreateChannel(edpTcp);
                return channel;

            }
            catch (Exception e)
            {
                //"订阅者：信息订阅失败!\n{0}"
                string err = string.Format(LanguageHelper.GetString("Exception_InformationSubscriptionFailed"), e.ToString());
                MessageBox.Show(err);
                return default(T);
            }
        }


        private void getJsonByObject(string filename, Object obj)
          {
              //实例化DataContractJsonSerializer对象，需要待序列化的对象类型
             DataContractJsonSerializer serializer = new DataContractJsonSerializer(obj.GetType());
              //实例化一个内存流，用于存放序列化后的数据
              MemoryStream stream = new MemoryStream();
              //使用WriteObject序列化对象
              serializer.WriteObject(stream, obj);
              //写入内存流中
             byte[] dataBytes = new byte[stream.Length];
             stream.Position = 0;
             stream.Read(dataBytes, 0, (int)stream.Length);
             //通过UTF8格式转换为字符串
             string jsoninfo = Encoding.UTF8.GetString(dataBytes);
            File.WriteAllText(filename, jsoninfo);

        }
        private string GetElement(string ServerConfigText, string elementName)
        {
            string Element = null;
            string regexStr = String.Format(@"<{0}>\s*(.*)\s*</{0}>", elementName);
            var re = new Regex(regexStr, RegexOptions.IgnoreCase);
            Match mc = re.Match(ServerConfigText);
            if (mc.Success)
            {
                Element = mc.Groups[1].ToString();
                return Element;
            }

            return string.Empty;
        }

        /// <summary>
        /// 尺寸
        /// </summary>
        /// <param name="db"></param>
        /// <returns></returns>
        public static List<Diminfos> GetDimStyleName(Database db, AcadApp.Document doc)
        {
            List<Diminfos> ls = new List<Diminfos>();
            //  Document doc1 = Application.DocumentManager.MdiActiveDocument;
            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForRead);

                    if (obj is Dimension)
                    {
                        Dimension mt = obj as Dimension;

                        string handle = mt.Handle.Value.ToString();
                        if (mt.DimensionText != null && mt.DimensionText.Contains("="))
                        {
                            List<string> parameterinfo = mt.DimensionText.Split(new char[] { '=' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                            if (parameterinfo.Count() == 2)
                            {
                                Diminfos diminfos = new Diminfos();
                                diminfos.Id = mt.Id;
                                string value = parameterinfo[1].Contains("<>") ? Math.Round((decimal)mt.Measurement, 2, MidpointRounding.AwayFromZero).ToString(CultureInfo.InvariantCulture) : parameterinfo[1].TrimEnd("米".ToCharArray());
                                if (parameterinfo[0].Contains("&"))
                                {
                                    List<string> parametermask = mt.DimensionText.Split(new char[] { '&' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                    for (int i = 0; i < parametermask.Count; i++)
                                    {

                                        if (i == parametermask.Count - 1)
                                        {
                                            diminfos.equipmentid = parametermask[i].Substring(0, 1);
                                            diminfos.parametername = parametermask[i].Substring(1); //可能要做映射
                                            diminfos.parametervalue = value;
                                        }
                                        else
                                        {
                                            diminfos.equipmentid = parametermask[i];
                                            diminfos.parametername = parametermask[parametermask.Count - 1].Substring(1);  //可能要做映射
                                            diminfos.parametervalue = value;
                                        }
                                        ls.Add(diminfos);
                                    }
                                }
                                else
                                {
                                    diminfos.equipmentid = parameterinfo[0].Substring(0, 1);
                                    diminfos.parametername = parameterinfo[0].Substring(1);  //可能要做映射
                                    diminfos.parametervalue = value;
                                    ls.Add(diminfos);
                                }

                        }

                    }

                    }


                }
                trans.Commit();
            }

            return ls;
        }
         [Serializable]
        class Dwginfo
        {
            public List<Diminfos> DimInfos;

            public List<Textinfos> TextInfos;
            public List<Builtdingtableinfos> BuiltdingTableInfos;

            public List<Equipmenttableinfos> EquipmentTableInfos;
        }
        [Serializable]
        public class Diminfos
        {

            public string equipmentid;
            public string parametername;
            public string parametervalue;
            public ObjectId Id { get; set; }
        }
         [Serializable]
        public class Textinfos
        {
            public string equipmentid;
            public string parametername;
            public string parametervalue;
        }
         [Serializable]
        class Builtdingtableinfos
        {

            public string floorNo;
            public string floorHight;
        }
         [Serializable]
        class Equipmenttableinfos
        {

            public string equipmentNo;
            public string factoryNo;
        }

        
        public List<Textinfos> GetMText(AcadApp.Document doc)
        {
            string regexStr = String.Format(@"\s*(.*)\s*@\(\s*(.*)\s*\)");
            var re = new Regex(regexStr, RegexOptions.IgnoreCase);
            List<Textinfos> ls = new List<Textinfos>();
           // Document doc = AcadApp.Application.DocumentManager.MdiActiveDocument;
            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForRead);
                    if (obj is DBText)
                    {
                        DBText mt = obj as DBText;

                        if (mt.TextString.Contains("@("))
                        {
                            Match mc = re.Match(mt.TextString);
                          if (mc.Success)
                            {
                                //ls.Add(mc.Groups[2].ToString(),mc.Groups[1].ToString());
                                if (mc.Groups[1].ToString().Contains(","))
                                {
                                    List<string> textEquipment =
                                        mc.Groups[1].ToString()
                                            .Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                            .ToList();
                                    for (int i = 0; i < textEquipment.Count; i++)
                                    {

                                    }
                                }
                                else
                                {

                                }

                            }

                        }

                    }
                    else if (obj is MText)
                    {
                        MText mt = obj as MText;

                        if (mt.Text.Contains("@("))
                        {
                            Match mc = re.Match(mt.Text);
                            if (mc.Success)
                            {
                               // ls.Add(mc.Groups[2].ToString(), mc.Groups[1].ToString());
                            }

                        }
                    }

                    else if (obj is FeatureControlFrame)
                    {
                        FeatureControlFrame mt = obj as FeatureControlFrame;

                        if (mt.Text.Contains("@("))
                        {
                            Match mc = re.Match(mt.Text);
                            if (mc.Success)
                            {
                               // ls.Add(mc.Groups[2].ToString(), mc.Groups[1].ToString());
                            }



                        }
                    }
                }
                trans.Commit();
                return ls;
            }
        }
        public String TrimMText(string text)
        {
            int found = text.IndexOf("/");
            string newText = text.Substring(found + 1).Replace("/", "").Replace("}", "");
            return newText;
        }
        public static List<string> GetTextStyleName(Database db)
        {
            List<string> ls = new List<string>();
            try
            {
                using (Transaction trans = AcadApp.Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                    TextStyleTable dst = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
                    foreach (ObjectId id in dst)
                    {
                        if (id.GetObject(OpenMode.ForRead).GetRXClass().DxfName == "STYLE")
                        {
                            TextStyleTableRecord dstr = (TextStyleTableRecord)id.GetObject(OpenMode.ForRead);
                            ls.Add(dstr.Name);
                        }
                    }
                }
            }
            catch
            {
            }
            return ls;
        }

        private List<string> GetTableNames(Database db)
        {
            List<string> tableNamels = new List<string>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {

                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForRead) as Table);
                        tableNamels.Add(table.GetTableTitle());
                    }
                }
                return tableNamels;
            }
        }

        private List<List<string>> readtabledata(Database db)
        {
            List<List<string>> tabledataList = new List<List<string>>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr =
                    trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {
                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForWrite) as Table);
                        int rowcount = table.GetRowsCount();
                        int columncount = table.GetColumnsCount();
                        for (int i = 0; i < rowcount - 2; i++)
                        {
                            tabledataList.Add(table.GetRow(i + 2));
                        }
                    }
                }
                return tabledataList;
            }
        }

        ///新建图层-消隐-zld-2018-1024
        ///  
        private ObjectId AddLayer(string name, short colorid, Database db)
        {
            short colorindex = (short)(colorid % 256);
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lr = trans.GetObject(db.LayerTableId, OpenMode.ForWrite, false) as LayerTable;
                ObjectId objid = new ObjectId();
                if (!lr.Has(name))
                {
                    LayerTableRecord layerreRecord = new LayerTableRecord();
                    layerreRecord.Name = name;
                    layerreRecord.Color = Color.FromColorIndex(ColorMethod.ByColor, colorindex);
                    layerreRecord.LinetypeObjectId = getLineId(db); //线性还得加一下
                    layerreRecord.LineWeight = LineWeight.LineWeight005;
                    objid = lr.Add(layerreRecord);
                    trans.AddNewlyCreatedDBObject(layerreRecord, true);

                }
                trans.Commit();
                return objid;
            }
        }

        private ObjectId getLineId(Database db)
        {

            ObjectId lineObjectId = new ObjectId();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LinetypeTable linetable = trans.GetObject(db.LinetypeTableId, OpenMode.ForRead, false) as LinetypeTable;
                if (linetable == null)
                {
                    return  new ObjectId();
                }
                else
                {
                    if (linetable.Has("Dashlines"))
                    {

                        lineObjectId = linetable["Dashlines"];
                    }

                }
              return lineObjectId;
            }
        }

        ///创建面域-消隐-zld-2018-1024
        ///

        private List<Region> CreatRegions(params Curve[] curvelist)
        {
            List<Region> regionlist = new List<Region>();
            DBObjectCollection dbceDbObjectCollection = new DBObjectCollection();
            foreach (var curveitem in curvelist)
            {
                if (curveitem.IsNewObject && curveitem.IsWriteEnabled)
                {
                    return null;
                }
                dbceDbObjectCollection.Add(curveitem);
            }
            try
            {
                DBObjectCollection regions = Region.CreateFromCurves(dbceDbObjectCollection);
                foreach (Region regionitem in regions)
                {
                    regionlist.Add(regionitem);
                }

                return regionlist;
            }
            catch (Exception)
            {
                regionlist.Clear();
                return regionlist;

            }

        }

        /// <summary>
        /// 提取事件
        /// </summary>
        /// <param name="args"></param>
        void taskServer_RunCADCodeEvent(RunCADCodeArgs args)
        {
            String resault = string.Empty;
            var code = args.CADCode;

            if (code.Para2 == CadItemType.propItem + "")
            {
                //var dic = RMI.GetPropertys(iSwApp, code.Para1);
                //SingleQuoteStringParser.Pack(dic, out resault);
            }
            else if (code.Para2 == CadItemType.dimItem + "")
            {
                try
                {
                    //读取模型中的尺寸列表
                    //"读取模型中的尺寸列表,文件名 '{0}'"
                    WriteLog(string.Format(LanguageHelper.GetString("ReadSizeListFileName"), code.Para1));
                    if (!string.IsNullOrEmpty(code.Para1))
                    {
                        DwgInfo dInfo = new DwgInfo(code.Para1);
                        List<string> allZx = dInfo.GetALLZXVariable();
                        resault = Zxtech.CADTaskServer.SingleQuoteStringParser.PackString(allZx);
                    }

                }
                catch (System.Exception ex)
                {

                    WriteLog(ex.ToString(), 2);
                }

            }
            else if (code.Para2 == CadItemType.featureItem + "")
            {
                //读取模型中的特征列表


            }
            else if (code.Para2 == CadItemType.asmComp + "" || code.Para2 == CadItemType.asmCompModelItem + "")
            {


                //读取模型中的零部件列表
                //"读取模型中的插入点列表,文件名 '{0}'"
                WriteLog(string.Format(LanguageHelper.GetString("ReadInserPointListFileName"), code.Para1));
                if (!string.IsNullOrEmpty(code.Para1))
                {
                    DwgInfo dInfo = new DwgInfo(code.Para1);
                    List<string> allZx = dInfo.GetALLZXVariablepoint();
                    resault = Zxtech.CADTaskServer.SingleQuoteStringParser.PackString(allZx);
                }


            }
            else if (code.Para2 == CadItemType.layerItem + "")
            {
                //图层列表
                resault = string.Empty;

            }
            code.Para3 = resault;

        }

        int aa = 0;
        /// <summary>
        /// 驱动事件
        /// </summary>
        /// <param name="args"></param>
        void taskServer_GetCADTask(CADArgs args)
        {
            Palettes.WriteLog(string.Format("taskServer_GetCADTask() start..."));
            if (IsWorking)
            {
                Palettes.WriteLog(string.Format("task is running..."));
                return;
            }

            IsWorking = true;
            try
            {
                //WriteLog("taskServer_GetCADTask() start...");

                WriteLog("taskServer.GetTaskCadCodeList() before...");
                TaskCodes = taskServer.GetTaskCadCodeList();
                //WriteLog("taskServer.GetTaskCadCodeList() after...");
                if (TaskCodes == null)
                {
                    IsWorking = false;

                    //"查找任务信息失败！,无任务或者无CAD命令"
                    WriteLog(LanguageHelper.GetString("FindTaskInfoFailedNoTask"), 2);
                    return;
                }
                else
                {
                    //current task type
                    switch (taskServer.formCADTaskServer.taskInfo.TaskType)
                    {
                        case Zxtech.CADTaskServer.TemplateRunTaskType.DWGToPDF: //dwg -> pdf
                            DwgToPdfTaskCadProcess(TaskCodes);
                            
                            break;
                        default:
                            if (stationInfo.DeleteNoStandardParam)
                            {
                                var jsonPath = Palettes.stationInfo.NostandardJsonPath;
                                NoStandardDimJson = File.ReadAllText(jsonPath);
                            }
                            DefaultTaskCadProcess(TaskCodes);
                            break;
                    }

                    //taskServer.UploadResult(Path.Combine(stationInfo.WorkPath,filePath), taskServer.formCADTaskServer.taskInfo.EpdTaskId);
                    //"任务运行结束！"


                    WriteLog(LanguageHelper.GetString("FinishTask"), 0);
                }
            }
            catch (System.Exception ex)
            {
                IsWorking = false;

                taskServer.SetModelError();
                WriteLog(ex.ToString(), 2);
                if (taskServer.formCADTaskServer.taskInfo != null)
                {
                    string path = taskServer.formCADTaskServer.taskFloderPath;
                    var logFilePath = Path.Combine(path, "CadTaskLog_" + taskServer.formCADTaskServer.taskInfo.EpdTaskId + ".txt");
                    var newPath = Path.Combine(stationInfo.WorkPath, _curTaskFoldName, Path.GetFileName(logFilePath));
                    File.Copy(logFilePath, newPath);

                }
                throw ex;
            }
            finally
            {                
                //Application.MainWindow.Visible = true;                
            }
        }

        public static void EndCadCurrentTask()
        {
            if (taskServer.formCADTaskServer != null)
                if (taskServer.formCADTaskServer.taskInfo != null)
                    taskServer.SetTaskRunEnd(taskServer.formCADTaskServer.taskInfo.EpdTaskId);
        }

        private static void DwgToPdfTaskCadProcess(List<CADTaskCode> ls)
        {
            var taskInfo = taskServer.formCADTaskServer.taskInfo;
            DwgToPdfTaskProcess.Process(taskInfo.EpdTaskId, taskInfo.CallbackUrl, ls);

            //WriteLog(LanguageHelper.GetString("FinishTask"), 0);
            EndCadCurrentTask();
        }


        private static void DefaultTaskCadProcess(List<CADTaskCode> ls)
        {
            DocumentMgr.ColseAll();
            _curTaskMergeFileName = string.Empty;
            Palettes.ClearHoldOnFiles();
            //WriteLog("yes, it's get tasks succ...");
            ls = ls.OrderBy(o => o.SortId).ToList();

            //生成目录中创建任务文件夹
            _curTaskFoldName = ls.FirstOrDefault(p => p.SortId == 1).Para3;
            if (string.IsNullOrEmpty(_curTaskFoldName))
            {
                //
                WriteLog("TaskFoldName:" + _curTaskFoldName+ "is empty");
            }
            else
            {
                if (!_curTaskFoldName.StartsWith("ETP00000"))//如果是以租户开头，例如菱王就是要生成到租户下的子文件夹，那就不能把斜杠替换为_,
                {
                    _curTaskFoldName = _curTaskFoldName.Replace('\\', '_').Replace('/', '_');
                }
            }
            WriteLog(string.Format(string.Format(LanguageHelper.GetString("TaskNo"), _curTaskFoldName)));
            var subTaskTypes = taskServer.formCADTaskServer.subTaskTypes;
            if (subTaskTypes != null && subTaskTypes.Count > 0 && subTaskTypes.Contains(taskServer.formCADTaskServer.taskInfo.TaskType))
            {
                var dirPath = Path.Combine(stationInfo.WorkPath, _curTaskFoldName);

                if (!Directory.Exists(dirPath)) 
                {
                    WriteLog(string.Format(string.Format(LanguageHelper.GetString("NotExist") + dirPath)));
                    return;
                }
                else
                {
                    var filePaths = Directory.GetFiles(dirPath, "*.*", SearchOption.AllDirectories).ToList();
                    
                    Palettes.AddHoldOnFiles(null, filePaths);
                }
            }
            else
            {
                CreateDestFolds(_curTaskFoldName);
            }
            
            

            //下载缓存中的模型,是否使用cache信息加到info中
#if DEBUG
            WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，整理命令开始",1);
#endif
            CADCommandCache current = null;
            foreach (var command in TaskCodes)
            {
                if (command.CadCodeId == 1200)
                {
                    var tempCmd = new CADCommandCache() { InitCommand = command, Parent = current, Children = new List<CADCommandCache>() };
                    totalCommands.Add(tempCmd);
                    if (current != null)
                    {
                        current.Children.Add(tempCmd);
                    }
                    current = tempCmd;
                   
                }
                else if (command.CadCodeId == 1300)
                {
                    if (current != null)
                    {
                        current.DriveCommand = command;
                        current = current.Parent;
                    }
                }
            }
            ls = taskServer.GetCacheFiles(ls, taskServer.formCADTaskServer.taskInfo.EpdTaskId, totalCommands, stationInfo, _curTaskFoldName);
            
            //taskServer.GetModelFiles(ls, taskServer.formCADTaskServer.taskInfo.EpdTaskId, stationInfo.PdsPath);
            List<List<CADTaskCode>> asslist = Node.DivisionList(ls);
#if DEBUG
            WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，整理命令结束", 1);
#endif
            if (asslist.Count == 0)
            {
                //"输出命令为空，退出任务！"
                WriteLog(LanguageHelper.GetString("OutputCommandEmptyExitTask"));

                IsWorking = false;
                return;
            }

            //每个List<CADTaskCode>是一组大命令, 中间会嵌套一些小命令, 即内层的1200.1300组合,最后的保留文件都是最外层的
            //是否输出pdf是根据配置文件来的, 是否输出dxf是根据解析到的结果, 解析结果是来自数据库中的para
            //dwg与pdf可能会因为文件名一样覆盖之前的同名的文件,使用并集union去掉重复的文件名称
            List<string> forecastFiles = new List<string>();
            foreach (var list in asslist)
            {
                if (list[0].Files.Count > 0) forecastFiles = forecastFiles.Union(list[0].Files).ToList();
            }
            WriteLog(string.Format("It is estimated that {0} files will be generated.", forecastFiles.Count));
            //Application.DocumentManager.CloseAll();
            
            string filePath = ls.FirstOrDefault(p => p.SortId == 1).Para3;
            string combineFileName = string.Empty;
            //"任务号：{0}"
            WriteLog(string.Format(LanguageHelper.GetString("TaskNo"), filePath));

            _listTaskCodes = asslist;
            

            _assListIndex = -1;
            _assListCount = asslist.Count;

            var result = asslist.SelectMany(innerList => innerList).Where(item =>
            item.CadCodeId == 1300 &&
            item.Para1 != null &&
            item.Para1.IndexOf("'DWG-TXT@载重'", System.StringComparison.OrdinalIgnoreCase) >= 0)
            .ToList();
            foreach (var code in result)
            {
                Match match = Regex.Match(code.Para1, @"'DWG-TXT@载重','([^']*)'");
                if (match.Success)
                {
                    CarryingCapacity = double.Parse(match.Groups[1].Value); 
                    
                }
            }
            ProcessNextPartTask();

        }


        public static void ProcessNextPartTask()
        {
            if (stationInfo == null) return;
            Palettes.WriteLog(string.Format("_assListIndex：{0}", _assListIndex + 1));
            if(++_assListIndex >= _assListCount)
            {
                //"合并文件"
                WriteLog(LanguageHelper.GetString("MergeFiles"));
                CombineFiles(stationInfo.WorkPath, _curTaskFoldName, _curTaskMergeFileName);
                List<CADTaskCode> jssTaskCodes = new List<CADTaskCode>();
                foreach (var item in _listTaskCodes)
                {
                    jssTaskCodes.AddRange(item.Where(o => o.CadCodeId == 1200).OrderBy(o => o.SortId).ToList());
                }
                JavaScriptSerializer jss = new JavaScriptSerializer() { MaxJsonLength = Int32.MaxValue };
                var json = jss.Serialize(jssTaskCodes);
                File.WriteAllText(stationInfo.WorkPath + "\\" + _curTaskFoldName + "\\" + "Result.json", json, Encoding.UTF8);
                AddHoldOnFiles(stationInfo.WorkPath + "\\" + _curTaskFoldName + "\\" + "Result.json");

                //per 完成状态是1,即没有完成的, 表示没有找到文件或者文件打开错误, 这种taskcode的files如果有值, 那files列表的count数量就是实际比预计少生成的文件数量
                var notFinishedCodes = jssTaskCodes.Where(o => o.Finished == FinishStatus.NotFinished && o.Files.Count > 0).ToList();
                int notFinishedFilesCount = 0;
                string notFinishedFilesPartid = string.Empty;
                foreach (var code in notFinishedCodes)
                {
                    notFinishedFilesCount += code.Files.Count;
                    notFinishedFilesPartid += code.PartId + ";";
                }
                WriteLog(string.Format("Generated failed count:{0},PartId {1}", notFinishedFilesCount, notFinishedFilesPartid), 0);
               
                //per 需要增加一下哪些是不使用缓存但是有错误信息的结果, 这些结果是不需要再上传的
                List<int> UseCachePartIds = new List<int>();
                List<string> uploadCacheFiles = new List<string>();
                foreach (var code in jssTaskCodes)
                {
                    if ((!code.UseCache.HasValue || !code.UseCache.Value) && code.Finished == FinishStatus.Finished)
                    {
                        UseCachePartIds.Add(code.PartId);
                    }
                }
                
                if (stationInfo.UseModelCache)
                {
                    WriteLog(string.Format("Upload cache model count:{0}...", UseCachePartIds.Count), 0);
                    foreach (var totalCommand in totalCommands)
                    {
                        if (UseCachePartIds.Contains(totalCommand.InitCommand.PartId))
                        {
                            var files = totalCommand.DriveCommand.Files.Union(totalCommand.TotalChildren().SelectMany(o => o.DriveCommand.Files)).ToList();
                            taskServer.formCADTaskServer.SaveDocument(totalCommand.InitCommand.Para3, totalCommand.InitCommand.DocumentId.ToString(), totalCommand.InitCommand.ModelMd5, taskServer.formCADTaskServer.taskInfo.EpdTaskId, files, stationInfo.WorkPath);
                            WriteLog(string.Format("Uploaded cache model {0} id:{1} md5:{2}", totalCommand.InitCommand.Para3, totalCommand.InitCommand.DocumentId, totalCommand.InitCommand.ModelMd5), 0);
                        }
                    }
                }
                //清理多余零件
                WriteLog(LanguageHelper.GetString("DeleteRedundantParts"));
                var dels = DletePartFiles(stationInfo.WorkPath, _curTaskFoldName);

                Palettes.ClearHoldOnFiles();
                if (stationInfo.KeepOpenLastDoc)
                {
                    DocumentMgr.ColseAllExceptCurrent();
                }
                else
                {
                    DocumentMgr.ColseAll();
                }
                DeleteUnusedFiles(dels, _curTaskFoldName);
                taskServer.UpdateTaskPartPropWithModel(taskServer.formCADTaskServer.taskInfo.EpdTaskId, new List<CADTaskPropFromModel>(), (taskServer.formCADTaskServer.taskInfo != null && taskServer.formCADTaskServer.taskInfo.EpdTaskId.ToByteArray().Count() > 0)
                                ? taskServer.formCADTaskServer.taskInfo.EpdTaskId
                                : Guid.Empty, _curTaskFoldName);

                if (taskServer.formCADTaskServer.taskInfo.TaskType == Zxtech.CADTaskServer.TemplateRunTaskType.PocInsertDwg)
                {
                    var pocResultFileSourceFile = Path.Combine(stationInfo.WorkPath, _curTaskFoldName, "main.dwg");
                    var pocResultFileTargetFile = Path.Combine(stationInfo.PocResultPath, taskServer.formCADTaskServer.taskInfo.EpdTaskId.ToString()+".dwg");
                    if (File.Exists(pocResultFileSourceFile))
                    {
                        if (!Directory.Exists(stationInfo.PocResultPath))
                        {
                            Directory.CreateDirectory(stationInfo.PocResultPath);
                            
                        }
                        File.Copy(pocResultFileSourceFile, pocResultFileTargetFile,true);
                        WriteLog(string.Format("Copy file from {0} to {1}", pocResultFileSourceFile, pocResultFileTargetFile));
                    }
                    else
                    {
                        WriteLog(string.Format("File {0} does not exist", pocResultFileSourceFile));
                    }
                    
                }
                //WriteLog(LanguageHelper.GetString("FinishTask"), 0);
                IsWorking = false;
                EndCadCurrentTask();
                if (jssTaskCodes.Any(o => o.ErrorInfo !=null))
                {
                    taskServer.formCADTaskServer.SetModelError(TemplateRunTaskState.CadWsCompleteButExitError);
                }
                return;
            }

            //任务实例化
            //TaskMgr t = new TaskMgr();
            //"XML格式化处理开始...."
#if DEBUG
            WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，XML格式化处理开始", 1);
#endif
            WriteLog(LanguageHelper.GetString("StartXMLFormat"), 0);
            var node = Node.CreateNode(_listTaskCodes[_assListIndex], stationInfo.PdsPath, stationInfo.bMakeDXF);

            if (string.IsNullOrWhiteSpace(_curTaskMergeFileName))
            {

                _curTaskMergeFileName = node.GetCombineFileName();
                WriteLog("_curTaskMergeFileName=" + _curTaskMergeFileName, 0);
            }

            //"XML格式化处理完成...."
            WriteLog(LanguageHelper.GetString("FinishXMLFormat"), 0);
#if DEBUG
            WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，XML格式化处理结束", 1);
#endif
            XDocument xdoc = new XDocument();
            using (StringWriter xml = new StringWriter())
            {
                try
                {
                    XmlSerializer xz = new XmlSerializer(node.GetType());
                    xz.Serialize(xml, node);
                    xdoc = XDocument.Parse(xml.ToString());
                }
                catch (Autodesk.AutoCAD.Runtime.Exception e1)
                {
                    Palettes.WriteLog(e1.Message, 2);
                    Palettes.WriteLog(e1.StackTrace, 2);
                    throw e1;
                }
                catch (Exception ex)
                {
                    Palettes.WriteLog(ex.Message, 2);
                    Palettes.WriteLog(ex.StackTrace, 2);
                    throw ex;
                }
            }

            //"任务运行中！"
            WriteLog(LanguageHelper.GetString("TaskRunning"), 0);
            WriteLog(xdoc.ToString());

            using (WSCommand runTask = new WSCommand())
            {
                runTask.RTEXT(xdoc, stationInfo.PdsPath, stationInfo.IsForceCutline, stationInfo.WorkPath, _curTaskFoldName);
            }
        }

        private static void DeleteUnusedFiles(List<string> dels, string filePath)
        {
            if (IniConfigData.IsHoldOnUnusedFiles || dels == null) return;

            WriteLog(string.Format("wait delete count: {0}.", dels.Count));
            var dirInfo = new DirectoryInfo(stationInfo.WorkPath + "\\" + filePath);

            int loopCount = 10, loopId = 0;

            bool isSucc = false, noSel = false;
            while (!isSucc && dels.Count > 0)
            {
                ++loopId;
                var files = dirInfo.GetFiles("*.*", SearchOption.AllDirectories).ToList();

                noSel = false;
                foreach (var item in files)
                {
                    if (dels.FirstOrDefault(p => p.ToLower() == item.FullName.ToLower()) != null)
                    {
                        noSel = true;

                        try
                        {
                            File.Delete(item.FullName);
                            if (File.Exists(item.FullName))
                            {
                                WriteLog(string.Format("delete file '{0}' failed at 2th.", item.FullName));
                            }
                            else
                            {
                                //WriteLog(string.Format("delete file '{0}' succ at 2th.", item.FullName));
                            }
                        }
                        catch (Exception ex)
                        {
                            WriteLog(ex.ToString());
                            throw ex;
                        }
                    }
                }

                isSucc = !noSel;
                if (!isSucc && loopId > loopCount)
                {
                    WriteLog(string.Format("loop delete files failed in count {0}.", loopCount));
                    isSucc = true;
                }
            }

            WriteLog("delete files complete.");
        }

        private static void CreateDestFolds(string filePath)
        {
            var dirPath = Path.Combine(stationInfo.WorkPath, filePath);
            try
            {
                if (Directory.Exists(dirPath)) Directory.Delete(dirPath, true);
                Directory.CreateDirectory(dirPath);
            }
            catch (System.Exception ex1)
            {
                Palettes.WriteLog(string.Format("create/delete path '{0}' failed.", dirPath), 2);
                Palettes.WriteLog(ex1.ToString(), 2);
                throw ex1;
            }
        }
        private T GetService<T>(string path)
        {
            var binding = new NetTcpBinding();
            binding.Security.Mode = SecurityMode.None;


            binding.ReaderQuotas = new XmlDictionaryReaderQuotas
            {
                MaxArrayLength = Convert.ToInt32(81920),
                MaxDepth = Convert.ToInt32(163840),
                MaxBytesPerRead = Convert.ToInt32(640),
                MaxNameTableCharCount =
                    Convert.ToInt32(163840),
                MaxStringContentLength =
                    Convert.ToInt32(81920)
            };
            binding.TransferMode = TransferMode.Streamed;
            binding.MaxReceivedMessageSize = 655360000;
            binding.CloseTimeout = new TimeSpan(0, 10, 0);
            binding.OpenTimeout = new TimeSpan(0, 10, 0);
            binding.ReceiveTimeout = new TimeSpan(0, 10, 0);
            ServiceEndpoint httpEndpoint;
            httpEndpoint = new ServiceEndpoint(ContractDescription.GetContract(typeof(T)),
                                               binding, new EndpointAddress(path));

            ChannelFactory<T> factory;
            factory = new ChannelFactory<T>(httpEndpoint);


            return factory.CreateChannel();
        }

        private static void CombineFiles(string WorkPath, string filePath, string combineDestFileName)
        {
            IList<string> outputFiles = new List<string>();
            WSCommand.StopPrccess();
            string path = WorkPath + "\\" + filePath;
            
            if (taskServer.formCADTaskServer.taskInfo!=null && !string.IsNullOrEmpty(taskServer.formCADTaskServer.taskInfo.CADWsId))
            {
                return;
            }
            
            if (Directory.Exists(path))
            {
                const string pdfExt = ".pdf", dwgExt = ".dwg";
                if (string.IsNullOrWhiteSpace(combineDestFileName)) combineDestFileName = "main";
                                
                if (Palettes.IniConfigData.IsMergeDwgFiles)
                {
                    string outDwg = Path.Combine(path, combineDestFileName);
                    if (Path.GetExtension(outDwg).ToLower() != dwgExt) outDwg += dwgExt;

                    var dwgFiles = new List<string>();
                    _holdOnFiles.ForEach(p => {
                        if (Path.GetExtension(p) == dwgExt && !_noMergeFiles.Contains(p))
                        {
                            dwgFiles.Add(p);
                        }
                    });
                    if (dwgFiles.Count > 1)
                    {
#if DEBUG
                        Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件开始", 1);
#endif
                        WriteLog("outDwgFile: " + outDwg);
                        DwgCombiner dwgc = new DwgCombiner(Palettes.IniConfigData.GapDistance > 0 ? Palettes.IniConfigData.GapDistance : 200, true, false);
                        dwgc.MergeDwgFiles(dwgFiles, outDwg);
                        WriteLog("MergeDwgFiles is complete.");
#if DEBUG
                        Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件结束", 1);
#endif
                    }
                    else if (dwgFiles.Count > 0)
                    {
                        WriteLog("outDwgFile: " + outDwg);
                        if (dwgFiles[0].ToLower() != outDwg.ToLower()) File.Move(dwgFiles[0], outDwg);
                        WriteLog("MergeDwgFiles is complete.");
                    }
                    else
                    {
                        WriteLog("Not any dwg file will be merged.");
                    }
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，删除不上传文件开始", 1);
#endif
                    Palettes.DeleteFixExtendFiles(dwgExt);
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，删除不上传文件结束", 1);
#endif

                    Palettes.AddHoldOnFiles(outDwg);
                    


                }

                Thread.Sleep(1000);

                //List<string> pdfs = Directory.GetFiles(path, "*.pdf").ToList();
                //if (pdfs.Count > 1)
                //{
                if (Palettes.stationInfo!=null && Palettes.stationInfo.bMakePDF && Palettes.IniConfigData.IsMergePdfFiles)
                    {
                        var pdfFiles = new List<string>();
                        _holdOnFiles.ForEach(p => {
                        if (Path.GetExtension(p) == pdfExt && !_noMergeFiles.Contains(p))
                        {
                            pdfFiles.Add(p);
                        }
                    });


                        string outPdf = Path.Combine(path, combineDestFileName);
                        if (Path.GetExtension(outPdf).ToLower() != pdfExt) outPdf += pdfExt;

                        PDFCombiner pdfc = new PDFCombiner();
                        pdfc.MergePdfFiles(pdfFiles.ToList(), outPdf);
                        WriteLog("MergePdfFiles is complete.");

                        Palettes.DeleteFixExtendFiles(pdfExt);
                        Palettes.AddHoldOnFiles(outPdf);
                    }
                //}

                Palettes.AddHoldOnFiles(null, _noMergeFiles);
            }
            // PDFCombiner
        }

        /// <summary>
        /// 比较二个字符串，查找出相同字数和差异字符
        /// </summary>
        /// <param name="s1"></param>
        /// <param name="s2"></param>
        /// <returns></returns>
        public string sCompare(string s1, string s2, ref List<string> outstring)
        {
            string samestring = "";
            int count = 0;/*相同字符个数*/
            int n = s1.Length > s2.Length ? s2.Length : s1.Length;/*获得较短的字符串的长度*/
            for (int i = 0; i < n; i++)
            {
                if (s1.Substring(i, 1) == s2.Substring(i, 1))
                /*同位置字符是否相同*/
                {
                    count++;
                    samestring += s1.Substring(i, 1);

                }
                else
                {
                   break; //MessageBox.Show("s1:" + s1.Substring(i, 1) + "| s2:" + s2.Substring(i, 1));
                }
            }
            outstring.Add(samestring);
            return samestring;
        }
        /// <summary>
        /// 删除多余文件
        /// </summary>
        /// <param name="WorkPath">工作目录</param>
        /// <param name="filePath">新文件夹</param>
        private static List<string> DletePartFiles(string WorkPath, string filePath)
        {

            Palettes.WriteLog("////////////////////////////////////////////////////////////////////");
            Palettes.WriteLog("holdFile:");
            foreach (var f in _holdOnFiles)
            {
                Palettes.WriteLog(string.Format("file： {0}.", f));
            }
            Palettes.WriteLog("////////////////////////////////////////////////////////////////////");

            //20230114,per,有dxf生成的任务中也有要删除的子件的dwg文件, 就把这里注释了
            //if (!string.IsNullOrEmpty(taskServer.formCADTaskServer.taskInfo.CADWsId))
            //{
            //    return null;
            //}
            //per


            #region old code
            /*string[] dwgFiles = new string[] {};
            string path = WorkPath + "\\" + filePath;
            if (Directory.Exists(path))
            {
                List<string> pdfFiles = Directory.GetFiles(path, "*.pdf").ToList();
                if (pdfFiles.Count>0)
                {
                    dwgFiles = pdfFiles.Select(p => p.Replace(".pdf", ".dwg")).ToArray();
                }

                if (IsMergeDwgFiles)
                {
                    var ls = new List<string>(dwgFiles);

                    string mergeFileName = "main.dwg";
                    if (!string.IsNullOrWhiteSpace(MergeDwgFileName))
                    {
                        mergeFileName = MergeDwgFileName;
                        if (!mergeFileName.ToLower().Contains(".dwg")) mergeFileName += ".dwg";
                    }
                    ls.Add(Path.Combine(path, mergeFileName));

                    dwgFiles = ls.ToArray();
                }
            }
            if (combinFiles == null || combinFiles.Count == 0)
            {
                List<string> DwgFiles = Directory.GetFiles(path, "*.dwg").ToList();
                foreach (string dwgFileitem in DwgFiles)
                {
                    if (!dwgFiles.Contains(dwgFileitem))
                    {
                        File.Delete(dwgFileitem);
                        WriteLog("delete file:" + dwgFileitem);
                    }
                }
            }*/
            #endregion

            List<string> delFiles = new List<string>();
            if (IniConfigData.IsHoldOnUnusedFiles) return delFiles;


            var fs = Directory.GetFiles(WorkPath + "\\" + filePath).ToList();
            foreach (var f in fs)
            {
                var lf = f.ToLower();

                var ext = Path.GetExtension(lf);
                if (_constNoDeleteExts.Exists(p => p == ext)) continue;

                if (null == _holdOnFiles.FirstOrDefault(p => p.ToLower() == lf) && File.Exists(lf))
                {
                    try
                    {
                        delFiles.Add(lf);

                        File.Delete(lf);
                        Palettes.WriteLog(string.Format("delete file： {0}.", lf));
                    }
                    catch (Exception ex)
                    {
                        Palettes.WriteLog(ex.Message + "\n" + ex.StackTrace, 2);
                        throw ex;
                    }
                }
            }
            return delFiles;
        }

        /// <summary>
        /// 数据库写入
        /// </summary>
        ////上传数据库
        //void UpdateTaskPartPropWithModel(int taskId, List<Zxtech.EdisService.Contract.TaskPropFromModel> listTaskPropFromModel, Guid epdTaskId)
        //{
        //    //List<Zxtech.EdisService.Contract.TaskPropFromModel> listTaskPropFromModel = new List<Zxtech.EdisService.Contract.TaskPropFromModel>();
        //    FormCADTaskServer formCADTask =new FormCADTaskServer();
        //    formCADTask.taskInfo = 
        //    //Guid EpdPartGuid = (Guid)ls[1].EpdPartGuid;
        //    bool lol = formCADTask.UpdateTaskPartPropWithModel(taskId, listTaskPropFromModel, epdTaskId);
        //}
        /// <summary>
        /// CAD运行日志
        /// </summary>
        /// +++++++++++++++++++++++
        /// 
        /// 
        /// <param name="msg"></param>
        /// <param name="type">0、信息；1、警告；2、错误；</param>

        public static void WriteLog(string msg, int type = 0)
        {
            //  Autodesk.AutoCAD.ApplicationServices.Core.Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage(msg+"\r\n");
            if (taskServer == null) 
            {
                if (File.Exists("D:\\CADWorkDir_AutoCAD\\testTask\\log.txt"))
                {
                    File.AppendAllText("D:\\CADWorkDir_AutoCAD\\testTask\\log.txt", msg + Environment.NewLine);
                    return;
                }
                return;
            }
                
            Guid taskID = new Guid();
            if (taskServer.formCADTaskServer.taskInfo != null)
            {
                taskID = taskServer.formCADTaskServer.taskInfo.EpdTaskId;
            }
            taskServer.SendLogMessage(taskID, Thread.CurrentThread.ManagedThreadId + " " + msg, type);
        }

        void frm_RTEXTHandler(TaskMgr t, XDocument xdoc)
        {
            WSCommand runTask = new WSCommand();
           // runTask.RTEXT( xdoc, stationInfo.PdsPath, stationInfo.WorkPath,0);
        }

        string frm_GetModelData(string filePath)
        {
            WSCommand GetModleData = new WSCommand();
            return GetModleData.GetModleData(filePath);
        }
        //internal static PaletteSet ps = null;
        static UCTreeView treeControl = null;
        static UCDockDrag dockControl = null;

    //    [CommandMethod("ShowPalette")]
    //    public void ShowPalette()
    //    {
    //        if (ps == null)//如果面板没有被创建
    //        {
    //            //新建一个面板对象，标题为"工作空间"
    //            ps = new PaletteSet("工作空间", typeof(Palettes).GUID);
    //            //添加标题为“属性点管理”的面板项（树形列表）
    //            treeControl = new UCTreeView();
    //            ps.Add("属性点管理", treeControl);
    //            //添加标题为“停靠及拖放”的面板项（组合框及文本框）
    //            dockControl = new UCDockDrag();
    //            ps.Add("停靠及拖放", dockControl);
    //            //添加文档打开事件
    //            AcadApp.DocumentManager.DocumentCreated += new DocumentCollectionEventHandler(DocumentManager_DocumentCreated);
    //            //添加文档关闭事件
    //            AcadApp.DocumentManager.DocumentDestroyed += new DocumentDestroyedEventHandler(DocumentManager_DocumentDestroyed);
    //            //添加面板装载事件，用于获取用户数据
    //            ps.Load += new PalettePersistEventHandler(ps_Load);
    //            //添加面板保存事件，在AutoCAD关闭时，保存用户数据
    //            ps.Save += new PalettePersistEventHandler(ps_Save);
    //        }
    //        ps.Visible = true;//面板可见
    //        updateTree();//填充树形列表
    //    }
    //    void ps_Save(object sender, PalettePersistEventArgs e)
    //    {
    //        //将文本框中的文本保存到配置文件中
    //        e.ConfigurationSection.WriteProperty("Drag", dockControl.textBoxDrag.Text);
    //    }
    //    void ps_Load(object sender, PalettePersistEventArgs e)
    //    {
    //        //如果配置文件中包含指定的用户数据，则将文本框内容设置为用户数据
    //        if (e.ConfigurationSection.Contains("Drag"))
    //            dockControl.textBoxDrag.Text = e.ConfigurationSection.ReadProperty("Drag", "test").ToString();
    //    }
    //    void DocumentManager_DocumentDestroyed(object sender, DocumentDestroyedEventArgs e)
    //    {
    //        updateTree();//有文档关闭，须重新更新树形列表
    //    }
    //    void DocumentManager_DocumentCreated(object sender, DocumentCollectionEventArgs e)
    //    {
    //        updateTree();//有文档打开，须重新更新树形列表
    //    }
    //    private static void updateTree()
    //    {
    //        //清空树形列表中的内容
    //        treeControl.treeViewEnts.Nodes.Clear();
    //        //遍历AutoCAD中的文档
    //        foreach (Document doc in AcadApp.DocumentManager)
    //        {
    //            ////TreeNode nodeDoor = new TreeNode("门");//门节点
    //            ////nodeDoor.ImageIndex = 1;//门节点图标
    //            //nodeDoor.SelectedImageIndex = 1;//门节点选中后的图标
    //            TreeNode nodeWindow = new TreeNode("点");//窗节点
    //            nodeWindow.ImageIndex = 2;//窗节点图标
    //            nodeWindow.SelectedImageIndex = 2;//窗节点选中后的图标
    //            TreeNode[] nodes = new TreeNode[] { nodeWindow };
    //            //定义一个以文档名称为标题的节点，该节点包含两个子节点：门、窗
    //            TreeNode nodeDoc = new TreeNode(doc.Name, nodes);
    //            nodeDoc.ImageIndex = 0;//文档节点图标
    //            //将文档节点添加到树形列表控件中
    //            treeControl.treeViewEnts.Nodes.Add(nodeDoc);
    //        }
    //    }
    //}
    //public class MyDropTarget : DropTarget
    //{
    //    static string dropText;//拖放文本
    //    Database db = HostApplicationServices.WorkingDatabase;
    //    Document doc = AcadApp.DocumentManager.MdiActiveDocument;
    //    public override void OnDrop(DragEventArgs e)
    //    {
    //        //如果拖放的是字符串对象
    //        if (e.Data.GetDataPresent(typeof(string)))
    //        {
    //            //获取拖放的字符串
    //            dropText = (string)e.Data.GetData(typeof(string));
    //            //调用命令，在当前鼠标位置拖放的字符串
    //            string cmd = string.Format("Drop\n{0},{1},0\n", e.X, e.Y);
    //            doc.SendStringToExecute(cmd, false, false, false);
    //        }
    //    }
    //    [CommandMethod("Drop")]
    //    public void Drop()
    //    {
    //        Editor ed = doc.Editor;
    //        if (dropText != null)//如果有拖放文本
    //        {
    //            //提示输入文本要放置的位置
    //            PromptPointOptions opt = new PromptPointOptions("请输入文本放置的位置");
    //            PromptPointResult ppr = ed.GetPoint(opt);
    //            if (ppr.Status != PromptStatus.OK) return;
    //            Point3d pos = ppr.Value;//获取输入的位置（拖放操作的鼠标位置，为屏幕坐标）
    //            using (Transaction trans = db.TransactionManager.StartTransaction())
    //            {
    //                DBText txt = new DBText();//新建文本对象
    //                txt.TextString = dropText;//设置文本内容为拖放文本
    //                //将屏幕坐标转换为AutoCAD的点坐标，再将该坐标值设置为文本的位置
    //                txt.Position = ed.PointToWorld(new Point((int)pos.X, (int)pos.Y));
    //                db.AddToModelSpace(txt);//将文本添加到模型空间
    //                trans.Commit();
    //            }
    //            dropText = null;
    //        }
    //    }
    }
}
