﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;

using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Geometry;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;


namespace CADWSAddin
{
    public class DwgCombiner
    {
        float _gapDistance = 200;
        bool Explode = false;
        bool _isVertOri = false;

        Document _doc = null;
        string _outMergeFile = string.Empty;

        public DwgCombiner(float distance = 200, bool exp = false, bool isVertOri = false)
        {
            _gapDistance = distance;
            Explode = exp;
            _isVertOri = isVertOri;
        }

        public bool Exec(string[] dwgFiles, string outFile)
        {
            Document doc = Application.DocumentManager.Add("");

            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction tran = doc.TransactionManager.StartTransaction())
                {
                    Point3d pt = new Point3d();
                     BlockTable bt = tran.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                     BlockTableRecord btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    double offset = 0;
                    for (int i = 0; i < dwgFiles.Length; i++)
                    {
                        Database blkDb = Utils.LoadWBlock(dwgFiles[i]);
                        string blkName = Path.GetFileNameWithoutExtension(dwgFiles[i]);
                        ObjectId blkObjId = doc.Database.Insert(blkName, blkDb, false);
                        BlockReference blkRef = new BlockReference(pt, blkObjId);
                        blkObjId = btr.AppendEntity(blkRef);
                        tran.AddNewlyCreatedDBObject(blkRef, true);

                        //if (Explode)
                        //{
                        //    blkRef.ExplodeToOwnerSpace();
                        //}
                                                
                        DrawingSpace ds = Utils.GetDrawingSpace(blkDb);
                        
                        if (i>0)
                        {
                            offset = offset + ds.Width + _gapDistance;
                        }
                        //"基础距离：{0}偏移距离： {1}"
                        //Palettes.WriteLog(string.Format(LanguageHelper.GetString("BaseDistanceOffsetDistance"), pt.X, offset)); 
                        double xoffset = pt.X + offset;
                        pt = new Point3d(xoffset, pt.Y/* - heightGap - ds.Heigth*/, 0);
                        blkRef.Position = pt;
                        // pt = new Point3d(pt.X + heightGap + ds.Width, pt.Y, 0);
                    }

                    tran.Commit();
                }
                
            }

            if (!string.IsNullOrEmpty(outFile))
            {
                Utils.SaveDocument(outFile);
            }

            return true;
        }


        public void T2()
        {

            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction tran = doc.TransactionManager.StartTransaction())
                {                    
                    BlockTable bt = tran.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                   ObjectIdCollection ids =   btr.GetBlockReferenceIds(true,true);

                    foreach (ObjectId BlcokId in btr)
                    {
                        Entity BlockEntity = tran.GetObject(BlcokId, OpenMode.ForRead) as Entity;

                        if (BlockEntity is Autodesk.AutoCAD.DatabaseServices.BlockReference)
                        {
                            BlockReference bk = (BlockReference)BlockEntity;
                            //将块名加到List中
                                                      
                            
                            if (bk.IsErased)
                            {
                                bk.Erase();
                            }

                            int kk = 0;
                        }
                    }
                    

                  
                    tran.Commit();
                }

            }
        }

        /// <summary>
        /// 合成dwg文件
        /// </summary>
        /// <param name="fileList">文件名list</param>
        /// <param name="outMergeFile">输出路径</param>
        public void MergeDwgFiles(List<string> fileList, string outMergeFile)
        {
            _outMergeFile = outMergeFile;

            if (fileList == null) return;
            //fileList = fileList.OrderBy(p => p).ToList();
                       
            var dir = System.IO.Path.GetDirectoryName(this.GetType().Assembly.CodeBase.Replace("file:///", ""));
            var tempFile = Path.Combine(dir, "MergeTemplate\\Template1.dwg");
            try
            {
                if (File.Exists(tempFile))
                {
                    _doc = Application.DocumentManager.Open(tempFile);
                }
                else
                {
                    _doc = Application.DocumentManager.Add("");
                }
            }
            catch (Exception)
            {

                _doc = Application.DocumentManager.Add("");
            }
            

            using (DocumentLock doclck = _doc.LockDocument())
            {
                using (Transaction tran = _doc.TransactionManager.StartTransaction())
                {
                    Point3d pt = new Point3d();
                    BlockTable bt = tran.GetObject(_doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    double offset = 0;
                    for (int i = 0; i < fileList.Count; i++)
                    {
                        Database blkDb = Utils.LoadWBlock(fileList[i]);
                        DrawingSpace ds = Utils.GetDrawingSpace(blkDb);
                        //"基础距离：{0}偏移距离： {1}"
                        //Palettes.WriteLog(string.Format(LanguageHelper.GetString("BaseDistanceOffsetDistance"), (_isVertOri ? pt.Y : pt.X), offset));
                        if (_isVertOri)
                        {
                            pt = new Point3d(-ds.LeftTop.X, -offset - ds.LeftTop.Y, 0);
                        }
                        else
                        {
                            pt = new Point3d(offset - ds.LeftTop.X, ds.Heigth - ds.LeftTop.Y, 0);
                        }
                        Palettes.WriteLog(string.Format("Merge dwg file：'{0}'", fileList[i]));
                        string blkName = Path.GetFileNameWithoutExtension(fileList[i]);
                        ObjectId blkObjId = _doc.Database.Insert(blkName, blkDb, false);
                        BlockReference blkRef = new BlockReference(pt, blkObjId);
                        //blkRef.Position = pt;
                        ObjectId blkAppendObjId = btr.AppendEntity(blkRef);
                        tran.AddNewlyCreatedDBObject(blkRef, true);
                        if (Explode)
                        {
#if DEBUG
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...炸开开始", 1);
#endif
                            try
                            {
                                //Palettes.WriteLog("merge dwg ExplodeToOwnerSpace() before ...");
                                blkRef.ExplodeToOwnerSpace();
                            }
                            catch (Exception ex)
                            {
                                Palettes.WriteLog(string.Format("block class name: {0}, blockRef name: {1}, ref block name: {2}, IsDynamicBlock: {3}", blkObjId.ObjectClass.Name, blkRef.Name, blkRef.BlockName, blkRef.IsDynamicBlock));
                                Palettes.WriteLog(ex.Message.ToString());

                                var reblkRef = new BlockReference(pt, blkObjId);
                                btr.AppendEntity(reblkRef);
                                tran.AddNewlyCreatedDBObject(reblkRef, true);
                                try
                                {
                                    Palettes.WriteLog(string.Format("create new blkref  try again,ptX {0},ptY {0},ptZ {0}", pt.X, pt.Y, pt.Z));
                                    reblkRef.ExplodeToOwnerSpace();
                                    reblkRef.Erase(true);
                                }
                                catch (Exception ex2)
                                {
                                    Palettes.WriteLog(string.Format("block class name: {0}, blockRef name: {1}, ref block name: {2}, IsDynamicBlock: {3}", blkObjId.ObjectClass.Name, blkRef.Name, blkRef.BlockName, blkRef.IsDynamicBlock));
                                    Palettes.WriteLog(ex2.Message.ToString());
                                }
                            }
                            if (blkRef != null)
                            {
                                blkRef.Erase(true);
                            }
#if DEBUG
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...炸开结束", 1);
#endif
                        }

                        //Palettes.WriteLog("merge dwg Erase() after ...");
                        offset += (_isVertOri ? ds.Heigth : ds.Width) + _gapDistance;
                    }

                    //Palettes.WriteLog("merge dwg commit before ...");
                    Component component = new Component(_doc.Database);
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...尺寸上移开始", 1);
                    component.MoveDimOrderUp();
#endif

#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...尺寸上移结束", 1);
#endif
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...pu开始", 1);
#endif
                    if (component.PurgeItems())
                    {
                        Palettes.WriteLog("Clear,Pu");
                    }
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...pu结束", 1);
#endif
                    tran.Commit();
                }

            }
            //Palettes.WriteLog("doc_save before ...");
#if DEBUG
            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...保存文件开始", 1);
#endif
            doc_Save();
#if DEBUG
            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，合并文件中的...保存文件结束", 1);
#endif
            //if (!string.IsNullOrEmpty(outMergeFile))
            //{
            //    _doc.SendStringToExecute("-PURGE A\n*\nN ", true, false, false);

            //    _doc.CommandEnded += doc_CommandEnded;
            //    _doc.CommandCancelled += doc_CommandEnded;
            //    _doc.CommandFailed += doc_CommandEnded;
            //}
        }

        void doc_CommandEnded(object sender, CommandEventArgs e)
        {
            _doc.CommandEnded -= doc_CommandEnded;
            _doc.CommandCancelled -= doc_CommandEnded;
            _doc.CommandFailed -= doc_CommandEnded;

            //Palettes.WriteLog("purge cmd complite, now start save doc...");
            doc_Save();
        }

        void doc_Save()
        {
            if (_doc != null && !string.IsNullOrWhiteSpace(_outMergeFile))
            {
                //Palettes.WriteLog(string.Format("start save file '{0}'", _outMergeFile));
                Utils.SaveDocument(_doc, _outMergeFile, Palettes.stationInfo?.DWGVersion);
                //Palettes.WriteLog(string.Format("end save file '{0}'", _outMergeFile));
            }
            else
            {
                //Palettes.WriteLog(string.Format("doc is null, mergeFile '{0}'.", _outMergeFile));
            }
        }
    }
}
