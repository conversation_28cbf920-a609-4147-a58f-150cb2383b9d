﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Xml.Linq;


namespace CADWSAddin
{
    /// <summary>
    /// 描述组件的参数数据
    /// </summary>
    public class Parameter
    {
        public string Name { get; set; }
        public string Type { get; set; }

        public string Value { get; set; }


        public void ParseXML(XElement xe)
        {
            XElement x1 = xe.Element("Name");
            Name = x1.Value;

            XElement x2 = xe.Element("Type");
            Type = x2.Value;

            XElement x3 = xe.Element("Value");
            Value = x3.Value;
        }

        public string Format()
        {
            return Value;
        }


    }


    /// <summary>
    /// 描述CAD中的函数操作
    /// </summary>
    public class Function
    {
        public string Name { get; set; }
        public string OperObject { get; set; }

        public string Value { get; set; }


        public void ParseXML(XElement xe)
        {
            XElement x1 = xe.Element("Name");
            Name = x1.Value;

            XElement x2 = xe.Element("OperObject");
            OperObject = x2.Value;

            XElement x3 = xe.Element("Value");
            Value = x3.Value;
        }


    }
  



}
