﻿using System;
using System.Reflection;
using System.Runtime.InteropServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Interop.Common;
using Autodesk.AutoCAD.Runtime;

using CADWSAddin.Tools;
using System.IO;
using Autodesk.AutoCAD.PlottingServices;

[assembly: ExtensionApplication(typeof(CADWSAddin.LoadWSAddin))]
[assembly: CommandClass(null)]



namespace CADWSAddin
{
    class LoadWSAddin : IExtensionApplication
    {
      //  protected internal CustomPropertyOfPoint custProp = null;
        static Palettes ShowCADWorkstation;

        //public event EndPlotEventHandler EventHandlePlotEnd;
        //public static LoadWSAddin Instance { set; get; }
        //PlotReactorManager _plotReactorMgr = new PlotReactorManager();

        public void Terminate()
        {
            // Remove the Dynamic Property

           // Dictionary classDict = SystemObjects.ClassDictionary;
           // RXClass pointDesc = (RXClass)classDict.At("AcDbPoint");


          //  IPropertyManager2 pPropMan = (IPropertyManager2)xOPM.xGET_OPMPROPERTY_MANAGER(pointDesc);
         //   pPropMan.RemoveProperty((object)custProp);
          //  custProp = null;
            //_plotReactorMgr.EndPlot -= new EndPlotEventHandler(CallEventHandlerPlotEnd);
        }

        public void Initialize()
        {
            //LoadWSAddin.Instance = this;

            Application.DisplayTextScreen = false;

            //_plotReactorMgr.EndPlot += new EndPlotEventHandler(CallEventHandlerPlotEnd);
      
           // Assembly.LoadFrom("D:\\Program Files\\Autodesk\\AutoCAD 2015\\asdkOPMNetExt.dll");
            //Assembly.LoadFrom("C:\\Users\\<USER>\\Documents\\Visual Studio 2012\\Projects\\CADWSAddin\\ClassLibrary1\\bin\\Debug\\CADWorkstation.dll");
            //var path = System.IO.Path.GetDirectoryName(this.GetType().Assembly.CodeBase.Replace("file:///", ""));
            //var dllFiles = Directory.GetFiles(path, "*.dll");
            //foreach (var dllFile in dllFiles)
            //{
            //    Assembly.LoadFrom(dllFile);

            //}
            // Add the Dynamic Property

           // Dictionary classDict = SystemObjects.ClassDictionary;
           // RXClass pointDesc = (RXClass)classDict.At("AcDbPoint");

           // IPropertyManager2 pPropMan = (IPropertyManager2)xOPM.xGET_OPMPROPERTY_MANAGER(pointDesc);

           // custProp = new CustomPropertyOfPoint();
          //  pPropMan.AddProperty((object)custProp);
            var path = System.IO.Path.GetDirectoryName(this.GetType().Assembly.CodeBase.Replace("file:///", ""));
            var iniFile = Path.Combine(path, "cad3dproe.ini");

            Palettes.IniConfigData = new IniConfigInfo();

            System.Text.StringBuilder temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("Control", "AutoLoad", "0", temp, 5, iniFile);
            if (temp.ToString() == "1")
            {
                ShowCADWorkstation = new Palettes();
                ShowCADWorkstation.ShowCADWorkstation();
            }

            //temp = new System.Text.StringBuilder(5);

            ////merge file name
            //GetPrivateProfileString("DwgFile", "MergeDwgFileName", "", temp, 5, iniFile);
            //if (!string.IsNullOrWhiteSpace(temp.ToString())) ShowCADWorkstation.MergeDwgFileName = temp.ToString();

            temp = new System.Text.StringBuilder(5);

            GetPrivateProfileString("DwgFile", "GapDistance", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                float distance = 0.0f;
                float.TryParse(temp.ToString(), out distance);

                Palettes.IniConfigData.GapDistance = distance;
            }

            temp = new System.Text.StringBuilder(5);

            GetPrivateProfileString("DwgFile", "IsMergeDwgFiles", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsMergeDwgFiles = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("PdfFile", "IsMergePdfFiles", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsMergePdfFiles = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "IsHoldOnDimenPoint", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsHoldOnDimenPoints = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "IsHoldOnUnusedFile", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsHoldOnUnusedFiles = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "IsNoRenamePoint", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsNoRenamePoints = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "DimMinPinLength", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                double dl = 0.0f;
                double.TryParse(temp.ToString(), out dl);

                Palettes.IniConfigData.DimMinPinLength = dl;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "IsFuzzyMatchPointName", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.IsFuzzyMatchPointName = b != 0;
            }

            temp = new System.Text.StringBuilder(5);
            GetPrivateProfileString("ContentCtrl", "UserInterval", "", temp, 5, iniFile);
            if (!string.IsNullOrWhiteSpace(temp.ToString()))
            {
                int b = 0;
                int.TryParse(temp.ToString(), out b);

                Palettes.IniConfigData.UserInterval = b;
            }
        }
        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string section, string key, string def, System.Text.StringBuilder retVal, int size, string filePath);


        //private void CallEventHandlerPlotEnd(object sender, EndPlotEventArgs e)
        //{
        //    Palettes.WriteLog("PlotEndEventHandler() start...");
        //    if (EventHandlePlotEnd != null)
        //    {
        //        EventHandlePlotEnd(sender, e);
        //        EventHandlePlotEnd = null;
        //    }
        //    Palettes.WriteLog("PlotEndEventHandler() end...");
        //}
    }
}
