﻿<Node >
  <ModelName>D:\Drawing1\bai\井道装配2010.dwg</ModelName>
  <BlockName >井道装配</BlockName>
  <Parameters>
    <Parameter>
      <Name>插入点</Name>
      <Type>String</Type>
      <Value />
    </Parameter>
  </Parameters>
  <Functions>
    <Function>
      <Name>SetVariable</Name>
      <OperObject>ZX对重到原点距离X</OperObject>
      <Value>7777</Value>
    </Function>
  </Functions>
  <ChildNodes >
    <Node>
      <ModelName></ModelName>
      <BlockName >井道</BlockName>
      <Parameters>
        <Parameter>
          <Name>插入点</Name>
          <Type>String</Type>
          <Value />
        </Parameter>
      </Parameters>
      <Functions>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道左内壁到轿厢中心距离</OperObject>
          <Value>1501</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道右内壁到轿厢中心距离</OperObject>
          <Value>900</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX开门宽</OperObject>
          <Value>901</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道净深</OperObject>
          <Value>2301</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道前壁厚</OperObject>
          <Value>200</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道左壁厚</OperObject>
          <Value>201</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道右壁厚</OperObject>
          <Value>200</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道后壁厚</OperObject>
          <Value>200</Value>
        </Function>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX井道前距</OperObject>
          <Value>1001</Value>
        </Function>
      </Functions>
      <ChildNodes />
    </Node>
    <Node >
      <ModelName></ModelName>
      <BlockName>轿厢</BlockName>
      <Parameters>
        <Parameter>
          <Name>插入点</Name>
          <Type>String</Type>
          <Value />
        </Parameter>
      </Parameters>
      <Functions>
        <Function>
          <Name>SetVariable</Name>
          <OperObject>ZX轿厢净宽</OperObject>
          <Value>1599</Value>
        </Function>
      </Functions>
      <ChildNodes>
        <Node >
          <ModelName></ModelName>
          <BlockName>轿厢反绳轮</BlockName>
          <Parameters>
            <Parameter>
              <Name>插入点</Name>
              <Type>string</Type>
              <Value />
            </Parameter>
          </Parameters>
          <Functions>
            <Function>
              <Name>SetVariable</Name>
              <OperObject>可见性1</OperObject>
              <Value>双轮</Value>
            </Function>
            <Function>
              <Name>SetVariable</Name>
              <OperObject>翻转状态1</OperObject>
              <Value>未翻转</Value>
            </Function>
          </Functions>
          <ChildNodes>
          </ChildNodes>
        </Node>
      </ChildNodes>
    </Node>
  </ChildNodes>
</Node>
