﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using CADWSAddin.Tools;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;

namespace CADWSAddin
{
    public class DatabaseOper
    {
        private Database _database = null;
        private List<Parameter> _lsPara = null;

        public DatabaseOper(Database db)
        {
            _database = db;
        }

        public void SetParameters(List<Parameter> ls)
        {
            _lsPara = ls;
        }





        //格式化属性需要用{}包围，用于区别其他属性值
        private bool SetAttribute(string attr, string sval)
        {
            if (_database == null) return false;

            using (Transaction trans = _database.TransactionManager.StartTransaction())
            {
                //for (int i = 0; i < _block.AttributeCollection.Count; i++)
                //{
                //    AttributeReference ar = trans.GetObject(_block.AttributeCollection[i], OpenMode.ForWrite) as AttributeReference;
                //    if (ar.TextString.StartsWith("{"))
                //    {
                //        string s = ar.TextString.TrimStart("{".ToCharArray());
                //        s = s.TrimEnd("}".ToCharArray());

                //       if ( s == attr )
                //        {
                //            ar.TextString = sval;
                //        }
                //    }
                //}

                trans.Commit();
            }
            return true;
        }



        private void SetPropertyValue(DynamicBlockReferenceProperty prop, string value)
        {
            if (string.IsNullOrEmpty(value))
                return;
            double pi = 3.1415926;
            if (prop.Value is double)
            {
                double tempDouble;
                if (!double.TryParse(value, out tempDouble))
                {
                    //"格式错误 属性 {0} ，值 {1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("FormatError_AttributeValue"), prop.PropertyName, value), 1);
                    return;
                }
                    
                if (prop.UnitsType == DynamicBlockReferencePropertyUnitsType.Angular)
                {

                    if (double.TryParse(value, out tempDouble))
                    {
                        prop.Value = tempDouble * pi / 180;
                    }
                }
                else
                {
                    prop.Value = tempDouble;
                }
            }
            else if (prop.Value is int)
            {
                int tempInt;
                if (!int.TryParse(value, out tempInt))
                {
                    //"格式错误 属性 {0} ，值 {1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("FormatError_AttributeValue"), prop.PropertyName, value), 1); 
                    return;
                }
                prop.Value = tempInt;
            }
            else if (prop.Value is Int16)
            {
                if (value == "已翻转")
                {
                    prop.Value = (Int16)1;
                    return;
                }
                if (value == "未翻转")
                {
                    prop.Value = (Int16)0;
                    return;
                }
                Int16 tempInt16;
                if (!Int16.TryParse(value, out tempInt16))
                {
                    //"格式错误 属性 {0} ，值 {1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("FormatError_AttributeValue"), prop.PropertyName, value), 1);
                    return;
                }
                prop.Value = tempInt16;
 
            }
            else if (prop.Value is string)
            {
                prop.Value = value;
            }
        }


        public void DriverDynBlock(string blkName, List<Function> lsFun, string parentBlkName)
        {
            if (_database == null) return;

            using (Transaction trans = _database.TransactionManager.StartTransaction())
            {
                //var t = _database.GetAllDynBlockReferences(blkName);
                // var t2 = _database.GetAllBlockReferences(blkName);
                BlockTable bt = (BlockTable)_database.BlockTableId.GetObject(OpenMode.ForRead);
                //trans.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;
            
                BlockTableRecord btr;

                if (string.IsNullOrEmpty(parentBlkName)|| !bt.Has(parentBlkName))
                {
                    btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                }
                else
                {
                   
                    btr = trans.GetObject(bt[parentBlkName], OpenMode.ForWrite) as BlockTableRecord;
                }
                // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                // BlockTableRecord btr = trans.GetObject(bt["井道装配"], OpenMode.ForWrite) as BlockTableRecord;


               // List<Dimension> dims = new List<Dimension>();
                foreach (ObjectId blockId in btr)
                {
                   
                  //  Palettes.WriteLog(blockId.ObjectClass.DxfName);
                    //if (blockId.ObjectClass.DxfName == "DIMENSION")
                    //{
                    //    Dimension dime = blockId.GetObject(OpenMode.ForWrite) as Dimension;
                    //    dims.Add(dime);
                    //}
                    
                    if (string.Compare(blockId.ObjectClass.DxfName, "INSERT", true) != 0) continue;

                    BlockReference childBlock = blockId.GetObject(OpenMode.ForWrite) as BlockReference;
                    if (childBlock == null) continue;
                    if (!childBlock.IsDynamicBlock) continue;
                    //  if (!childBlock.IsDynamicBlock || childBlock.Name != blkName) continue;//匿名块问题
                  
                    if (childBlock.GetBlockName() != blkName) continue;
                     childBlock.ResetBlock();

                  

                    for (int i = 0; i < lsFun.Count(); i++)
                    {
                        for (int j = 0; j < childBlock.DynamicBlockReferencePropertyCollection.Count; j++)
                        {
                         
                            if (lsFun[i].OperObject == childBlock.DynamicBlockReferencePropertyCollection[j].PropertyName)
                            {
                                Palettes.WriteLog(string.Format("{0} : {1}",childBlock.DynamicBlockReferencePropertyCollection[j].PropertyName,lsFun[i].Value));
                                SetPropertyValue(childBlock.DynamicBlockReferencePropertyCollection[j], lsFun[i].Value);
                            }
                        }
                    }
              
                    

                }
              

                trans.Commit();
            }

        }
        public void DriverDynBlock2(string blkName, List<Function> lsFun)
        {
            if (_database == null) return;
            // "***驱动块{0}"
            Palettes.WriteLog(string.Format(LanguageHelper.GetString("DriveBlock"), blkName)); 
            using (Transaction trans = _database.TransactionManager.StartTransaction())
            {
            
                BlockTable bt = (BlockTable)_database.BlockTableId.GetObject(OpenMode.ForRead);
        BlockTableRecord btr  = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                foreach (ObjectId blockId in btr)
                {

                  
                    if (string.Compare(blockId.ObjectClass.DxfName, "INSERT", true) != 0) continue;

                    BlockReference childBlock = blockId.GetObject(OpenMode.ForWrite) as BlockReference;
                    if (childBlock == null) continue;
                    if (!childBlock.IsDynamicBlock) continue;
                    if (childBlock.GetBlockName() != blkName) continue;
                    childBlock.ResetBlock();
                    for (int i = 0; i < lsFun.Count(); i++)
                    {
                        for (int j = 0; j < childBlock.DynamicBlockReferencePropertyCollection.Count; j++)
                        {

                            if (lsFun[i].OperObject == childBlock.DynamicBlockReferencePropertyCollection[j].PropertyName)
                            {
                                Palettes.WriteLog(string.Format("{0} : {1}", childBlock.DynamicBlockReferencePropertyCollection[j].PropertyName, lsFun[i].Value));
                                SetPropertyValue(childBlock.DynamicBlockReferencePropertyCollection[j], lsFun[i].Value);
                            }
                        }
                    }

                }
                trans.Commit();
            }
        }
      
        
        public void Test()
        {
            BlockTable bt = (BlockTable)_database.BlockTableId.GetObject(OpenMode.ForRead);
            foreach (var item in bt)
            { 

                
            }

        }

        //格式化文本类型对象
        public void FormatText(string sName, string sVal)
        {
            if (_database == null) return;


            using (Transaction trans = _database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = trans.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTableRecord;

                #region 遍历块表对象
                foreach (ObjectId blockId in btrBlock)
                {
                    DBObject ent = trans.GetObject(blockId, OpenMode.ForRead) as Entity;
                    if (ent is Dimension)
                    {
                        Dimension ent1 = trans.GetObject(ent.ObjectId, OpenMode.ForWrite) as Dimension;
                        string s1 = ent1.DimensionText;
                        TextOperFP tm = new TextOperFP(true, _lsPara);
                        tm.FormatText(ent1);
                        string s2 = ent1.DimensionText;
                        if (string.IsNullOrEmpty(s2) && s1 != s2)
                        {
                            ent1.Erase();
                        }
                    }
                    if (ent is MText || ent is DBText || ent is FeatureControlFrame)
                    {
                        string sOld = "";
                        if (ent is MText)
                        {
                            sOld = ((MText)ent).Text;
                        }

                        ent = trans.GetObject(ent.ObjectId, OpenMode.ForWrite);
                        TextOperFP tm = new TextOperFP(true, _lsPara);
                        tm.FormatText(ent);

                        if (ent is MText)
                        {
                            MText mt = ent as MText;
                            if (mt.Text != sOld && mt.ActualWidth > mt.Width)
                            {
                                for (int i = 0; i < 5; i++)
                                {
                                    mt.TextHeight -= 0.2;
                                    if (mt.ActualWidth <= mt.Width)
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (ent is Table)
                    {
                        ent = trans.GetObject(ent.ObjectId, OpenMode.ForWrite);
                        TableOper tp = new TableOper(ent as Table);
                        tp.Format(_lsPara);
                    }


                }
                #endregion
                trans.Commit();
            }

        }
    }
}
