﻿using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using CADWSAddin;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;
using System.Data;
using Autodesk.AutoCAD.EditorInput;

namespace CADWSAddin.Tools
{
    public partial class UCTreeView : UserControl
    {
        public UCTreeView()
        {
            InitializeComponent();
            this.treeViewEnts.ImageList = imageListNode;
        }

        private void treeViewEnts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            //选中节点的根节点名称
            string rootName = findRoot(e.Node).Text;
            //如果根节点名称与活动文档的名称不相同，则需要切换活动文档
            if (rootName != AcadApp.DocumentManager.MdiActiveDocument.Name)
            {
                //查找文档名为选中节点名的文档
                var docs = from Document doc in AcadApp.DocumentManager
                           where doc.Name == rootName
                           select doc;
                if (docs.Count() == 1)//如果找到，则切换活动文档
                    AcadApp.DocumentManager.MdiActiveDocument = docs.First();
            }
            switch (e.Node.Text)//判断选中节点的标签文本
            {
                //case "门":
                //    GetPointsFromDwg("Door");//统计Door块个数并填充DataGridView
                //    break;
                case "点":
                    GetPointsFromDwg("xPoint");//统计Window块个数并填充DataGridView
                    break;
                default://其它的标签文本，DataGridView的内容设为空
                    this.dataGridViewEnts.DataSource = null;
                    break;
            }
        }
        //private void GetBlocksFromDwg(string blockName)
        //{
        //    Database db = HostApplicationServices.WorkingDatabase;
        //    using (Transaction trans = db.TransactionManager.StartTransaction())
        //    using (DocumentLock loc = db.GetDocument().LockDocument())
        //    {
        //        //查找当前文档中块名以blockName开头的块参照
        //        var blocks = from block in db.GetEntsInModelSpace<BlockReference>()
        //                     //条件：块参照的块名以blockName开头
        //                     where block.GetBlockName().StartsWith(blockName)
        //                     //设置一个中间变量，取值为块参照的SYM.属性（即门或窗的符号）
        //                     let SYM = block.ObjectId.GetAttributeInBlockReference("SYM.")
        //                     //根据符号对符合条件的块参照进行分组
        //                     group block by SYM into g
        //                     orderby g.Key //根据符号对组进行升序排序
        //                     select new    //创建一个新的匿名类型作为结果
        //                     {
        //                         符号 = g.Key,
        //                         宽度 = g.First().ObjectId.GetAttributeInBlockReference("WIDTH"),
        //                         高度 = g.First().ObjectId.GetAttributeInBlockReference("HEIGHT"),
        //                         个数 = g.Count()
        //                     };
        //        //设置DataGridView的数据源以显示块的分组统计信息
        //        this.dataGridViewEnts.DataSource = blocks.ToList();
        //        trans.Commit();
        //    }
        //}
        private void GetPointsFromDwg(string xPoint)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            using (DocumentLock loc = db.GetDocument().LockDocument())
            {
                //查找当前文档中块名以blockName开头的块参照
                var Points = from Point in db.GetEntsInModelSpace<DBPoint>()
                             //条件：块参照的块名以blockName开头
                             //where block.GetBlockName().StartsWith(blockName)
                             //设置一个中间变量，取值为点的SYM.属性（即门或窗的符号）
                             let SYM = Point.ObjectId
                             //根据符号对符合条件的块参照进行分组
                             group Point by SYM into g
                             orderby g.Key //根据符号对组进行升序排序
                             select new   //创建一个新的匿名类型作为结果
                             {
                                 编号 = g.First().ObjectId,
                                 名称 = g.First().XData,
                                 坐标X = (int)g.First().Position.X,
                                 坐标Y = (int)g.First().Position.Y
                             };



                System.Data.DataTable dataTable = new System.Data.DataTable();
                //System.Data.DataTable dataTables = new System.Data.DataTable();
                //this.dataGridViewEnts.DataSource = Points.ToList();//必须转化为dataTable才能修改

                this.dataGridViewEnts.DataSource = dataTable;
                dataTable.Columns.Add("名称");
                dataTable.Columns.Add("坐标X").ReadOnly = true;
                dataTable.Columns.Add("坐标Y").ReadOnly = true;
                dataTable.Columns.Add("编号").ReadOnly = true;





                foreach (var point in Points.ToList())
                {
                    var dr = dataTable.NewRow();

                    dr["名称"] = point.名称;
                    if (dr.IsNull(0) == false)
                    {
                        string sourceValue = dr["名称"].ToString();   //((1001,NAME)(1000,123))
                        //sourceValue = sourceValue.Substring(sourceValue.LastIndexOf(",") + 1);
                        //sourceValue = sourceValue.Substring(0, sourceValue.IndexOf(")"));
                        //dr["名称"] = sourceValue;

                        sourceValue = sourceValue.Remove(0, 17);
                        sourceValue = sourceValue.Remove(sourceValue.Length - 2);
                        dr["名称"] = sourceValue;
                    }
                    dr["坐标X"] = point.坐标X;
                    dr["坐标Y"] = point.坐标Y;
                    dr["编号"] = point.编号;
                    dataTable.Rows.Add(dr);
                }

                //dataTables = dataTable;
                //设置DataGridView的数据源以显示块的分组统计信息
                trans.Commit();
            }

        }
        TreeNode findRoot(TreeNode node)
        {
            if (node.Parent == null) return node;
            else return findRoot(node.Parent);
        }

        private string newPointNameValue = string.Empty;
        //private string oldPointNameValue = string.Empty;
        private string PointObjectValue = string.Empty;
        //ObjectId id = new ObjectId();


        private void dataGridViewEnts_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex != -1)
            {
                System.Data.DataTable thisTable = this.dataGridViewEnts.DataSource as System.Data.DataTable;
                newPointNameValue = thisTable.Rows[e.RowIndex][0].ToString();
                PointObjectValue = thisTable.Rows[e.RowIndex][3].ToString();


                Document doc = AcadApp.DocumentManager.MdiActiveDocument;
                Database db = doc.Database;
                Editor ed = doc.Editor;
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                    TypedValueList values = new TypedValueList();
                    values.Add(DxfCode.ExtendedDataAsciiString, newPointNameValue);
                    foreach (ObjectId pointId in btr)
                    {
                        if ("(" + pointId.OldId.ToString() + ")" == PointObjectValue)
                        {
                            pointId.AddXData("NAME", values);
                        }
                    }

                    //ObjectId id.AddXData("NAME", values);
                    trans.Commit();
                }
            }
        }

        private void dataGridViewEnts_CellBeginEdit(object sender, DataGridViewCellCancelEventArgs e)
        {
            Document doc = AcadApp.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            System.Data.DataTable thisTable = this.dataGridViewEnts.DataSource as System.Data.DataTable;
            PointObjectValue = thisTable.Rows[e.RowIndex][3].ToString();
            using (DocumentLock loc = db.GetDocument().LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btr = trans.GetObject(db.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                    foreach (ObjectId pointId in btr)
                    {
                        if ("(" + pointId.OldId.ToString() + ")" == PointObjectValue)
                        {
                            Entity ent = trans.GetObject(pointId, OpenMode.ForRead) as Entity;
                            if (ent != null)
                            {
                                ent.UpgradeOpen();
                                ent.Highlight();
                                ent.ColorIndex = 1;
                               
                            }
                        }
                    }
                    trans.Commit();
                }
            }
            //        System.Windows.Forms.DataGridView table = sender as System.Windows.Forms.DataGridView;
            //        System.Data.DataTable dataTable = table.DataSource as System.Data.DataTable;
            //        if (e.RowIndex != -1 && (dataTable.Rows.Count > e.RowIndex))
            //    {
            //        oldPointNameValue = dataTable.Rows[e.RowIndex][0].ToString();
            //    }
        }
    }
}
