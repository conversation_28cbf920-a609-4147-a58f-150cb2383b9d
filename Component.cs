﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using CADWSAddin.Tools;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Script.Serialization;
using System.Xml.Linq;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ToolTip;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
using Exception = System.Exception;

namespace CADWSAddin
{
    /// <summary>
    /// 表示CAD中的一个装配单元
    /// </summary>
    public class Component
    {
        public Component()
        {
        }

        public Component(Database database)
        {
            _database = database;
        }


        //~Component()
        //{
        //    if (_database != null)
        //    {
        //        _database.Dispose();
        ////        GC.SuppressFinalize(_database);              
        //    }
        //}

        public int PartId { get; set; }
        public string ModelName { get; set; }
        public string ModeDefault { get; set; }

        //不能为空，且在系统中唯一（不能重名）
        public string BlockName { get; set; }

        public string DrawingName { get; set; }
        public Component Parent = null;
        public double PaperSizeWidth { get; set; }
        public double PaperSizeHeight { get; set; }
        // private Database _database = null;
        public bool NoMerage { get; set; }
        public string InstanceName { get; set; }
        /// <summary>
        /// ///////////////////
        /// </summary>
        /// 
        public Document AssemDoucument = null;
        //public Document Document;

        public double RotateAngle { get; set; }
        public List<CADNamingPoint> ChildBlockNamedPoints { get; set; }
        public Point3d BlockNewPosition { get; set; }

        DocumentLock _docLock = null;
        Database _database;
        public Database HisDB
        {
            get { return _database; }
            set { _database = value; }
        }
        Point3d position = new Point3d();
        public Point3d Position
        {
            get { return position; }
            set { position = value; }
        }

        /* public BlockNode(Database Db)
         {
             _database = Db;
         }*/

        public void Dispose()
        {
            Palettes.WriteLog(string.Format("File: '{0}' dispose() start...", DrawingName));
            try
            {
                //for (int i = 0; i < ChildNodes.Count; i++)
                //{
                //    ChildNodes[i].Dispose();
                //}

                using (Transaction tr = HisDB.TransactionManager.StartTransaction())
                {
                    if (HisDB != null) { HisDB.Dispose(); tr.Commit(); }
                }
                HisDB = null;
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(string.Format("File: '{0}' dispose() failed.", DrawingName));
            }
        }
        public void LockDoc()
        {
            if (_docLock == null && AssemDoucument != null) _docLock = AssemDoucument.LockDocument();
        }
        public void UnlockDoc()
        {
            if (_docLock != null) _docLock.Dispose();
            _docLock = null;
        }

        List<CADNamingPoint> _lsNamingPoint = new List<CADNamingPoint>();

        public bool Exec(string fileName, bool? IscutLine, bool useCache)
        {
#if DEBUG
            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，打开模型开始", 1);
#endif
            //0 打开模型
            if (!OpenModel(fileName))
            {
                return false;
            }
#if DEBUG
            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，打开模型结束", 1);
#endif
            if (ChildBlockNamedPoints == null) ChildBlockNamedPoints = new List<CADNamingPoint>();
            Exception retException = null;
            try
            {
                //1 驱动动态块
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，驱动动态块开始", 1);
#endif
                DriveModel(useCache);

#if DEBUG

                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，驱动动态块结束", 1);
#endif
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，重建填充开始", 1);
#endif
                RebuildHatch();
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，重建填充结束", 1);
#endif
                //2 分析数据库
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，分析数据库开始", 1);
#endif
                ParseNamingPoint(HisDB);
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，分析数据库结束", 1);
#endif
                //3 递归子模型
                for (int i = 0; i < ChildNodes.Count; i++)
                {
                    if (!string.IsNullOrEmpty(ChildNodes[i].DrawingName))
                    {
                        string ChildFilename = Path.GetDirectoryName(fileName) + "\\" + ChildNodes[i].DrawingName + ".dwg";
                        //如果是使用缓存, 则不需要再驱动动态块或者重画填充
                        var TaskCode = Palettes._listTaskCodes != null ? Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].PartId) : null;
                        if (TaskCode != null && TaskCode.UseCache == true)
                        {
                            ChildNodes[i].OpenModel(ChildFilename);
                            ChildNodes[i].GetCacheModeBasePoint(1.17);
                            continue;
                        }
                        else
                        {

                            if (!File.Exists(ChildNodes[i].ModelName))
                            {
                                Palettes.WriteLog(string.Format(LanguageHelper.GetString("NotFoundCopyDrawingFailed"), Path.GetFileName(ChildNodes[i].ModelName), 2));
                                if (Palettes._listTaskCodes!=null)
                                {
                                    Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].PartId).AddErInfo("NotFoundDrawing");
                                }
                                //throw new FileNotFoundException(string.Format(LanguageHelper.GetString("NotFoundCopyDrawingFailed"), Path.GetFileName(ChildNodes[i].ModelName)), ChildNodes[i].ModelName);
                            }
                            else
                            {
                                File.Copy(ChildNodes[i].ModelName, ChildFilename, true);
                                //"复制子图纸：  {0} "
                                Palettes.WriteLog(string.Format(LanguageHelper.GetString("CopySubDrawing"), ChildFilename));
                                if (!ChildNodes[i].Exec(ChildFilename, IscutLine, false))
                                {
                                    if (Palettes._listTaskCodes != null)
                                    {
                                        Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].PartId).AddErInfo("OpenModelError");
                                        Palettes.WriteLog(string.Format("OpenModelError{0}", ChildFilename));
                                    }

                                }
                            }
                        }
                    }
                }

#if _DEBUG
            Palettes.WriteLog(string.Format("'{0}' 块装中...", DrawingName));
#endif
                //4 块装配
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，块装配开始", 1);
#endif
                List<ObjectId> lsBrefIds = new List<ObjectId>();
                for (int i = 0; i < ChildNodes.Count; i++)
                {
                    string ChildFilename = Path.GetDirectoryName(fileName) + "\\" + ChildNodes[i].DrawingName + ".dwg";
                    if (!string.IsNullOrEmpty(ChildNodes[i].DrawingName))
                    {
                        if (Palettes._listTaskCodes != null)
                        {
                            //per 生成,表示没有找到这个文件,不能进行装配
                            if (!File.Exists(ChildFilename))
                            {
                                Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].Parent.PartId).AddErInfo("SubDrawingError");
                            }
                            else
                            {

                                var objectId = AssemChildBlock(ChildNodes[i], true);
                                var childTaskCode = Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].PartId);
                                if (objectId == ObjectId.Null || !string.IsNullOrEmpty(childTaskCode.ErrorInfo))
                                {
                                    Palettes.WriteLog("ObjectId" + objectId + "ChildNodes[i]" + childTaskCode.Para7);
                                    Palettes._listTaskCodes?[Palettes._assListIndex].FirstOrDefault(o => o.PartId == ChildNodes[i].Parent.PartId).AddErInfo("SubDrawingError");

                                }
                            }
                        }
                        else
                        {//本地测试使用
                            if (!File.Exists(ChildFilename))
                            {
                                Palettes.WriteLog("NoExists" + ChildFilename);
                            }
                            else
                            {
                                var objectId = AssemChildBlock(ChildNodes[i], true);
                            }
                        }

                    }
                }
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，块装配结束", 1);
#endif
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，删除命名点开始", 1);
#endif
                EraseDimNamingPoint();
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，删除命名点结束", 1);
#endif
                //"'{0}' 图层处理"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("LayerProcessing"), DrawingName));
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，图层处理开始", 1);
#endif
                ExecDoc();
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，图层处理结束", 1);
#endif
                //5 尺寸整理
                //"'{1}' 尺寸整理,打断尺寸线：{0}"
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，整理尺寸开始", 1);
#endif
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("BreakDimLine"), IscutLine, DrawingName));
                
                    DimMgr dimMgr = new DimMgr(this.HisDB, IscutLine);
                    dimMgr.DimTidy();
                    AssemDoucument.Editor.Regen();
                
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，整理尺寸结束", 1);
#endif
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，尺寸次序上移开始", 1);
                MoveDimOrderUp();
#endif

#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，尺寸次序上移结束", 1);
#endif
                //rename point if need
                //" 重命名处理"
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，重命名点开始", 1);
#endif
                Palettes.WriteLog(DrawingName + LanguageHelper.GetString("RenameProcessing"));
                RenamePointObjectIfNeed();
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，重命名点结束", 1);
#endif
                DeleteInvisibleEnt();
                if (!string.IsNullOrEmpty(DxfName) && !useCache)
                {
                    string path = Path.GetDirectoryName(fileName);
                    string DxfFile = Path.Combine(path, DxfName + ".dxf");
                    //"生成文件 {0}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("Makefile"), DxfFile));
                    Palettes.AddHoldOnFiles(DxfFile);
                    if (NoMerage)
                    {
                        Palettes.AddNoMergeFiles(DxfFile);
                    }
                    if (Palettes.totalCommands != null&& Palettes.totalCommands.Count>0)
                        Palettes.totalCommands.FirstOrDefault(o => o.DriveCommand.PartId == PartId).DriveCommand.Files.Add(DxfFile);
                    AssemDoucument.Database.DxfOut(DxfFile, 10, GetVersion(Palettes.stationInfo?.DWGVersion));

                }
                if (Palettes.totalCommands?.Count > 0)
                {
                    Palettes.totalCommands.FirstOrDefault(o => o.DriveCommand.PartId == PartId).DriveCommand.Files.Add(fileName);
                    var finishStatusLog = Palettes._listTaskCodes?[Palettes._assListIndex].FirstOrDefault(o => o.PartId == PartId).AddFinishStatus();
                    Palettes.WriteLog(finishStatusLog);

                }

            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.ToString());
                retException = ex;
                throw ex;

            }
            finally
            {

#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，保存文件开始", 1);
#endif
                //SaveAsFile(fileName);
#if DEBUG
                Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，保存文件结束", 1);
#endif
                UnlockDoc();

                if (retException != null) throw retException;

            }
            // Application.DocumentManager.MdiActiveDocument.SendCommand("WIPEOUT ");
            //  Wipeout aa=new Wipeout();
            //  aa.;

            //6 炸开子
            //for (int i = 0; i < lsBrefIds.Count; i++)
            //{
            //    if (lsBrefIds[i] != ObjectId.Null && ChildNodes[i].IsExploded)
            //    {
            //        ExplodeBlockReference(lsBrefIds[i]);
            //    }
            //}


            //5
            //if (Parent != null)
            //{
            //    WblockDB();
            //}
            //6保存文件
            //SaveAsFile(fileName);
            //7保存pdf文件
            // MakePdfFile(fileName);
            //8关闭文件
            /* if (this.Parent != null)
             {
               AssemDoucument.CloseAndSave(fileName);
             }*/
            //AssemDoucument.CloseAndSave(fileName);


            return true;
        }



        DwgVersion GetVersion(string DWGVer)
        {
            DwgVersion dwgVersion;
            switch (DWGVer)
            {
                case Utility.CAD2007:
                    dwgVersion = DwgVersion.AC1021;
                    break;
                case Utility.CAD2010:
                    dwgVersion = DwgVersion.AC1024;
                    break;
                case Utility.CAD2013:
                    dwgVersion = DwgVersion.AC1027;
                    break;
                default:
                    dwgVersion = DwgVersion.Current;
                    break;
            }

            return dwgVersion;
        }
        //    public static Document AssemDoucument0 = null;
        ////  public AcadDocument AaAcadDocument;
        //    Database _database0;
        //    private bool OpenModel1(string ModelName)
        //    {
        //        if (!File.Exists(ModelName))
        //        {
        //            //Palettes.WriteLog(string.Format("{0}没有找到", ModelName)); return false;
        //        }

        //        // if (this.Parent == null)         // 2017-080-21 yyg
        //        // {
        //        AssemDoucument0 = Application.DocumentManager.Add(ModelName);
        //        //Application.DocumentManager.Open()
        //        _database0 = AssemDoucument0.Database;
        //        // }
        //        // else
        //        /* {

        //             this.AssemDoucument = this.Parent.AssemDoucument;
        //             _database = LoadWBlock(ModelName);
        //         }*/

        //        return true;
        //    }
        //public Document AssemDoucument2 = null;
        //Database _database2;
        //private bool OpenModel2(string ModelName)
        //{
        //    if (!File.Exists(ModelName))
        //    {
        //        //Palettes.WriteLog(string.Format("{0}没有找到", ModelName)); return false;
        //    }

        //    // if (this.Parent == null)         // 2017-080-21 
        //    // {
        //    AssemDoucument2 = Application.DocumentManager.Add(ModelName);
        //    _database2 = AssemDoucument2.Database;
        //    // }
        //    // else
        //    /* {

        //         this.AssemDoucument = this.Parent.AssemDoucument;
        //         _database = LoadWBlock(ModelName);
        //     }*/

        //    return true;
        //}

        /// <summary>
        /// 尺寸
        /// </summary>
        /// <param name="db"></param>
        /// <returns></returns>
        public static List<string> GetDimStyleName(Database db, Document doc)
        {
            List<string> ls = new List<string>();
            //  Document doc1 = Application.DocumentManager.MdiActiveDocument;
            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForRead);

                    if (obj is Dimension)
                    {
                        Dimension mt = obj as Dimension;
                        string handle = mt.Handle.Value.ToString();




                    }
                    if (obj is Polyline)
                    {
                        Polyline tt = obj as Polyline;

                    }

                }
                trans.Commit();
                //trans.Dispose();
            }

            return ls;
        }
        //private AcadEntity[] acadEntitys2;
        /// <summary>
        /// /文字
        /// </summary>
        /// <param name="db"></param>
        /// <returns></returns>
        public static List<string> GetTextStyleName(Database db)
        {
            List<string> ls = new List<string>();
            try
            {
                using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                    TextStyleTable dst = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
                    foreach (ObjectId id in dst)
                    {
                        if (id.GetObject(OpenMode.ForRead).GetRXClass().DxfName == "STYLE")
                        {
                            TextStyleTableRecord dstr = (TextStyleTableRecord)id.GetObject(OpenMode.ForRead);
                            ls.Add(dstr.Name);
                        }
                    }
                }
            }
            catch
            {
            }
            return ls;
        }
        public List<string> GetMText(List<string> ls)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForRead);
                    if (obj is DBText)
                    {
                        DBText mt = obj as DBText;

                        if (mt.TextString.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.TextString));
                        }
                    }
                    else if (obj is MText)
                    {
                        MText mt = obj as MText;

                        if (mt.Text.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.Contents));
                        }
                    }
                    else if (obj is Dimension)
                    {
                        Dimension mt = obj as Dimension;

                        if (mt.DimensionText.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.DimensionText));
                        }

                    }
                    else if (obj is FeatureControlFrame)
                    {
                        FeatureControlFrame mt = obj as FeatureControlFrame;

                        if (mt.Text.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.Text));
                        }
                    }
                }
                trans.Commit();
                //trans.Dispose();
                return ls;
            }
        }
        public String TrimMText(string text)
        {
            int found = text.IndexOf("/");
            string newText = text.Substring(found + 1).Replace("/", "").Replace("}", "");
            return newText;
        }
        public static List<ObjectId> GetBlockReferenceIds0(Database HisDB0)
        {
            List<ObjectId> ls = new List<ObjectId>();

            using (Transaction trans = HisDB0.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB0);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                ObjectIdCollection collect = new ObjectIdCollection();

                foreach (ObjectId objId in btr)
                {

                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference))))
                    {
                        ls.Add(objId);
                    }
                }
            }

            return ls;
        }



        /////////////////////////////////////////////////////////////////
        /// <summary>
        /// /
        /// </summary>
        /// <param name="brfId"></param>
        private void ExplodeBlockReference(ObjectId brfId)
        {
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                BlockReference blkref = trans.GetObject(brfId, OpenMode.ForWrite) as BlockReference;
                if (blkref != null)
                {
                    blkref.ExplodeToOwnerSpace();
                }
                trans.Commit();
            }
        }
        /// <summary>
        /// 炸开所有块
        /// </summary>
        /// <param name="b_name">块名称</param>
        public void BlockSelectAndExplode()
        {
            this.HisDB = Application.DocumentManager.MdiActiveDocument.Database;

            int blockCount = 1;
            while (blockCount != 0)
            {
                blockCount = 0;
                Document doc = Application.DocumentManager.MdiActiveDocument;
                //  Database db = doc.Database;
                // Editor ed = doc.Editor;
                Transaction transaction = this.HisDB.TransactionManager.StartTransaction();
                using (transaction)
                {
                    Entity entity = null;
                    DBObjectCollection EntityCollection = new DBObjectCollection();
                    DocumentLock documentLock = doc.LockDocument();

                    BlockTable bt = (BlockTable)transaction.GetObject(this.HisDB.BlockTableId, OpenMode.ForWrite);
                    BlockTableRecord btr = (BlockTableRecord)transaction.GetObject(this.HisDB.CurrentSpaceId, OpenMode.ForWrite);
                    foreach (ObjectId id in btr)
                    {
                        entity = (Entity)transaction.GetObject(id, OpenMode.ForWrite);
                        if (entity is BlockReference)
                        {
                            if (entity.ObjectId.ObjectClass.DxfName != "ACAD_TABLE")//当块参照为表格时，不炸开。
                            {
                                blockCount++;
                                BlockReference br = (BlockReference)entity;
                                br.ExplodeToOwnerSpace();
                                br.Erase();
                                //entity.Explode(EntityCollection);
                                //entity.UpgradeOpen();
                                //entity.Erase();
                            }
                        }
                    }
                    if (blockCount == 0)
                    {
                        break;
                    }


                    //AddEntityCollection(EntityCollection, doc);
                    transaction.Commit();
                    //transaction.Dispose();
                    documentLock.Dispose();
                }
            }
        }

        public void ExecDoc()
        {
#if _DEBUG
            Palettes.WriteLog("ExecDoc() start...");
#endif

            // this.HisDB = Application.DocumentManager.MdiActiveDocument.Database;

            //foreach (var cNote in ChildNodes)
            //{

            //    cNote.ExecDoc();
            //}


            foreach (var fItem in Functions)
            {
#if _DEBUG
            Palettes.WriteLog(string.Format("start processing function:'{0}', value:{1}", fItem.Name, fItem.Value));
#endif
                switch (fItem.Name)
                {
                    case "SetDimVariable":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetDimVariable : {0}", fItem.Value));
#endif
                        SetDimVariable(fItem.Value);
                        break;
                    //SetText
                    case "SetText":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetText : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetText(fItem.OperObject, fItem.Value);
                        break;
                    //SetTableVariable
                    case "SetTableVariable":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetTableVariable : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetTableVariable(fItem.OperObject, fItem.Value);
                        break;

                    case "SetLayerVariable":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetLayerVariable : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetLayerVariable(fItem.OperObject, fItem.Value);
                        break;
                    case "SetLayerLineWeight":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetLayerLineWeight : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetLayerLineWeight(fItem.OperObject, fItem.Value);
                        break;
                    //SetTextStyle
                    case "SetTextStyle":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetSextStyle : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetSextStyle(fItem.OperObject, fItem.Value);
                        break;
                    //SetDimStyle
                    case "SetDimStyle":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetDimStyle : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetDimStyle(fItem.OperObject, fItem.Value);
                        break;

                    case "SetAddDim":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetDimStyle : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetAddDim(fItem.Value);
                        break;

                    default:
                        break;
                }
            }

#if _DEBUG
            Palettes.WriteLog("ExecDoc() end...");
#endif
        }
        public void ExecEnd()
        {

            // this.HisDB = Application.DocumentManager.MdiActiveDocument.Database;

            foreach (var cNote in ChildNodes)
            {

                cNote.ExecEnd();
            }
            foreach (var fItem in Functions)
            {
                switch (fItem.Name)
                {
                    case "SetLayerVariable":
#if _DEBUG
                          Palettes.WriteLog(string.Format("SetLayerVariable : {0}  {1}", fItem.OperObject, fItem.Value));
#endif
                        SetLayerVariable(fItem.OperObject, fItem.Value);
                        break;
                    default:
                        break;
                }
            }

        }

        public void SetDimVariable(string dimValuels)
        {

            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                //List<string> CenterPointls = DimStyleTools.xmlDimList("CenterPoint");
                //List<string> StartPointls = DimStyleTools.xmlDimList("StartPoint");
                //List<string> EndPointls = DimStyleTools.xmlDimList("EndPoint");
                //List<string> ReferenceStartPointls = DimStyleTools.xmlDimList("ReferenceStartPoint");
                //List<string> ReferenceEndPointls = DimStyleTools.xmlDimList("ReferenceEndPoint");
                //List<string> LayerNumberls = DimStyleTools.xmlDimList("LayerNumber");
                //List<string> LayerDistancels = DimStyleTools.xmlDimList("LayerDistance");
                //List<string> Heightls = DimStyleTools.xmlDimList("Height");
                //List<string> Typels = DimStyleTools.xmlDimList("Type");
                //List<string> Textls = DimStyleTools.xmlDimList("Text");
                //List<string> Stylels = DimStyleTools.xmlDimList("Style");

                List<string> ls = new TableOper(null).htmlTable(dimValuels);
                List<string> CenterPointls = new List<string>();
                List<string> StartPointls = new List<string>();
                List<string> EndPointls = new List<string>();
                List<string> ReferenceStartPointls = new List<string>();
                List<string> ReferenceEndPointls = new List<string>();
                List<string> LayerNumberls = new List<string>();
                List<string> LayerDistancels = new List<string>();
                List<string> Heightls = new List<string>();
                List<string> Typels = new List<string>();
                List<string> Textls = new List<string>();
                List<string> Stylels = new List<string>();
                for (int c = 0; c < ls.Count / 11; c++)
                {
                    CenterPointls.Add(ls[c]);
                    StartPointls.Add(ls[c + ls.Count / 11]);
                    EndPointls.Add(ls[c + 2 * ls.Count / 11]);
                    ReferenceStartPointls.Add(ls[c + 3 * ls.Count / 11]);
                    ReferenceEndPointls.Add(ls[c + 4 * ls.Count / 11]);
                    LayerNumberls.Add(ls[c + 5 * ls.Count / 11]);
                    LayerDistancels.Add(ls[c + 6 * ls.Count / 11]);
                    Heightls.Add(ls[c + 7 * ls.Count / 11]);
                    Typels.Add(ls[c + 8 * ls.Count / 11]);
                    Textls.Add(ls[c + 9 * ls.Count / 11]);
                    Stylels.Add(ls[c + 10 * ls.Count / 11]);
                }
                //创建一个列表，用于存储标注对象
                List<Dimension> dims = new List<Dimension>();
                DimStyleTable dst = (DimStyleTable)this.HisDB.DimStyleTableId.GetObject(OpenMode.ForRead);
                for (int i = 0; i < StartPointls.Count; i++)
                {
                    Point3d CenterPoint = Tools.Tools.GetPoint(this.HisDB, CenterPointls[i]);
                    Point3d StartPoint, EndPoint;
                    int height = 0;
                    int count = 0;
                    if (ReferenceStartPointls[i] == "-")
                    {
                        height = Convert.ToInt32(Heightls[i].Trim());
                        StartPoint = Tools.Tools.GetPoint(this.HisDB, StartPointls[i]);
                        EndPoint = Tools.Tools.GetPoint(this.HisDB, EndPointls[i]);
                    }
                    else
                    {
                        count = Convert.ToInt32(LayerNumberls[i].Trim());
                        height = Convert.ToInt32(LayerDistancels[i].Trim()) * count;
                        StartPoint = Tools.Tools.ArrangementPoint(this.HisDB, ReferenceStartPointls[i], ReferenceEndPointls[i], StartPointls[i], Typels[i], height, count);
                        EndPoint = Tools.Tools.ArrangementPoint(this.HisDB, ReferenceStartPointls[i], ReferenceEndPointls[i], EndPointls[i], Typels[i], height, count);
                    }
                    switch (Typels[i])
                    {
                        case "X":
                            // 创建转角标注（水平）
                            RotatedDimension dimRotated1 = new RotatedDimension();
                            //指定第一条尺寸界线的附着位置
                            dimRotated1.XLine1Point = StartPoint;
                            //指定第二条尺寸界线的附着位置
                            dimRotated1.XLine2Point = EndPoint;
                            //指定尺寸线的位置
                            if (count != 0)
                            {
                                if (StartPointls[i].Contains("$"))
                                {
                                    dimRotated1.DimLinePoint = StartPoint.PolarPoint(Math.PI / 2, height);
                                }
                                else dimRotated1.DimLinePoint = StartPoint.PolarPoint(Math.PI / 2, height / count);
                            }
                            dimRotated1.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            if (dst.Has(Stylels[i]))
                            {
                                dimRotated1.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            }
                            dims.Add(dimRotated1);//将水平转角标注添加到列表中
                            break;
                        case "Y":
                            //创建转角标注(垂直）
                            RotatedDimension dimRotated2 = new RotatedDimension();
                            dimRotated2.Rotation = Math.PI / 2;//转角标注角度为90度，表示垂直方向
                                                               //指定两条尺寸界线的附着位置和尺寸线的位置
                            dimRotated2.XLine1Point = StartPoint;
                            dimRotated2.XLine2Point = EndPoint;
                            if (StartPointls[i].Contains("$"))
                            {
                                dimRotated2.DimLinePoint = StartPoint.PolarPoint(0, height);
                            }
                            else dimRotated2.DimLinePoint = StartPoint.PolarPoint(0, height / count);
                            dimRotated2.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            dimRotated2.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            dims.Add(dimRotated2);//将垂直转角标注添加到列表中
                            break;
                        case "D":
                            // 创建对齐标注
                            AlignedDimension dimAligned = new AlignedDimension();
                            //指定两条尺寸界线的附着位置和尺寸线的位置
                            dimAligned.XLine1Point = StartPoint;
                            dimAligned.XLine2Point = EndPoint;
                            dimAligned.DimLinePoint = StartPoint.PolarPoint(Math.Abs(Math.Atan((StartPoint.X - EndPoint.X) / (StartPoint.Y - EndPoint.Y))), height);
                            dimAligned.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            dimAligned.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            dims.Add(dimAligned);//将对齐标注添加到列表中
                            break;
                        case "RAD":
                            // 创建半径标注
                            RadialDimension dimRadial = new RadialDimension();
                            dimRadial.Center = StartPoint;//圆或圆弧的圆心
                                                          //用于附着引线的圆或圆弧上的点
                            dimRadial.ChordPoint = EndPoint;
                            dimRadial.LeaderLength = height;//引线长度
                            dimRadial.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            dimRadial.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            dims.Add(dimRadial);//将半径标注添加到列表中
                            break;
                        case "DIA":
                            // 创建直径标注
                            DiametricDimension dimDiametric = new DiametricDimension();
                            //圆或圆弧上第一个直径点的坐标
                            dimDiametric.ChordPoint = StartPoint;
                            //圆或圆弧上第二个直径点的坐标
                            dimDiametric.FarChordPoint = EndPoint;
                            dimDiametric.LeaderLength = height;//引线长度;
                            dimDiametric.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            dimDiametric.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            dims.Add(dimDiametric);//将直径标注添加到列表中
                            break;
                        case "ANG":
                            // 创建角度标注
                            Point3AngularDimension dimLineAngular = new Point3AngularDimension();
                            //圆或圆弧的圆心、或两尺寸界线间的共有顶点的坐标
                            dimLineAngular.CenterPoint = CenterPoint;
                            //指定两条尺寸界线的附着位置
                            dimLineAngular.XLine1Point = StartPoint;
                            dimLineAngular.XLine2Point = EndPoint;
                            //设置角度标志圆弧线上的点
                            dimLineAngular.ArcPoint = StartPoint.PolarPoint(GeTools.DegreeToRadian(0), height);
                            dimLineAngular.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                            dimLineAngular.DimensionStyle = dst[Stylels[i]];//设置标注样式
                            dims.Add(dimLineAngular);//将角度标注添加到列表中
                            break;
                    }
                }
                foreach (Dimension dim in dims)//遍历标注列表
                {
                    this.HisDB.AddToModelSpace(dim);//将标注添加到模型空间中
                }
                trans.Commit();//提交更改
                               //trans.Dispose();
            }

            //public Point3d GetInsetPoint()
            //{
            //    double x = 0, y = 0;

            //    Parameter px = GetParameter("位置X");
            //    if (px != null)
            //    {
            //        x = double.Parse(px.Value);
            //    }

            //    Parameter py = GetParameter("位置Y");
            //    if (py != null)
            //    {
            //        y = double.Parse(py.Value);
            //    }

            //    return new Point3d(x, y, 0);

            //}

        }
        public void SetAddDim(string dimValue)
        {
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)this.HisDB.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                // 创建旋转尺寸标注
                RotatedDimension acRotDim = new RotatedDimension();
                var angle = 0.0;
                var DimPoints = GetDimPoints(dimValue, out angle);
                acRotDim.XLine1Point = DimPoints[0];
                acRotDim.XLine2Point = DimPoints[1];
                acRotDim.DimLinePoint = DimPoints[2];
                acRotDim.Rotation = angle;
                acRotDim.DimensionStyle = HisDB.Dimstyle;
                // 将新对象添加到块表记录 ModelSpace 及事务
                btr.AppendEntity(acRotDim);
                trans.AddNewlyCreatedDBObject(acRotDim, true);
                trans.Commit();

            }
        }
        private List<Point3d> GetDimPoints(string addDimInfo, out Double angle)
        {
            List<Point3d> DimPoints = new List<Point3d>();
            angle = 0;
            string[] parts = addDimInfo.Split('|');

            if (parts.Length >= 4)
            {
                string xline1Str = parts[0];
                string[] xline1PointStrs = xline1Str.Split(':');
                DimPoints.Add(new Point3d(double.Parse(xline1PointStrs[0]), double.Parse(xline1PointStrs[1]), 0));

                string xline2Str = parts[1];
                string[] xline2PointStrs = xline2Str.Split(':');
                DimPoints.Add(new Point3d(double.Parse(xline2PointStrs[0]), double.Parse(xline2PointStrs[1]), 0));

                string dimLine1Str = parts[2];
                string[] dimLine1PointStrs = dimLine1Str.Split(':');
                var dimLine1Point = new Point3d(double.Parse(dimLine1PointStrs[0]), double.Parse(dimLine1PointStrs[1]), 0);

                string dimLine2Str = parts[3];
                string[] dimLine2PointStrs = dimLine2Str.Split(':');
                var dimLine2Point = new Point3d(double.Parse(dimLine2PointStrs[0]), double.Parse(dimLine2PointStrs[1]), 0);

                if (dimLine1Point.X == dimLine2Point.X)
                {
                    angle = Math.PI / -2;
                }
                DimPoints.Add(new Point3d((dimLine1Point.X + dimLine2Point.X) / 2, (dimLine1Point.Y + dimLine2Point.Y) / 2, 0));


            }
            return DimPoints;
        }

        public void SetTableVariable(string tableName, string tableVale)
        {
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)this.HisDB.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {
                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForWrite) as Table);

                        if (table.GetTableTitle().Contains(tableName))
                        {

                            var ls = new TableOper(null).htmlTable2(tableVale);
                            int columnCount = table.GetColumnsCount();
                            int curRowCount = table.GetRowsCount();
                            //table.InsertData(ls);
                            if (ls.Count != 0 && columnCount != 0)
                            {
                                for (int i = 0; i < ls.Count; i++)
                                {
                                    // List<string> lstable = new List<string>();
                                    //for (int j = 0; j < table.GetColumnsCount(); j++)
                                    //{
                                    //    lstable.Add(ls[j * ((ls.Count + j) / table.GetColumnsCount()) - j]);
                                    //    ls.Remove(ls[j * ((ls.Count + j) / table.GetColumnsCount()) - j]);
                                    //}

                                    if (i + 2 < curRowCount)
                                    {
                                        table.UpdateData(i + 2, ls[i].ToList());
                                    }
                                    else
                                    {
                                        table.InsertData(ls[i].ToList());
                                    }
                                    //if (table.GetRow(2)[0] != "")
                                    //{
                                    //    table.InsertData(ls[i].ToList());
                                    //}
                                    //else table.UpdateData(2, ls[i].ToList());
                                }
                            }
                        }
                    }

                }
                trans.Commit();
                //trans.Dispose();
            }
        }
        public void SetSextStyle(string oldStyleName, string newStyleName)
        {
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                TextStyleTable dst = (TextStyleTable)this.HisDB.TextStyleTableId.GetObject(OpenMode.ForRead);
                if (dst.Has(oldStyleName) && dst.Has(newStyleName))
                {
                    foreach (Entity ent in HisDB.GetEntsInDatabase())
                    {
                        if (ent.GetRXClass().DxfName == "MTEXT")
                        {
                            MText text = (MText)ent;
                            if (text.TextStyleName == oldStyleName)//遍历多行文字列表
                            {
                                text.UpgradeOpen();
                                text.TextStyleId = dst[newStyleName];
                                text.DowngradeOpen();
                            }
                        }
                    }
                    foreach (Entity ent in this.HisDB.GetEntsInDatabase())
                    {
                        if (ent.GetRXClass().DxfName == "TEXT")
                        {
                            DBText text = (DBText)ent;
                            if (text.TextStyleName == oldStyleName)//遍历单行文字列表
                            {
                                text.UpgradeOpen();
                                text.TextStyleId = dst[newStyleName];
                                text.DowngradeOpen();
                            }
                        }
                    }
                }
                else return;
                trans.Commit();
                //trans.Dispose();
            }
        }
        public void SetText(string str1, string str2)
        {
            str1 = string.Format("/{0}/", str1);
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = this.HisDB.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForWrite);
                    if (obj is DBText)
                    {
                        DBText mt = obj as DBText;

                        if (mt.TextString.Contains(str1))
                        {
                            mt.TextString = mt.TextString.Replace(str1, str2);
                        }
                    }
                    else if (obj is MText)
                    {
                        MText mt = obj as MText;

                        if (mt.Text.Contains(str1))
                        {
                            mt.Contents = mt.Contents.Replace(str1, str2);
                        }
                    }
                    else if (obj is Dimension)
                    {
                        Dimension mt = obj as Dimension;

                        if (mt.DimensionText.Contains(str1))
                        {
                            mt.DimensionText = mt.DimensionText.Replace(str1, str2);
                        }
                    }
                    else if (obj is FeatureControlFrame)
                    {
                        FeatureControlFrame mt = obj as FeatureControlFrame;

                        if (mt.Text.Contains(str1))
                        {
                            mt.Text = mt.Text.Replace(str1, str2);
                        }
                    }
                    else if (obj is BlockReference)
                    {
                        BlockReference blkRef = obj as BlockReference;
                        BlockTableRecord blkRec = trans.GetObject(blkRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                        foreach (ObjectId subBlockId in blkRec)
                        {
                            DBObject subObj = trans.GetObject(subBlockId, OpenMode.ForWrite);
                            if (subObj is DBText)
                            {
                                DBText mt = subObj as DBText;

                                if (mt.TextString.Contains(str1))
                                {
                                    mt.TextString = mt.TextString.Replace(str1, str2);
                                }
                            }
                            else if (subObj is MText)
                            {
                                MText mt = subObj as MText;

                                if (mt.Text.Contains(str1))
                                {
                                    mt.Contents = mt.Contents.Replace(str1, str2);
                                }
                            }
                            else if (subObj is Dimension)
                            {
                                Dimension mt = subObj as Dimension;

                                if (mt.DimensionText.Contains(str1))
                                {
                                    mt.DimensionText = mt.DimensionText.Replace(str1, str2);
                                }
                            }
                        }
                    }
                }
                trans.Commit();
                //trans.Dispose();

            }
        }
        public void SetDimStyle(string oldStyleName, string newStyleName)
        {
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                DimStyleTable dst = (DimStyleTable)this.HisDB.DimStyleTableId.GetObject(OpenMode.ForRead);
                if (dst.Has(oldStyleName) && dst.Has(newStyleName))
                {
                    foreach (Entity ent in this.HisDB.GetEntsInDatabase())
                    {
                        if (ent.GetRXClass().DxfName == "DIMENSION")
                        {
                            Dimension dim = (Dimension)ent;
                            //dim.SetDimstyleData(dstr);
                            if (dim.DimensionStyleName == oldStyleName)
                            {
                                dim.UpgradeOpen();
                                dim.DimensionStyle = dst[newStyleName];
                                dim.DowngradeOpen();
                            }
                        }
                    }
                }
                trans.Commit();
                //trans.Dispose();
            }
        }
        //设置图层可见性
        public bool SetLayerVariable(string sLay, string visible)
        {
            bool bVisible = CoverToBool(visible);
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                LayerTable lt;
                lt = (LayerTable)trans.GetObject(this.HisDB.LayerTableId, OpenMode.ForWrite);


                foreach (var item in lt)
                {
                    LayerTableRecord ltr = (LayerTableRecord)trans.GetObject(item, OpenMode.ForWrite);
                    if (ltr.Name.StartsWith("$"))
                    {
                        ltr.IsOff = true;
                    }
                    if (ltr.Name == sLay)
                    {
                        ltr.IsOff = !bVisible;
                        //break;

                    }
                    /* if (ltr.Name == "对重右置")
                     {

                         Palettes.WriteLog(string.Format("设置图层“对重右置”的线宽为：0.09mm"));
                         ltr.LineWeight = LineWeight.LineWeight009;
                     }*/
                }

                trans.Commit();
                //trans.Dispose();
            }

            return true;
        }
        public bool SetLayerLineWeight(string sLay, string value)
        {
            LineWeight lineWeight = CoverToLineWeight(value);
            using (Transaction trans = this.HisDB.TransactionManager.StartTransaction())
            {
                LayerTable lt;
                lt = (LayerTable)trans.GetObject(this.HisDB.LayerTableId, OpenMode.ForWrite);


                foreach (var item in lt)
                {
                    LayerTableRecord ltr = (LayerTableRecord)trans.GetObject(item, OpenMode.ForWrite);

                    if (ltr.Name == sLay)
                    {

                        //"设置图层:“{0}”的线宽为：{1}mm"
                        Palettes.WriteLog(string.Format(LanguageHelper.GetString("SetLayerWidth"), ltr.Name, value));
                        ltr.LineWeight = lineWeight;
                        break;
                    }
                }

                trans.Commit();
                //trans.Dispose();
            }

            return true;
        }

        public bool CoverToBool(string sBool)
        {
            if (sBool == "Y")
            {
                return true;
            }
            return false;
        }
        public LineWeight CoverToLineWeight(string sBool)
        {
            LineWeight lineWeight = LineWeight.ByLineWeightDefault;
            if (sBool == "0.05")
            {
                lineWeight = LineWeight.LineWeight005;
            }
            if (sBool == "0.09")
            {
                lineWeight = LineWeight.LineWeight009;
            }
            if (sBool == "0.40" || sBool == "0.4")
            {
                lineWeight = LineWeight.LineWeight040;
            }
            if (sBool == "0.13")
            {
                lineWeight = LineWeight.LineWeight013;
            }
            if (sBool == "0.50" || sBool == "0.5")
            {
                lineWeight = LineWeight.LineWeight050;
            }
            return lineWeight;
        }

        public void MakePdfFile(string filename)
        {

            ;
        }


        //打开模型
        private bool OpenModel(string ModelName)
        {
            if (!File.Exists(ModelName))
            {
                //"{0}没有找到"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("NotFoundModel"), ModelName)); return false;
            }

            try
            {
                if (this.Parent == null)         // 2017-080-21 yyg
                {
                    try
                    {
                        AssemDoucument = Application.DocumentManager.Add(ModelName);

                    }
                    catch (Exception)
                    {
                        AssemDoucument = Application.DocumentManager.Open(ModelName, false);
                    }
                    _database = AssemDoucument.Database; LockDoc();
                }
                else
                {
                    var pare = Parent;
                    //while (pare.Parent != null) pare = pare.Parent;

                    this.AssemDoucument = pare.AssemDoucument;

                    _database = LoadWBlock(ModelName);
                    if (_database.BlockTableId.IsNull)
                    {
                        Palettes.WriteLog("Fail to open file" + ModelName, 2);
                        return false;

                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception e1)
            {
                Palettes.WriteLog(e1.Message, 2);
                Palettes.WriteLog(e1.StackTrace, 2);
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
            }

            return true;
        }
        //保存模型
        public void SaveAsFile(string ModelName)
        {
            if (!string.IsNullOrEmpty(this.DrawingName))
            {

                string fileName = Path.GetDirectoryName(ModelName) + "\\" + this.DrawingName + ".dwg";
                //"保存文件 {0}"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("SaveFile"), fileName));
                string path = Path.GetDirectoryName(fileName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }

                bool saveSuccess = false;
                int retryCount = 0;
                while (!saveSuccess && retryCount < 5)
                {
                    try
                    {

                        HisDB.SaveAs(fileName, Utils.GetVersion(Palettes.stationInfo.DWGVersion));
                        Palettes.WriteLog(string.Format("save version '{0}'", Palettes.stationInfo.DWGVersion));

                        saveSuccess = true;
                    }
                    catch (Autodesk.AutoCAD.Runtime.Exception ex)
                    {
                        Palettes.WriteLog("retry  saveas ...");
                        AssemDoucument.SendStringToExecute("_qsave\n", false, false, true);
                        retryCount++;
                    }
                    catch (System.Exception ex)
                    {
                        System.Threading.Thread.Sleep(500);
                        retryCount++;
                    }
                }




            }
        }

        ///加载模型
        public static Database LoadWBlock(string blkFile)
        {
            //per 创建的数据库要读取的不是当前文档,database类的构造函数第一个是false,第二个是true
            Database blkDb = new Database(false, true);
            try
            {
                blkDb.ReadDwgFile(blkFile, System.IO.FileShare.Read, true, null);
                blkDb.CloseInput(true);
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(string.Format("LoadWBlock() Faild, file: {0}", blkFile));
                Palettes.WriteLog(ex.ToString());
                //Application.ShowAlertDialog(ex.ToString());
            }
            return blkDb;
        }


        //2 分析数据库中对象
        private void ParseNamingPoint(Database db)
        {
#if _DEBUG
            Palettes.WriteLog(string.Format("'{0}' ParseNamingPoint() start...", DrawingName));
#endif
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(db);
                    BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;

                    var tc = RXClass.GetClass(typeof(DBPoint));
                    var blkrefType = RXClass.GetClass(typeof(BlockReference));
                    foreach (ObjectId objId in btr)
                    {
                        if (objId.ObjectClass.Equals(tc))
                        {
                            GetNamingPoint(trans.GetObject(objId, OpenMode.ForRead) as DBPoint);
                        }
                        else if (objId.ObjectClass.Equals(blkrefType))
                        {
                            var blkRef = trans.GetObject(objId, OpenMode.ForRead) as BlockReference;
                            BlockTableRecord blkRec = trans.GetObject(blkRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                            foreach (ObjectId blkObjId in blkRec)
                            {
                                Entity blkEnt = trans.GetObject(blkObjId, OpenMode.ForRead) as Entity;
                                if (blkEnt is DBPoint)
                                {
                                    DBPoint point = blkEnt as DBPoint;
                                    GetNamingPoint(point);
                                }
                            }

                        }

                    }
                }
                catch (Exception ex)
                {
                    //"分析命名点错误：{0},错误发生在：{1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("ParsingNamedPointError"), ex.ToString(), ex.StackTrace), 2);
                }
            }
#if _DEBUG
            Palettes.WriteLog(string.Format("'{0}' ParseNamingPoint() end...", DrawingName));
#endif
        }

        ////判断是否存在指定快
        public BlockTableRecord GetBlockTableRecord(OpenMode openMode = OpenMode.ForWrite)
        {
            try
            {
                BlockTable bt = HisDB.TransactionManager.StartTransaction().GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                return HisDB.TransactionManager.StartTransaction().GetObject(bt[BlockTableRecord.ModelSpace], openMode) as BlockTableRecord;
            }
            catch (System.Exception)
            {
                return null;
            }
        }



        //判断是否存在指定块
        private BlockTableRecord GetBlockTableRecord(string blkName)
        {
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                {
                    foreach (ObjectId objId in bt)
                    {
                        BlockTableRecord btr1 = objId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                        if (string.Compare(btr1.Name, blkName, true) == 0)
                        {
                            btr = btr1;
                            break;
                        }
                    }
                }

                return btr;
            }
        }



        //1驱动动态块
        public void DriveDynamicBlock(ObjectId blkRefId)
        {
#if _DEBUG
            Palettes.WriteLog("DriveDynamicBlock() start...");
#endif
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                //DocumentLock docLock = AssemDoucument.LockDocument();

                try
                {
                    BlockReference blkRef = tran.GetObject(blkRefId, OpenMode.ForWrite) as BlockReference;  //报错
                    if (blkRef == null) return;

                    bool isModify = false;

                    //1 设置属性
                    for (int i = 0; i < blkRef.AttributeCollection.Count; i++)
                    {
                        AttributeReference ar = tran.GetObject(blkRef.AttributeCollection[i], OpenMode.ForWrite) as AttributeReference;
                        Function para = this.Functions.FirstOrDefault(e => e.OperObject == ar.Tag);
                        if (para != null)
                        {
                            ar.TextString = para.Value;

                            if (!isModify) isModify = true;
                        }
                    }

                    //2 设置动态属性
                    if (blkRef.IsDynamicBlock)
                    {
#if _DEBUG
                        Palettes.WriteLog(string.Format("PropertyCollection.Count = {0}", blkRef.DynamicBlockReferencePropertyCollection.Count));
#endif
                        foreach (DynamicBlockReferenceProperty it in blkRef.DynamicBlockReferencePropertyCollection)
                        {
                            string propname = it.PropertyName;
                            if (propname == "Origin") continue;

                            Function para = this.Functions.FirstOrDefault(e => e.OperObject == propname);
                            if (para != null)
                            {
#if _DEBUG
                                Palettes.WriteLog(string.Format("{0} setDynamicBlock {1}={2} ",this.DrawingName,propname,para.Value));
#endif
                                SetPropertyValue(it, para.Value);

                                if (!isModify) isModify = true;
                            }
#if _DEBUG
                            else Palettes.WriteLog(string.Format("{0} DynamicBlock {1} skip", this.DrawingName, propname));
#endif
                        }
                    }
                    //docLock.Dispose();

                    if (isModify) tran.Commit();
                }
                catch (Exception ex)
                {
                    //"设置属性错误：{0},错误发生在：{1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("SettingPropertyError"), ex.ToString(), ex.StackTrace), 2);
                }

            }
        }
        ///设置属性值
        public static void SetPropertyValue(DynamicBlockReferenceProperty prop, string value)
        {
            if (!(prop.Value is string) && string.IsNullOrWhiteSpace(value))
            {
                //"错误：参数'{0}'的值不能被设置为空，请检查参数。"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("ErrorParamCannotNull"), prop.PropertyName, value), 2);
                return;
            }

            try
            {
                double pi = 3.1415926;
                if (prop.Value is double)
                {
                    if (prop.UnitsType == DynamicBlockReferencePropertyUnitsType.Angular)
                    {
                        prop.Value = double.Parse(value) * pi / 180;
                    }
                    else
                    {
                        prop.Value = double.Parse(value);
                    }
                }
                else if (prop.Value is int)
                {
                    prop.Value = int.Parse(value);
                }
                else if (prop.Value is Int16)
                {
                    if (value == "已翻转")
                    {
                        prop.Value = (Int16)1;
                        return;
                    }
                    if (value == "未翻转")
                    {
                        prop.Value = (Int16)0;
                        return;
                    }
                    Int16 tempInt16;
                    if (!Int16.TryParse(value, out tempInt16))
                    {
                        //"格式错误 属性 {0} ，值 {1}"
                        Palettes.WriteLog(string.Format(LanguageHelper.GetString("FormatError_AttributeValue"), prop.PropertyName, value), 1);
                        return;
                    }
                    prop.Value = tempInt16;

                }
                else if (prop.Value is string)
                {
                    prop.Value = value;
                }
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
                //"参数:'{0}' 赋值: '{1}' 失败。"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("ParamAssignmentFailed"), prop.PropertyName, value), 2);
            }
        }


        //驱动模型
        private void DriveModelBak()
        {
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                ObjectIdCollection collect = new ObjectIdCollection();

                foreach (ObjectId objId in btr)
                {
                    collect.Add(objId);
                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference))))
                    {
                        DriveDynamicBlock(objId);

                        BlockReference blkRef = trans.GetObject(objId, OpenMode.ForWrite) as BlockReference;

                        this.Position = blkRef.Position;

                        blkRef.ExplodeToOwnerSpace();

                        blkRef.Erase(true);
                        if (blkRef.IsDynamicBlock)
                        {
                            BlockTableRecord dynBtr = trans.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForWrite) as BlockTableRecord;
                            dynBtr.Erase(true);
                        }

                    }
                }

                trans.Commit();
                //trans.Dispose();
            }
        }
        private void GetCacheModeBasePoint(Double lineStyleSacale)
        {
            List<ObjectId> lsId = GetDBPointIds();
            if (lsId.Count == 0) return;
            foreach (var id in lsId)
            {
                using (Transaction trans = HisDB.TransactionManager.StartTransaction())
                {
                    try
                    {
                        DBPoint cacheBasePoint = trans.GetObject(id, OpenMode.ForWrite) as DBPoint;
                        if (cacheBasePoint == null) return;
                        if (cacheBasePoint.LinetypeScale == lineStyleSacale)
                        {
                            this.position = cacheBasePoint.Position;
                        }
                    }
                    catch
                    {
                        Palettes.WriteLog("GetCacheModeBasePoint Failed", 2);

                    }

                    trans.Commit();
                }
            }

        }


        List<ObjectId> GetDBPointIds()
        {
            List<ObjectId> ls = new List<ObjectId>();
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId objId in btr)
                {

                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(DBPoint))))
                    {
                        ls.Add(objId);
                    }
                }
            }
            return ls;
        }

        private void SetCacheModeBasePoint()
        {

        }

        private void DriveModel(bool useCache)
        {
#if _DEBUG
            Palettes.WriteLog("DriveModel() start...");
#endif
            List<ObjectId> lsId = GetBlockReferenceIds();
            if (lsId.Count == 0) return;
            if (!useCache)
            {
                for (int i = 0; i < lsId.Count; i++)
                {
                    DriveDynamicBlock(lsId[i]);
                }
            }


            //Palettes.WriteLog("DimRegen command before...");
            //一次更新操作没效果，不知道啥原因
            /*Autodesk.AutoCADExt.AssocPoint.DimRegen(HisDB.UnmanagedObject);*/

            //Autodesk.AutoCADExt.AssocPoint.DimRegen(HisDB.UnmanagedObject);
            //Palettes.WriteLog("DimRegen command after...");

            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                for (int i = 0; i < lsId.Count; i++)
                {
                    //Palettes.WriteLog(string.Format("Block '{0}' explode and erase it", i+1));


                    BlockReference blkRef = trans.GetObject(lsId[i], OpenMode.ForWrite) as BlockReference;
                    this.Position = blkRef.Position;            //保存块的插入点
                    Palettes.WriteLog($"{this.DrawingName}+x{this.Position.X}++y{this.Position.Y}");
                    //per 在动态块基点的位置加一个新的标记点, 保存缓存模型后此点可以被获取到, 作为参照点
                    BlockTableRecord space = HisDB.CurrentSpaceId.GetObject(OpenMode.ForWrite) as BlockTableRecord;
                    if (Palettes.stationInfo!=null && Palettes.stationInfo.UseModelCache)
                    {
                        DBPoint cacheBasePoint = new DBPoint(blkRef.Position);
                        cacheBasePoint.LinetypeScale = 1.17;
                        space.AppendEntity(cacheBasePoint);
                        trans.AddNewlyCreatedDBObject(cacheBasePoint, true);
                    }
                    

                    if (Palettes.stationInfo != null)
                    {
                        if (Palettes.stationInfo.bMakeDRW)
                        {
                            if (Palettes.stationInfo.hasSheetPattern)
                            {
                                var objects = new DBObjectCollection();
                                blkRef.Explode(objects);
                                foreach (DBObject entity in objects)
                                {
                                    space.AppendEntity(entity as Entity);
                                    trans.AddNewlyCreatedDBObject(entity, true);
                                }
                            }
                            else
                            {
                                blkRef.ExplodeToOwnerSpace();
                            }
                            
                            //删除块引用，同时删除在块表中记录                        
                            if (blkRef.IsDynamicBlock)
                            {
                                //BlockTableRecord dynBtr = trans.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForWrite) as BlockTableRecord;
                                //dynBtr.Erase(true);
                            }
                            blkRef.Erase(true);
                        }

                    }
                    else //本地测试使用,无法获取此配置,先使用土建图的部分
                    {
                        //if (Palettes.stationInfo.hasSheetPattern)
                        //{
                        //    var objects = new DBObjectCollection();
                        //    blkRef.Explode(objects);
                        //    foreach (DBObject entity in objects)
                        //    {
                        //        space.AppendEntity(entity as Entity);
                        //        trans.AddNewlyCreatedDBObject(entity, true);
                        //    }
                        //}
                        //else
                        {
                            blkRef.ExplodeToOwnerSpace(); 
                        }
                       
                        //删除块引用，同时删除在块表中记录                        
                        if (blkRef.IsDynamicBlock)
                        {
                            //BlockTableRecord dynBtr = trans.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForWrite) as BlockTableRecord;
                            //dynBtr.Erase(true);
                        }
                        blkRef.Erase(true);
                    }
                    /*
                    //会使标注关联丢失,但是动态块中的阵列可以正常显示
                    var objects = new DBObjectCollection();
                    blkRef.Explode(objects);
                    foreach (DBObject entity in objects)
                    {
                        space.AppendEntity(entity as Entity);
                        trans.AddNewlyCreatedDBObject(entity, true);
                    }

                    //动态块中的阵列无法添加到摸型空间，标注关联还在
                    blkRef.ExplodeToOwnerSpace();*/



                }
                trans.Commit();
                //trans.Dispose();
            }

#if _DEBUG
            Palettes.WriteLog("DriveModel() end...");
#endif
        }

        List<ObjectId> GetBlockReferenceIds()
        {
            List<ObjectId> ls = new List<ObjectId>();

#if _DEBUG
            Palettes.WriteLog("GetBlockReferenceIds() start...");
#endif
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                //ObjectIdCollection collect = new ObjectIdCollection();

                foreach (ObjectId objId in btr)
                {

                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference))))
                    {
                        ls.Add(objId);
                    }
                }
            }
#if _DEBUG
            Palettes.WriteLog("GetBlockReferenceIds() end...");
#endif
            return ls;
        }


        private void RegenObjects()
        {
            Palettes.WriteLog("RegenObjects() start...");

            // Redraw the drawing
            //Application.UpdateScreen();
            AssemDoucument.Editor.UpdateScreen();

            // Regenerate the drawing
            AssemDoucument.Editor.Regen();
        }

        private void UpdateAllDimensions()
        {
            Autodesk.AutoCADExt.AssocPoint.DimRegen(HisDB.UnmanagedObject);
        }


        private bool CloneDynamicBlock(Database sourceDB, string blkName)
        {
            try
            {
                using (DocumentLock docLock = AssemDoucument.LockDocument())
                {
                    using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                    {
                        BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                        if (bt.Has(blkName)) return true;

                        using (Transaction tran1 = sourceDB.TransactionManager.StartTransaction())
                        {
                            BlockTable bt1 = tran1.GetObject(sourceDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                            if (!bt1.Has(blkName)) return false;

                            ObjectIdCollection collect = new ObjectIdCollection(new ObjectId[] { bt1[blkName] });

                            //当前块表记录
                            ObjectId curDBId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);
                            sourceDB.WblockCloneObjects(collect, curDBId, new IdMapping(), DuplicateRecordCloning.Replace, false);
                        }

                        tran.Commit();
                        //tran.Dispose();
                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
                //"克隆块{0}失败！"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("CloneBlkFailed") + ex.ToString(), blkName));
                return false;
            }

            return true;
        }


        //克隆子级对象
        private bool WblockChildNodeDB(Component rootComp)
        {
            try
            {
                //using (DocumentLock docLock = AssemDoucument.LockDocument())
                {
                    Database db1 = new Database();
                    using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                    {
                        //BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                        //Palettes.WriteLog("using(Transaction tran1 = rootComp.HisDB.TransactionManager.StartTransaction()) before...");
                        using (Transaction tran1 = rootComp.HisDB.TransactionManager.StartTransaction())
                        {
                            ObjectId childBtrId = SymbolUtilityServices.GetBlockModelSpaceId(rootComp.HisDB);

                            BlockTableRecord btr1 = tran1.GetObject(childBtrId, OpenMode.ForRead) as BlockTableRecord;
                            ObjectIdCollection collect = new ObjectIdCollection();

                            foreach (ObjectId objId in btr1)
                            {
                                collect.Add(objId);
                            }
#if _DEBUG
                            Palettes.WriteLog("child node Wblock() before...");
#endif
                            rootComp.HisDB.Wblock(db1, collect, rootComp.Position, DuplicateRecordCloning.Replace);


                            tran1.Commit();
                        }

                        //tran.Commit();
                        //tran.Dispose();
#if _DEBUG
                        Palettes.WriteLog("child node Dispose() before...");
#endif
                        //per 保存驱动完成的文件
                        //string fileName = Path.GetDirectoryName(HisDB.Filename) + "\\" + rootComp.DrawingName + ".dwg";
                        //rootComp.HisDB.SaveAs(fileName, DwgVersion.AC1015);
                        //per
                        rootComp.HisDB.Dispose();//per 子图纸驱动完成或者插入到大图中

                    }
 #if _DEBUG
                    Palettes.WriteLog("rootComp.HisDB = db1 before...");
#endif
                    rootComp.HisDB = db1;
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
                Palettes.WriteLog(ex.ToString());
                Palettes.WriteLog("Failed to assemble sub drawing" + rootComp.DrawingName);
                //模型文件存在打不开问题时, 会报错catch后返回fasle,之后的块装配要继续进行
                return false;
            }

            return true;
        }
        //驱动模型
        private void RebuildHatch()
        {
#if DEBUG
            Palettes.WriteLog("RebuildHatch() start...");
#endif
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForWrite) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(Hatch)))) continue;

                    Hatch hatch = trans.GetObject(objId, OpenMode.ForWrite) as Hatch;
                    if (hatch.NumberOfLoops == 0) continue;

                    ObjectIdCollection collect = new ObjectIdCollection();
                    Curve2dCollection curveCollect = new Curve2dCollection();
                    IntegerCollection intCollect = new IntegerCollection();

                    LineSegment2d lineSeg2d = null;
                    foreach (ObjectId hid in hatch.GetAssociatedObjectIds())
                    {
                        if (hid.ObjectClass.Equals(RXClass.GetClass(typeof(Polyline))))
                        {
                            Polyline pl = trans.GetObject(hid, OpenMode.ForRead) as Polyline;

                            for (int c = 0; c < pl.NumberOfVertices; c++)
                            {
                                lineSeg2d = null;
                                //Palettes.WriteLog(string.Format("pl.GetLineSegment2dAt({0}) before...", c));

                                try { lineSeg2d = pl.GetLineSegment2dAt(c); } catch (Exception ee) { }
                                if (lineSeg2d != null)
                                {
                                    curveCollect.Add(lineSeg2d);
                                    intCollect.Add(1);
                                }
                            }
                            collect.Add(hid);
                        }
                    }

                    HatchLoop loop = hatch.GetLoopAt(0);
                    var lt = loop.LoopType;

                    while (hatch.NumberOfLoops > 0) hatch.RemoveLoopAt(0);
                    hatch.AppendLoop(lt, curveCollect, intCollect);
                }

                trans.Commit();
                //trans.Dispose();
                //HisDB.SaveAs(string.Format(@"d:\temp\{0}.dwg", DrawingName), DwgVersion.Current);

            }
#if DEBUG
            Palettes.WriteLog("RebuildHatch() end...");
#endif
        }

        //装配下一级节点
        private ObjectId AssemChildBlock(Component rootComp, bool explode = false)
        {
            if (rootComp.HisDB == null) return ObjectId.Null;
#if _DEBUG
            Palettes.WriteLog("AssemChildBlock() start...");
            Palettes.WriteLog(string.Format("{0} insert child node '{1}'", DrawingName, rootComp.DrawingName));
#endif

            if (!WblockChildNodeDB(rootComp))
            {
                return ObjectId.Null;
            }

            ObjectId brefId = InsertChildNode(rootComp, explode);

            //RegenObjects();

            //Palettes.WriteLog(string.Format("'{0}' GetInstNumber before...", rootComp.InstanceName));
            string instNumber = "";
            int pos = rootComp.InstanceName.LastIndexOf('.');
            if (pos > 0)
            {
                instNumber = rootComp.InstanceName.Substring(pos);
                if (instNumber.Contains("-"))
                {
                    //"{0}： 实例名修改前 "
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("BeforeChangeName"), instNumber));
                    instNumber = instNumber.Substring(0, instNumber.IndexOf("-", StringComparison.Ordinal));  //去掉取号的影响
                    //"{0}： 实例名修改后 "
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("AfterChangeName"), instNumber));
                }
            }
            //Palettes.WriteLog(string.Format("instNumber = '{0}'", instNumber));

            //instNumber = instNumber.Replace('-', '.');

            //Palettes.WriteLog("DimensionAssoc() before...");
            DimensionAssoc(rootComp, brefId, instNumber);
#if _DEBUG
            Palettes.WriteLog("AssemChildBlock() end...");
#endif
            if ((Palettes.taskServer?.formCADTaskServer?.ServiceConfigInfo!=null &&Palettes.taskServer.formCADTaskServer.ServiceConfigInfo.bMakeDRW)|| Palettes.taskServer == null)
            {
                ExplodeBlockReference(brefId);//炸开为了支持跨层找命名点， 实现跨层标注
              
                using (Transaction trans = HisDB.TransactionManager.StartTransaction())
                {
                    BlockReference bref = trans.GetObject(brefId, OpenMode.ForWrite) as BlockReference;
                    bref.Erase();
                    trans.Commit();
                }
            }
           
            return brefId;
        }

        private void DimensionAssoc(Component rootComp, ObjectId brefId, string instName)
        {
            if (!brefId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference)))) return;

            //获取所有的尺寸, 和其对应的参照框, 给尺寸加一个后缀
            DimMgr dimMgr = new DimMgr(this.HisDB, false);//
            dimMgr.ParseDimDatum();
            dimMgr.AddDimDirection();
#if _DEBUG
            Palettes.WriteLog("DimensionAssoc() start...");
#endif
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                BlockReference bref = trans.GetObject(brefId, OpenMode.ForRead) as BlockReference;
                rootComp.BlockNewPosition = new Point3d(bref.Position.X, bref.Position.Y, 0);
                ChildBlockNamedPoints.AddRange(GetNamingPointFromBlockReference(bref));

#if _DEBUG
                string logText = string.Empty;
                foreach (var it in ChildBlockNamedPoints) { logText += it.Name + ";"; }
                Palettes.WriteLog(rootComp.DrawingName+" InnerPoints: " + logText);
#endif

                for (int i = 0; i < _lsNamingPoint.Count; i++)
                {
                    CADNamingPoint namePoint = ChildBlockNamedPoints.FirstOrDefault(e => (e.Name == (_lsNamingPoint[i].Name + instName))/* || ((e.Name + instName) == _lsNamingPoint[i].Name) */|| e.Name == _lsNamingPoint[i].Name);
                    if (namePoint == null) namePoint = FindPoint3dByName(rootComp, instName, _lsNamingPoint[i].Name);
                    else
                    {
#if _DEBUG
                        Palettes.WriteLog(string.Format("Match point name succ, c: '{0}' <---> p: '{1}'.", namePoint.Name, _lsNamingPoint[i].Name));
#endif
                    }
                    if (namePoint == null)
                    {
#if _DEBUG
                        Palettes.WriteLog(string.Format("Block: {1} Point: {0} don't need repos.", _lsNamingPoint[i].Name, DrawingName));
#endif
                        continue;
                    }

                    DBPoint pt = trans.GetObject(_lsNamingPoint[i].CADObjectId, OpenMode.ForWrite) as DBPoint;
                    var p = new Point3d(pt.Position.X, pt.Position.Y, 0);
                    pt.Position = namePoint.Point;
#if _DEBUG
                    Palettes.WriteLog(string.Format("Block: {4} Point: {0}, oldpos: ({5},{6},{7}), newpos: ({1},{2},{3}), childObjId: {8}, namedObjId: {9}.", _lsNamingPoint[i].Name
                        , namePoint.Point.X, namePoint.Point.Y, namePoint.Point.Z, DrawingName, p.X, p.Y, p.Z, namePoint.CADObjectId.ToString(), _lsNamingPoint[i].CADObjectId.ToString()));
#endif
                }

                trans.Commit();
                //trans.Dispose();
            }

            UpdateAllDimensions();
#if _DEBUG
            Palettes.WriteLog("DimensionAssoc() end...");
#endif
        }

        private CADNamingPoint FindPoint3dByName(Component rootComp, string instName, string destName)
        {
            if (rootComp == null || rootComp.ChildBlockNamedPoints == null) return null;
            if (destName.Contains("-")) destName = destName.Substring(0, destName.IndexOf('-'));

            if (rootComp.ChildBlockNamedPoints != null)
            {
                var a = rootComp.ChildBlockNamedPoints.FirstOrDefault(e => { var en = e.Name; if (en.Contains("-")) en = en.Substring(0, en.IndexOf('-')); return en == (destName + instName)/* || (en + instName) == destName*/ || en == destName; });
                if (a != null)
                {
                    var na = new CADNamingPoint() {
                        Name = a.Name,
                        BlockReferenceId = a.BlockReferenceId,
                        CADObjectId = a.CADObjectId,
                        IsInserted = a.IsInserted
                    };

#if _DEBUG
                    Palettes.WriteLog(string.Format("Match point name succ, c: '{0}' <---> p: '{1}' in FindPoint3dByName().", a.Name, destName));
#endif

                    na.Point = new Point3d(a.Point.X + rootComp.BlockNewPosition.X, a.Point.Y + rootComp.BlockNewPosition.Y, 0);
                    return na;
                }
                else
                {
                    // Palettes.WriteLog(string.Format("'{1}' in {0} ChildBlockNamedPoints is not match", rootComp.DrawingName, destName));
                }
            }
            else
            {
                Palettes.WriteLog(string.Format("{0} ChildBlockNamedPoints is null..", rootComp.DrawingName));
            }

            foreach (var tt in rootComp.ChildNodes)
            {
                var ret = FindPoint3dByName(tt, instName, destName);

                if (ret != null)
                {
                    ret.Point = new Point3d(ret.Point.X + tt.BlockNewPosition.X, ret.Point.Y + tt.BlockNewPosition.Y, 0);
                    return ret;
                }
            }

            return null;
        }

        private ObjectId InsertChildNode(Component rootComp, bool explode)
        {
            ObjectId blkRefId = ObjectId.Null;

            //using (DocumentLock dlock = AssemDoucument.LockDocument())
            {
                using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                {
#if _DEBUG
                    Palettes.WriteLog(string.Format( "{0} 插入块 {1}",this.DrawingName,rootComp.DrawingName));
#endif
                    //  ObjectId blkObjId = HisDB.Insert(rootComp.DrawingName, rootComp.HisDB, false);   //有问题
                    ObjectId blkObjId;
                    Point3d insetPoint = new Point3d();
                    double angle = 0;
                    CADNamingPoint np = GetInsetPoint(rootComp);
                    if (np != null)
                    {
                        np.IsInserted = true;
                        insetPoint = np.Point;
                        angle = Utility.AngleToRadian(rootComp.RotateAngle);
                    }
                    else
                    {
                        insetPoint = GetInsetPointAndAngle(rootComp, out angle) ?? new Point3d(0, 0, 0);
                    }

                    rootComp.InstanceName = rootComp.DrawingName;
                    blkObjId = HisDB.Insert(rootComp.DrawingName, rootComp.HisDB, false);         //块实例使用插入点名称

                    BlockReference bref = new BlockReference(insetPoint, blkObjId);
                    bref.Rotation = angle;

                    BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    blkRefId = btr.AppendEntity(bref);
                    tran.AddNewlyCreatedDBObject(bref, true);

                    bref.Dispose();
                    rootComp.HisDB.Dispose();

                    tran.Commit();

                    //tran.Dispose();
                }
            }
            return blkRefId;
        }

        private void DimensionTidy(ObjectId brefId)
        {
            if (!brefId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference)))) return;


            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                BlockReference bref = trans.GetObject(brefId, OpenMode.ForRead) as BlockReference;
                List<CADNamingPoint> lsPoint = GetNamingPointFromBlockReference(bref);

                for (int i = 0; i < _lsNamingPoint.Count; i++)
                {
                    CADNamingPoint namePoint = lsPoint.Where(e => e.Name == _lsNamingPoint[i].Name).FirstOrDefault();
                    if (namePoint == null) continue;

                    DBPoint pt = trans.GetObject(_lsNamingPoint[i].CADObjectId, OpenMode.ForWrite) as DBPoint;
                    //DBPoint pt1 = trans.GetObject(_lsNamingPoint[i].CADObjectId, OpenMode.ForRead) as DBPoint;
                    // DBPoint pt = trans.GetObject(namePoint.CADObjectId, OpenMode.ForWrite) as DBPoint;
                    pt.Position = namePoint.Point;

                    // pt.Close();
                }

                trans.Commit();
                //trans.Dispose();

            }

            UpdateAllDimensions();
        }


        //获得块引用中的命名点
        private List<CADNamingPoint> GetNamingPointFromBlockReference(BlockReference bref)
        {
            List<CADNamingPoint> lsPoint = new List<CADNamingPoint>();

            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = trans.GetObject(bref.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId objId in btr)
                {
                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(DBPoint))))
                    {
                        DBPoint pt = trans.GetObject(objId, OpenMode.ForRead) as DBPoint;
                        CADNamingPoint namePoint = CADCommon.GetNamingPoint(pt);
                        if (namePoint != null)
                        {
                            namePoint.BlockReferenceId = bref.Id;
                            namePoint.Point = new Point3d(namePoint.Point.X + bref.Position.X, namePoint.Point.Y + +bref.Position.Y, 0);
                            lsPoint.Add(namePoint);
                        }
                    }
                }
                trans.Commit();
                //trans.Dispose();
            }

            return lsPoint;

        }



        //获得子级节点的插入点
        private CADNamingPoint GetInsetPoint(Component component)
        {

            var find = this.Functions.FirstOrDefault(p => p.Name == "SetBlock" && p.Value == component.DrawingName);
            if (find != null)
            {
                //解决同一块重复插入问题
                this.Functions.Remove(find);
                var findPoint = _lsNamingPoint.FirstOrDefault(p => p.Name == find.OperObject);
                return findPoint;
            }
            return null;
        }

        //获得子级节点的插入点
        private Point3d? GetInsetPointAndAngle(Component component, out double angle)
        {

            var find = this.Functions.FirstOrDefault(p => p.Name == "SetBlock" && p.Value.Contains(component.DrawingName) && p.OperObject == "basepoint");
            if (find != null)
            {
                //解决同一块重复插入问题
                this.Functions.Remove(find);
                string[] parts = find.Value.Split('|');
                if (parts.Length >= 4)
                {
                    double posx = double.Parse(parts[1]);
                    double posy = double.Parse(parts[2]);
                    angle = double.Parse(parts[3]);
                    Point3d findPoint = new Point3d(posx, posy, 0);
                    return findPoint;
                }

            }
            angle = 0;
            return null;
        }
        //获得子级节点的插入点
        private CADNamingPoint GetInsetPointBak(Component component)
        {
            for (int i = 0; i < _lsNamingPoint.Count; i++)
            {
                if (_lsNamingPoint[i].IsInserted) continue;

                string s1 = _lsNamingPoint[i].Name;
                int pos = s1.LastIndexOf('.');
                if (pos > 0)
                {
                    s1 = s1.Substring(0, pos);
                }

                if (component.DrawingName.EndsWith(s1))
                {

                    return _lsNamingPoint[i];
                }
            }
            return null;
        }


        //插入一个图块,从当前数据库
        private void InsertBlock(string blkName, Point3d point)
        {
            BlockTableRecord btr = GetBlockTableRecord(blkName);

            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {

                BlockReference bref = new BlockReference(point, btr.ObjectId);


                BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                btr.AppendEntity(bref);
                tran.AddNewlyCreatedDBObject(bref, true);
                bref.Dispose();

                tran.Commit();
            }
        }

        private string GetDynamicBlockName(ObjectId blkRefId)
        {
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                BlockReference blkRef = tran.GetObject(blkRefId, OpenMode.ForRead) as BlockReference;
                if (blkRef == null) return "";

                if (blkRef.IsDynamicBlock)
                {
                    BlockTableRecord btr = tran.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    return btr.Name;
                }
                return "";
            }
        }



        //获得图形中的块引用、命名点
        private void GetNamingPoint(DBPoint pt)
        {
            if (!pt.Visible) return;
            ResultBuffer rb = pt.XData;
            if (rb == null) return;

            foreach (TypedValue tv in rb)
            {
                if (tv.TypeCode == (int)DxfCode.ExtendedDataAsciiString
                    && tv.Value != null && !string.IsNullOrEmpty(tv.Value.ToString()))
                {
                    CADNamingPoint namingPoint = new CADNamingPoint();
                    namingPoint.Point = pt.Position;
                    namingPoint.Name = tv.Value.ToString();
                    namingPoint.CADObjectId = pt.ObjectId;
#if _DEBUG
                    Palettes.WriteLog(string.Format("获取尺寸命名点信息 : {0}  名字:{1}，ID:{2}", namingPoint.Point, namingPoint.Name, namingPoint.CADObjectId));
#endif
                    _lsNamingPoint.Add(namingPoint);

                    break;
                }
            }
            //rb.Dispose();
        }

        /* private static ServiceReferenceCAD.CADTaskServiceClient GetTaskServer()
         {
             var plugInpath = System.IO.Path.GetDirectoryName(typeof(Component).Assembly.CodeBase.Replace("file:///", ""));

             string cadPath= Path.Combine(plugInpath, "CADWSAddin.dll");

             var config = ConfigurationManager.OpenExeConfiguration(cadPath);
             var settings = config.AppSettings.Settings;
             string endPointAddress = settings["CADTaskService"].Value;
             //安全性 none  是否启用可靠会话 false
             NetTcpBinding binding = new NetTcpBinding(SecurityMode.None, false)
             {
                 ReceiveTimeout = new TimeSpan(0, 10, 10),
                 TransferMode = TransferMode.Streamed,///传输方式
                 MaxReceivedMessageSize = 1073741824,
                 SendTimeout = new TimeSpan(0, 10, 0)

             };
             binding.ReaderQuotas = new XmlDictionaryReaderQuotas
             {
                 MaxArrayLength = 1073741824

             };


             var endPoint = new EndpointAddress(endPointAddress);

             var taskService = new CADWSAddin.ServiceReferenceCAD.CADTaskServiceClient(binding, endPoint);
             return taskService;
         }*/
        public static byte[] GetDrawing(string partNO)
        {
            /*string drawingNo = GetDrawingNO(partNO);
            var find = CADServiceClient.GetCurrentModelFile(drawingNo.TrimStart(new char[]{'$'}), null, "RD", false, "dwg", false);
            if (find != null)
                return find.FileContent;*/
            return null;

        }
        private static string GetDrawingNO(string partNO)
        {
            char splitKey = '.';
            if (partNO.Contains(splitKey))
            {
                return partNO.Split(splitKey)[0];
            }
            return partNO;
        }
        /* private static CADTaskServiceClient _CADServiceClient;
         public static CADTaskServiceClient CADServiceClient
         {
             get
             {
                 if (_CADServiceClient == null)
                 {
                     _CADServiceClient = GetTaskServer();

                 }
                 return _CADServiceClient;
             }

         }*/




        /// //////////////////////////////////////////////////////////// 

        //private string SkeletonName = null;
        public List<Parameter> Parameters = new List<Parameter>();
        public List<Function> Functions = new List<Function>();
        public List<Component> ChildNodes = new List<Component>();

        public void AddParameter(Parameter p)
        {
            Parameters.Add(p);
        }

        public void AddFunction(Function f)
        {
            Functions.Add(f);
        }

        public void AddChildNode(Component child)
        {
            ChildNodes.Add(child);
            child.SetParent(this);
        }


        public void SetParent(Component p)
        {
            Parent = p;
        }

        public void DeleteRefDatabase()
        {
            for (int i = 0; i < ChildNodes.Count(); i++)
            {
                ChildNodes[i].DeleteDatabase();
            }
        }

        private void DeleteDatabase()
        {
            try
            {
                if (_database != null)
                {
                    _database.Dispose();
                    _database = null;
                }
                for (int i = 0; i < ChildNodes.Count(); i++)
                {
                    ChildNodes[i].DeleteDatabase();
                }
            }
            catch (System.AccessViolationException ex)
            {
            }
        }


        public Parameter GetParameter(string para)
        {
            for (int i = 0; i < Parameters.Count(); i++)
            {
                if (para == Parameters[i].Name)
                    return Parameters[i];
            }
            return null;
        }

        //private bool InitCADDocument()
        //{
        //    if (string.IsNullOrEmpty(ModelName))
        //    {
        //        CADDocument = DocumentMgr.NewDocument();
        //    }
        //    else
        //    {
        //        CADDocument = DocumentMgr.OpenDocument(ModelName,false);
        //    }

        //    return CADDocument != null;
        //}

        //把子件的数据库作为块插入到父中

        public static Component ParseTaskFile(XDocument xdoc)
        {
            //if (!File.Exists(xmlFile))
            //    return null;

            Component rootComp = new Component();

            //XDocument xdoc = XDocument.Load(xmlFile);
            XElement xele = xdoc.Element("Node");
            ParseNodeXML(rootComp, xele);

            return rootComp;
        }


        private static void ParseNodeXML(Component comp, XElement xele)
        {
            XElement e1 = xele.Element("ModelName");
            comp.ModelName = e1 != null ? e1.Value : "";

            XElement ef = xele.Element("ModeDefault");
            comp.ModeDefault = ef != null ? ef.Value : "";

            XElement e2 = xele.Element("BlockName");
            comp.BlockName = e2 != null ? e2.Value : "";

            XElement dnX = xele.Element("DrawingName");
            comp.DrawingName = dnX != null ? dnX.Value : "";

            XElement dxfX = xele.Element("DxfName");
            comp.DxfName = dxfX != null ? dxfX.Value : "";

            var rotateAngleNode = xele.Element("RotateAngle");
            comp.RotateAngle = rotateAngleNode == null ? 0 : Utility.ConvertToDouble(rotateAngleNode.Value);
            //添加partid方便与任务号中的partid进行对应
            XElement partIDInfo = xele.Element("PartId");
            comp.PartId = partIDInfo != null ? int.Parse(partIDInfo.Value) : 0;

            XElement e3 = xele.Element("Parameters");
            foreach (XElement e in e3.Elements())
            {
                Parameter p = new Parameter();
                p.ParseXML(e);
                comp.AddParameter(p);
            }

            XElement e4 = xele.Element("Functions");
            foreach (XElement e in e4.Elements())
            {
                Function f = new Function();
                f.ParseXML(e);
                comp.AddFunction(f);
            }

            XElement e5 = xele.Element("ChildNodes");
            foreach (XElement e in e5.Elements())
            {
                Component child = new Component();
                ParseNodeXML(child, e);
                comp.AddChildNode(child);
            }
            //poc纸张的大小
            XElement xPaperW = xele.Element("PaperSizeWidth");
            comp.PaperSizeWidth = xPaperW != null ? double.Parse(xPaperW.Value) : 0;
            XElement xPaperH = xele.Element("PaperSizeHeight");
            comp.PaperSizeHeight = xPaperH != null ? double.Parse(xPaperH.Value) : 0;
            XElement noMerage = xele.Element("NoMerge");
            if (noMerage != null)
            {
                comp.NoMerage = noMerage.Value == "true";
            }


        }

        public string DxfName { get; set; }


        //尺寸绘图次序上移
        public void MoveDimOrderUp()
        {
            try
            {
                using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btr = null;
                    BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                    btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                    ObjectIdCollection collection = new ObjectIdCollection();
                    foreach (ObjectId objId in btr)
                    {
                        if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension))))
                        {
                            collection.Add(objId);
                        }
                        else
                        {
                            continue;
                        }
                    }
                    Palettes.WriteLog("MoveDimOrderUp Count is" + collection.Count);

                    if (collection.Count > 0)
                    {
                        DrawOrderTable orderTable = tran.GetObject(btr.DrawOrderTableId, OpenMode.ForWrite) as DrawOrderTable;
                        orderTable.MoveToTop(collection);
                    }
                    tran.Commit();
                }


            }
            catch (Exception ex)
            {
                //"尺寸上移错误"
                Palettes.WriteLog("MoveDimOrderUp Error  " + ex.Message, 2);
            }
        }

        public bool PurgeItems()
        {
            
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            // 收集所有需要清理的符号表
            ObjectIdCollection tableIds = new ObjectIdCollection
            {
            db.BlockTableId,
            db.LayerTableId,
            db.LinetypeTableId,
            db.TextStyleTableId,
            db.DimStyleTableId,
            db.RegAppTableId,
            db.UcsTableId,
            db.ViewTableId,
            db.ViewportTableId
        };

            bool itemsPurged = false;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                bool keepPurging = true;
                while (keepPurging)
                {
                    ObjectIdCollection purgeableIds = new ObjectIdCollection();
                    foreach (ObjectId tableId in tableIds)
                    {
                        if (tableId.IsErased || !tableId.IsValid)
                            continue;
                        SymbolTable table = (SymbolTable)tr.GetObject(tableId, OpenMode.ForRead, false);
                        foreach (ObjectId recordId in table)
                        {
                            if (recordId.IsErased || !recordId.IsValid)
                                continue;
                            SymbolTableRecord record = (SymbolTableRecord)tr.GetObject(recordId, OpenMode.ForRead, false);
                            purgeableIds.Add(recordId);
                        }
                    }
                    db.Purge(purgeableIds);
                    if (purgeableIds.Count > 0)
                    {
                        itemsPurged = true;
                        keepPurging = true; // 继续清理可能有嵌套对象

                        foreach (ObjectId id in purgeableIds)
                        {
                            if (id.IsErased || !id.IsValid) continue;
                            SymbolTableRecord record = (SymbolTableRecord)tr.GetObject(id, OpenMode.ForWrite);
                            string recordName = record.Name;
                            try
                            {
                                record.Erase();
                            }
                            catch
                            {
                                // 忽略删除失败的对象
                            }
                        }
                    }
                    else
                    {
                        keepPurging = false;
                    }
                }
                tr.Commit();
            }
            return itemsPurged;
        }

        public void AddDrawingBorder()
        {
            using (var docLock = AssemDoucument.LockDocument())
            {
                using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                {

                    var bt = (BlockTable)tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite);
                    var paperSizeWidth = this.PaperSizeWidth;
                    var paperSizeHeight = this.PaperSizeHeight;
                    if (paperSizeWidth > 0 && paperSizeHeight > 0)
                    {
                        Polyline3d acPoly3d = new Polyline3d();
                        acPoly3d.ColorIndex = 5;
                        btr.AppendEntity(acPoly3d);
                        tran.AddNewlyCreatedDBObject(acPoly3d, true);
                        Point3dCollection acPts3dPoly = new Point3dCollection();
                        acPts3dPoly.Add(new Point3d(0, 0, 0));
                        acPts3dPoly.Add(new Point3d(paperSizeWidth, 0, 0));
                        acPts3dPoly.Add(new Point3d(paperSizeWidth, paperSizeHeight, 0));
                        acPts3dPoly.Add(new Point3d(0, paperSizeHeight, 0));
                        foreach (Point3d acPt3d in acPts3dPoly)
                        {
                            PolylineVertex3d acPolVer3d = new PolylineVertex3d(acPt3d);
                            acPoly3d.AppendVertex(acPolVer3d);
                            tran.AddNewlyCreatedDBObject(acPolVer3d, true);
                        }
                        acPoly3d.Closed = true;

                        tran.Commit();
                    }
                    else
                    {
                        tran.Dispose();
                    }


                }
            }

        }


        private void EraseDimNamingPoint()
        {
            if (Palettes.IniConfigData.IsHoldOnDimenPoints) return;

#if _DEBUG
            Palettes.WriteLog(string.Format("'{0}' EraseDimNamingPoint() start...", DrawingName));
#endif
            try
            {
                using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btr = null;
                    BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;

                    btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    foreach (ObjectId objId in btr)
                    {
                        if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) continue;

                        RotatedDimension dim = tran.GetObject(objId, OpenMode.ForRead) as RotatedDimension;

#if _DEBUG
                        Palettes.WriteLog(string.Format("获取标注：{0},{1}", dim.DimensionText, dim.Measurement));
#endif
                        List<long> lsIds = Autodesk.AutoCADExt.AssocPoint.GetDimAssocs(dim.UnmanagedObject);
#if _DEBUG
                        Palettes.WriteLog(string.Format("结束获取标注：{0},{1}", dim.DimensionText, dim.Measurement));
#endif
                        for (int i = 0; i < lsIds.Count; i++)
                        {
                            DBPoint point = tran.GetObject(new ObjectId(new IntPtr(lsIds[i])), OpenMode.ForWrite) as DBPoint;
                            if (point != null)
                            {
                                point.Erase(true);
                            }
                        }

                    }

                    tran.Commit();
                    //tran.Dispose();
                }
            }
            catch (Exception ex)
            {
                //"擦拭点错误：{0}"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("WipePointError"), ex.ToString()), 2);
            }

#if _DEBUG
            Palettes.WriteLog(string.Format("'{0}' EraseDimNamingPoint() end...", DrawingName));
#endif
        }

        private void RenamePointObjectIfNeed()
        {
            if (!DrawingName.Contains('.') || Palettes.IniConfigData.IsNoRenamePoints) return;

            bool succ = false;
            string name = string.Empty;

            var pos = DrawingName.LastIndexOf('.');
            var ext = DrawingName.Substring(pos);
            if (ext.Contains("-")) ext = ext.Substring(0, ext.IndexOf('-'));

            if (!Palettes.IniConfigData.IsFuzzyMatchPointName
                && (ext.Length != 2 || ext[ext.Length - 1] < 'A' || ext[ext.Length - 1] > 'Z'))
            {
                Palettes.WriteLog(string.Format("Param name err in RenamePoints(), BlockName: '{0}'. it's must same as 'xxx.A' or 'xxx.A-001'.", DrawingName));
                return;
            }

            using (var trans = HisDB.TransactionManager.StartTransaction())
            {
                //Palettes.WriteLog("////////////////////////////////////////////////////");
                foreach (var it in _lsNamingPoint)
                {
                    try
                    {
                        DBPoint pt = trans.GetObject(it.CADObjectId, OpenMode.ForWrite) as DBPoint;
                        if (pt == null) continue;

                        using (var nbr = new ResultBuffer())
                        {
                            foreach (var xd in pt.XData)
                            {
                                if (xd.TypeCode == (int)DxfCode.ExtendedDataAsciiString && xd.Value != null && !string.IsNullOrWhiteSpace(xd.Value.ToString()))
                                {
                                    name = xd.Value.ToString();
#if _DEBUG
                                   Palettes.WriteLog(string.Format("Point name: {0} -> {1}.", name, name + ext));
#endif
                                    nbr.Add(new TypedValue(xd.TypeCode, name + ext));
                                }
                                else
                                {
                                    nbr.Add(new TypedValue(xd.TypeCode, xd.Value));
                                }
                            }

                            pt.XData = nbr;
                            if (!succ) succ = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        //Palettes.WriteLog(ex.Message);
                    }
                }

                if (succ) trans.Commit();
            }
        }
        private void DeleteInvisibleEnt()
        {
            Document doc = Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            Database db = doc.Database;
            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    TypedValue[] filListPol = new TypedValue[1];
                    filListPol[0] = new TypedValue(0, "*");
                    SelectionFilter filterPol = new SelectionFilter(filListPol);
                    PromptSelectionResult resPol = ed.SelectAll(filterPol);
                    resPol = ed.SelectAll(filterPol);
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(db.CurrentSpaceId, OpenMode.ForWrite);
                    if (resPol.Status == PromptStatus.OK)
                    {
                        foreach (ObjectId id in resPol.Value.GetObjectIds())
                        {
                            Entity entity = trans.GetObject(id, OpenMode.ForWrite) as Entity;
                            if (!entity.Visible)
                            {
                                //entity.Visible = true;
                                entity.Erase();
                            }
                        }
                    }
                    trans.Commit();
                    trans.Dispose();
                }
            }

        }
    }

    class HatchLoopInfo
    {
        public HatchLoopTypes HatchLoopType { get; set; }
        public Point2dCollection Points { get; set; }
        public DoubleCollection Bulges { get; set; }
    }
    
}
