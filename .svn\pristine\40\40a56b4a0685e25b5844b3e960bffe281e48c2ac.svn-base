﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Autodesk.AutoCAD.DatabaseServices;

namespace CADWSAddin
{
    public partial class FormDwgInfo : Form
    {
        public FormDwgInfo()
        {
            InitializeComponent();
        }

        private void buttonOpenfile_Click(object sender, EventArgs e)
        {
            if (DialogResult.OK == openFileDialog1.ShowDialog())
            {
                this.textBox1.Text = openFileDialog1.FileName;
            }
        }

        private void buttonDim_Click(object sender, EventArgs e)
        {
            DwgInfo dInfo = new DwgInfo(this.textBox1.Text);
            List<string> r = dInfo.GetALLZXVariable();
            foreach (var item in r)
            {
                this.richTextBox1.AppendText(item + "\r\n");
            }
          
        }

        private void FormDwgInfo_Load(object sender, EventArgs e)
        {

        }
    }
}
