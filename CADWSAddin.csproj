﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{ABAA54D2-07FC-487B-874B-8BB7F4FEBAED}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CADWSAddin</RootNamespace>
    <AssemblyName>CADWSAddin</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AcCoreMgd, Version=20.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\000CADStationVersion\CADdll\2018\AcCoreMgd.dll</HintPath>
    </Reference>
    <Reference Include="acdbmgd, Version=22.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\000CADStationVersion\CADdll\2015\acdbmgd.dll</HintPath>
    </Reference>
    <Reference Include="AcMgd, Version=20.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\000CADStationVersion\CADdll\2018\AcMgd.dll</HintPath>
    </Reference>
    <Reference Include="Autodesk.AutoCAD.Interop.Common">
      <HintPath>..\..\..\..\..\..\..\000CADStationVersion\CADdll\2018\Autodesk.AutoCAD.Interop.Common.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>2015dll\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <Private>False</Private>
    </Reference>
    <Reference Include="OPMNetExt, Version=1.0.6483.29450, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\000CADStationVersion\CADdll\2018\OPMNetExt.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore">
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BlockExp.cs" />
    <Compile Include="BlockNode.cs" />
    <Compile Include="CADCommon.cs" />
    <Compile Include="CADNamingPoint.cs" />
    <Compile Include="CAD操作\DatabaseOper.cs" />
    <Compile Include="CAD操作\DimMgr.cs" />
    <Compile Include="CAD操作\DocOper.cs" />
    <Compile Include="CAD操作\DrawingSpace.cs" />
    <Compile Include="CAD操作\DwgCombiner.cs" />
    <Compile Include="CAD操作\ExecuteContext.cs" />
    <Compile Include="CAD操作\PDFCombiner.cs" />
    <Compile Include="CAD操作\TableOper.cs" />
    <Compile Include="CAD操作\TextOper.cs" />
    <Compile Include="CAD操作\TextOperFP.cs" />
    <Compile Include="CAD操作\Utils.cs" />
    <Compile Include="CAD操作\XmlCADNode.cs" />
    <Compile Include="CAD操作\XmlOpers.cs" />
    <Compile Include="TaskProcess\DwgToPdfTaskProcess.cs" />
    <Compile Include="PlotingPDF.cs" />
    <Compile Include="DelHideLayer.cs" />
    <Compile Include="CommonData.cs" />
    <Compile Include="Component.cs" />
    <Compile Include="DocumentMgr.cs" />
    <Compile Include="DwgInfo.cs" />
    <Compile Include="FormDwgInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormDwgInfo.Designer.cs">
      <DependentUpon>FormDwgInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="FormInput.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormInput.Designer.cs">
      <DependentUpon>FormInput.cs</DependentUpon>
    </Compile>
    <Compile Include="Palettes.cs" />
    <Compile Include="Tools\BlockTools.cs" />
    <Compile Include="Tools\CommandTools.cs" />
    <Compile Include="Tools\COMTools.cs" />
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="cad3dproe.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\AutoUpdateClient.exe.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\Zxtech.PdsCadWsOutsideControl.exe.config" />
    <Compile Include="Tools\MyDimension.cs" />
    <Compile Include="Tools\DimStyleTools.cs" />
    <Compile Include="Tools\DimTools.cs" />
    <Compile Include="Tools\GeTools.cs" />
    <Compile Include="Tools\LinqToCAD.cs" />
    <Compile Include="Tools\ListTools.cs" />
    <Compile Include="LoadWSAddin.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TaskMgr.cs" />
    <Compile Include="Tools\PlotTools.cs" />
    <Compile Include="Tools\SingleQuoteStringParser.cs" />
    <Compile Include="Tools\TextTools.cs" />
    <Compile Include="Tools\Tools.cs" />
    <Compile Include="Tools\UCDockDrag.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Tools\UCDockDrag.Designer.cs">
      <DependentUpon>UCDockDrag.cs</DependentUpon>
    </Compile>
    <Compile Include="Tools\UCTreeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Tools\UCTreeView.Designer.cs">
      <DependentUpon>UCTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Utility.cs" />
    <Compile Include="WSCommand.cs" />
    <Compile Include="Tools\XDataTools.cs" />
    <None Include="NostandardDim.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="CADStationConfig.xml">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="CADTaskClientConfig.xml">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="CADTaskServerConfig.xml">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="升级指引.txt" />
    <None Include="modeltask.xml">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="ConfigBak.xml" />
    <Content Include="config\AutoUpdateClient.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\DeleteProcess.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\DevComponents.DotNetBar2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\PdsCadWsOutsideControl.Xml" />
    <Content Include="config\PdsClientConfig.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\PdsClientConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\Zxtech.CADTaskServerConfig.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="config\Zxtech.PdsCadWsOutsideControl.exe" />
    <Content Include="dimtest.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="dimtest1.xml">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Image\acad.ico" />
    <Content Include="Image\cadworkstation.ico" />
    <Content Include="Image\CADworkstationConfig.ico" />
    <Content Include="Image\door.ico" />
    <Content Include="Image\window.ico" />
    <Content Include="PdsClientConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TextFile1.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="texttest.xml">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FormDwgInfo.resx">
      <DependentUpon>FormDwgInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormInput.resx">
      <DependentUpon>FormInput.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Tools\UCDockDrag.resx">
      <DependentUpon>UCDockDrag.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Tools\UCTreeView.resx">
      <DependentUpon>UCTreeView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CADTaskServer.Contract\CADTaskServer.Contract.csproj">
      <Project>{D3E2FE3E-7FC3-496F-B6C8-8DB042657EC2}</Project>
      <Name>CADTaskServer.Contract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\CADTaskServer\CADTaskServer.csproj">
      <Project>{AEEB3226-313F-44C6-BDF6-535D42D62363}</Project>
      <Name>CADTaskServer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Neuxa.EDS.ExcelAddIn.LanguagePackage\LanguagePackage.csproj">
      <Project>{8C3761FA-F0E9-4671-9327-F02D7105EF06}</Project>
      <Name>LanguagePackage</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\PdsConstant\PdsConstant.csproj">
      <Project>{4d7c99d1-729c-403e-8330-ccf434221cfd}</Project>
      <Name>PdsConstant</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\PlugInSetup\PlugInSetup.csproj">
      <Project>{bd923277-a4f6-4381-9dba-c9384281c878}</Project>
      <Name>PlugInSetup</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Zxtech.EGI.PlatformService.Contract\Zxtech.EGI.PlatformService.Contract.csproj">
      <Project>{f0ca42e2-4993-4406-bfa6-f0886f7fddf9}</Project>
      <Name>Zxtech.EGI.PlatformService.Contract</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Command\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>