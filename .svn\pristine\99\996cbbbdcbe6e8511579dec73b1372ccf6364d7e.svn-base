﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using System.IO;


namespace CADWSAddin
{
    public class DocumentMgr
    {
        public static void ColseAll()
        {
            if (Application.DocumentManager.Count == 0)
                return;
            List<Document> ls = new List<Document>();

            System.Collections.IEnumerator IEnu = Application.DocumentManager.GetEnumerator();
            while (IEnu.MoveNext())
            {
                ls.Add((Document)IEnu.Current);
            }

            for (int i = 0; i < ls.Count; i++)
            {
               ls[i].CloseAndDiscard();
            }
        }

        public static void ColseAllExceptCurrent()
        {
            if (Application.DocumentManager.Count == 0)
                return;
            List<Document> ls = new List<Document>();

            System.Collections.IEnumerator IEnu = Application.DocumentManager.GetEnumerator();
            var currentDoc = Application.DocumentManager.MdiActiveDocument;
            while (IEnu.MoveNext())
            {
                ls.Add((Document)IEnu.Current);
            }

            for (int i = 0; i < ls.Count; i++)
            {
                if (ls[i].Name != currentDoc.Name)
                {
                    ls[i].CloseAndDiscard();
                }
                
            }
        }


            public static Document OpenDocument(string file, bool onlyRead = true)
        {
           // Document doc = Application.DocumentManager.Open(file, onlyRead);
            Document doc = null;
            if (doc == null) return null;

            Application.DocumentManager.MdiActiveDocument = doc;
            return doc;
        }

        static public void ActivateDocument(Document doc)
        {
            Application.DocumentManager.MdiActiveDocument = doc;
        }

        public void CloseCurDocument()
        {
            Document doc = GetActivateDocument();
            doc.CloseAndDiscard();
        }

        public static Document GetActivateDocument()
        {
            return Application.DocumentManager.MdiActiveDocument;
        }

        public static void CloseDocument(string name)
        {
            if (Application.DocumentManager.Count == 0)
                return;

            System.Collections.IEnumerator IEnu = Application.DocumentManager.GetEnumerator();
            while (IEnu.MoveNext())
            {
                Document doc = (Document)IEnu.Current;
                if (string.Compare(doc.Name, name, true) == 0)
                {
                   doc.CloseAndDiscard();
                    break;
                }
            }
        }


        static public List<Document> GetAllDocuments()
        {
            List<Document> ls = new List<Document>();
            if (Application.DocumentManager.Count == 0)
                return ls;

            System.Collections.IEnumerator IEnu = Application.DocumentManager.GetEnumerator();
            while (IEnu.MoveNext())
            {
                Document doc = (Document)IEnu.Current;
                ls.Add(doc);
            }
            return ls;
        }

        public static Document NewDocument()
        {
            string docTemp = "acad.dwt";
           // return Application.DocumentManager.Add(docTemp);
            return null;
        }


        public static void SaveDocument(string newName = "", string ver = "")
        {
            if (!string.IsNullOrEmpty(newName))
            {
                string path = Path.GetDirectoryName(newName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }

            Document doc = GetActivateDocument();
            //using (DocumentLock doclock = doc.LockDocument())
            //{
            //    using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            //    {


                    //AC1027 AutoCAD 2013
                    //AC1024 AutoCAD 2012
                    //AC1024 AutoCAD 2011
                    //AC1024 AutoCAD 2010
                    //AC1021 AutoCAD 2007/2008/2009
                    //AC1018 AutoCAD 2004/2005/2006
                    //AC1015 AutoCAD 2000/2000i/2002

                    DwgVersion ver1;
                    switch (ver)
                    {
                        case "2000":
                            ver1 = DwgVersion.AC1015;
                            break;
                        case "2004":
                        case "2005":
                        case "2006":
                            ver1 = DwgVersion.AC1800;
                            break;
                        case "2007":
                        case "2008":
                        case "2009":
                            ver1 = DwgVersion.AC1021;
                            break;
                        case "2010":
                        case "2011":
                        case "2012":
                            ver1 = DwgVersion.AC1024;
                            break;
                        case "2013":
                        case "2014":
                        case "2015":
                        case "2016":
                            ver1 = DwgVersion.AC1027;
                            break;
                        default:
                           // ver1 = DwgVersion.Current;
                            ver1 = DwgVersion.AC1024;
                            break;
                    }

                    doc.Database.SaveAs(newName, ver1);
                  //  trans.Commit();
               // }
            }

        }


    
}
