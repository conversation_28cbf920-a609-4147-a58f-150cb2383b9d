﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.ApplicationServices;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;


namespace CADWSAddin
{
    public class TextOperFP : TextOper
    {
        private List<Parameter> _lsPara = null;
        bool _format = true;


        public TextOperFP(bool f, List<Parameter> ls)
            : base(f)
        {
            _lsPara = ls;
            _format = f;
        }

        public override void FormatText(object ent)
        {
            try
            {
                if (_lsPara == null || _lsPara.Count() == 0) return;

                if (ent is DBText)
                {
                    FormatText(ent as DBText);
                }
                else if (ent is MText)
                {
                    FormatText(ent as MText);
                }
                else if (ent is Dimension)
                {
                    FormatText(ent as Dimension);
                }
                else if (ent is FeatureControlFrame)
                {
                    FormatText(ent as FeatureControlFrame);
                }

            }
            catch (Exception ex)
            {
                Application.ShowAlertDialog(ex.ToString());
            }
        }



        private void FormatText(FeatureControlFrame fc)
        {
            string old = fc.Text;
            try
            {
                string searchtext = fc.Text;
                string para = Utility.ExtractFormatPara(searchtext);
                while (para != "")
                {
                    Parameter value = _lsPara.Where(e => e.Name == para).FirstOrDefault();

                    if (value != null)
                    {
                        fc.Text = fc.Text.Replace("{" + para + "}", value.Format());
                        searchtext = fc.Text;
                    }
                    else
                    {
                        int pos = searchtext.IndexOf('}');
                        if (pos != -1)
                        {
                            searchtext = searchtext.Substring(pos + 1);
                        }
                        else
                        {
                            searchtext = "";
                        }
                    }

                    para = Utility.ExtractFormatPara(searchtext);
                }
            }
            catch
            {
                //"格式化公差（{0}）出错!"
                Exception ex = new Exception(string.Format(LanguageHelper.GetString("Exception_FormatTolerance"), old));
                throw ex;
            }
        }

        private void FormatText(Dimension dim)
        {
            string old = dim.DimensionText;
            try
            {

                dim.DimensionText = dim.DimensionText.Replace("\\{", "{");
                dim.DimensionText = dim.DimensionText.Replace("\\}", "}");

                string searchtext = dim.DimensionText;
                string para = Utility.ExtractFormatPara(searchtext);
                while (para != "")
                {
                    Parameter value = _lsPara.Where(e => e.Name == para).FirstOrDefault();
                    if (value != null)
                    {
                        dim.DimensionText = dim.DimensionText.Replace("{" + para + "}", value.Format());
                        searchtext = dim.DimensionText;
                    }
                    else
                    {
                        int pos = searchtext.IndexOf('}');
                        if (pos != -1)
                        {
                            searchtext = searchtext.Substring(pos + 1);
                        }
                        else
                        {
                            searchtext = "";
                        }
                    }

                    para = Utility.ExtractFormatPara(searchtext);
                }
            }
            catch
            {
                //"格式化Dimension（{0}）出错!"
                Exception ex = new Exception(string.Format(LanguageHelper.GetString("Exception_FormatDimension"), old));
                throw ex;
            }

        }




        private void FormatText(DBText text)
        {
            string old = text.TextString;
            try
            {
                string searchtext = text.TextString;
                string para = Utility.ExtractFormatPara(searchtext);
                while (para != "")
                {
                    Parameter value = _lsPara.Where(e => e.Name == para).FirstOrDefault();
                    if (value != null)
                    {
                        text.TextString = text.TextString.Replace("{" + para + "}", value.Format());
                        searchtext = text.TextString;
                    }
                    else
                    {
                        int pos = searchtext.IndexOf('}');
                        if (pos != -1)
                        {
                            searchtext = searchtext.Substring(pos + 1);
                        }
                        else
                        {
                            searchtext = "";
                        }
                    }

                    para = Utility.ExtractFormatPara(searchtext);
                }
            }
            catch
            {
                //"格式化DBText（{0}）出错!"
                Exception ex = new Exception(string.Format(LanguageHelper.GetString("Exception_FormatDBText"), old));
                throw ex;
            }

        }


        private void FormatText(MText text)
        {
            string old = text.Text;
            try
            {
                text.Contents = text.Contents.Replace("\r\n", "");
                text.Contents = text.Contents.Replace("\\{", "{");
                text.Contents = text.Contents.Replace("\\}", "}");

                string searchtext = text.Contents;
                string para = Utility.ExtractFormatPara(searchtext);

                while (para != "")
                {
                    Parameter value = _lsPara.Where(e => e.Name == para).FirstOrDefault();
                    if (value != null)
                    {

                        text.Contents = text.Contents.Replace("{" + para + "}", value.Format());
                        searchtext = text.Contents;
                    }
                    else
                    {
                        int pos = searchtext.IndexOf('}');
                        if (pos != -1)
                        {
                            searchtext = searchtext.Substring(pos + 1);
                        }
                        else
                        {
                            searchtext = "";
                        }
                    }

                    para = Utility.ExtractFormatPara(searchtext);

                }


            }
            catch (Exception e)
            {
                //Exception ex = new Exception(string.Format("格式化MText（{0}）出错!\r\n{1}", old, e.ToString()));
                //"格式化MText（{0}）出错!"
                Exception ex = new Exception(string.Format(LanguageHelper.GetString("Exception_FormatMText"), old)+ "\r\n"+e.ToString());
                throw ex;
            }
        }

    }
}
