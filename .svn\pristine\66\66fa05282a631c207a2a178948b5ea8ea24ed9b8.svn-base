﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD;
using Autodesk.AutoCAD.Runtime;

//using Dimensiontidy;

//using netlib.Dimensiontidy;


namespace CADWSAddin.Tools
{
    //尺寸参照对象极限范围坐标
    struct EntityBound
    {
        public Double Z;           //Z水平最小坐标
        public Double Y;           //Y水平最大坐标
        public Double S;           //S竖直最大坐标
        public Double X;           //X竖直最小坐标              
    }

    struct DimLineInfo
    {
        public DimLineInfo(bool b)
        {
            fir_deep = 0.0;
            sec_deep = 0.0;
            FormatT = b;
            ent_hadel_key = string.Empty;
        }

        public double fir_deep;
        public double sec_deep;
        public bool FormatT;
        public string ent_hadel_key;
    }


    public class DimensionTidy /*:IExtensionApplication */
    {
        //public void Terminate()
        //{
        //}

        //public void Initialize()
        //{    //初始化，增加右键菜单

        //}

        public const string HL_name = "EntityKey";
        private double Dimexe = 0.0;
        private bool btest = false;
        //获得实体边界 2012-1-9
        private EntityBound GetEntityBound(Entity ent)
        {
            EntityBound bound = new EntityBound();
            if (ent == null)
            {
                return bound;
            }
            else if (ent is BlockReference || ent is Polyline)
            {
                bound.Z = ent.Bounds.Value.MinPoint.X;
                bound.Y = ent.Bounds.Value.MaxPoint.X;
                bound.S = ent.Bounds.Value.MaxPoint.Y;
                bound.X = ent.Bounds.Value.MinPoint.Y;
            }
            return bound;
        }

        DimLineInfo ParseDimLineInfo(string s)
        {
            DimLineInfo info = new DimLineInfo(false);
            if (s.IndexOf(HL_name) >= 0)
            {
                string LineW;
                if (s.Substring(s.Length - 1, 1) == "T")
                {
                    LineW = s.Remove(s.Length - 2, 2);
                    info.FormatT = true;
                }
                else
                {
                    LineW = s;
                    info.FormatT = false;
                }
                //EntityKey:e35701;DimLine:30,15
                if (LineW.IndexOf("DimLine") >= 0)
                {
                    //带有尺寸线间距的参照记录
                    string Ent_handel_temp = LineW.Split(';')[0];
                    string Dimdeep = LineW.Split(';')[1].Split(':')[1];

                    info.ent_hadel_key = Ent_handel_temp.Substring(Ent_handel_temp.IndexOf(":") + 1,
                                    Ent_handel_temp.Length - (Ent_handel_temp.IndexOf(":") + 1));

                    try
                    {
                        info.fir_deep = double.Parse(Dimdeep.Split(',')[0]);
                        info.sec_deep = double.Parse(Dimdeep.Split(',')[1]);
                    }
                    catch (System.Exception ex)
                    {
                        Palettes.WriteLog(ex.Message, 2);
                        Palettes.WriteLog(ex.StackTrace, 2);
                    }
                }
            }
            return info;
        }

        //计算尺寸线相对边界的偏移量 n尺寸所在层,从1 开始计数
        private double CalOffset(int n, DimLineInfo info, Dictionary<int, bool> dicLayer)
        {
            double offset = 0.0;
            if (n == 1)
            {
                offset = info.fir_deep;
            }
            else
            {
                int nHide = 0;      //隐藏层数量
                foreach (int key in dicLayer.Keys)
                {
                    if (key < n)
                    {
                        if (!dicLayer[key])
                        {
                            nHide += 1;
                        }
                    }
                }

                offset = info.fir_deep + (n - 1 - nHide) * info.sec_deep;
            }

            return offset;
        }


        //sample  94289-Lay:1
        private int GetLayerName(Dimension d, string entHandle)
        {
            int num = -1;
            string s = d.Dimapost;

            if (s.IndexOf(entHandle + "-Lay:") >= 0)
            {
                int pos = s.IndexOf(":") + 1;

                try
                {
                    num = int.Parse(s.Substring(pos, s.Length - pos));
                }
                catch (System.Exception ex)
                {
                    Palettes.WriteLog(ex.Message, 2);
                    Palettes.WriteLog(ex.StackTrace, 2);
                }
            }
            return num;
        }


        private int GetDimPosition(RotatedDimension dim, EntityBound bound)
        {
            int pos = 0;
            if (dim.Rotation == 0)
            {
                if (Math.Abs(dim.TextPosition.Y - bound.X) < Math.Abs(dim.TextPosition.Y - bound.S))
                {
                    pos = 1;
                }
                else
                {
                    pos = 0;
                }
            }
            else
            {
                if (Math.Abs(dim.TextPosition.X - bound.Z) < Math.Abs(dim.TextPosition.X - bound.Y))
                {
                    pos = 2;
                }
                else
                {
                    pos = 3;
                }
            }

            return pos;
        }

        /*水平移动尺寸(垂直尺寸) 
         *  dir=0,1,2,3 上下左右
         *  offset 相对边界的距离(都是正的)
        */
        private void DimLineMove(RotatedDimension dimen, EntityBound bound, double offset, int dir)
        {
            switch (dir)
            {
                case 0:
                    dimen.XLine1Point = new Point3d(dimen.XLine1Point.X, bound.S + offset, dimen.XLine1Point.Z);
                    dimen.XLine2Point = new Point3d(dimen.XLine2Point.X, bound.S + offset, dimen.XLine2Point.Z);
                    break;
                case 1:
                    dimen.XLine1Point = new Point3d(dimen.XLine1Point.X, bound.X - offset, dimen.XLine1Point.Z);
                    dimen.XLine2Point = new Point3d(dimen.XLine2Point.X, bound.X - offset, dimen.XLine2Point.Z);
                    break;
                case 2:
                    dimen.XLine1Point = new Point3d(bound.Z - offset, dimen.XLine1Point.Y, dimen.XLine1Point.Z);
                    dimen.XLine2Point = new Point3d(bound.Z - offset, dimen.XLine2Point.Y, dimen.XLine2Point.Z);
                    break;
                case 3:
                    dimen.XLine1Point = new Point3d(bound.Y + offset, dimen.XLine1Point.Y, dimen.XLine1Point.Z);
                    dimen.XLine2Point = new Point3d(bound.Y + offset, dimen.XLine2Point.Y, dimen.XLine2Point.Z);
                    break;
            }
        }

        private Dictionary<int, bool>[] TraveDimLayerInfo(Transaction trans, BlockTableRecord btrBlock, string ent_hadel_key, EntityBound bound)
        {
            Dictionary<int, bool>[] dicLayer = new Dictionary<int, bool>[4];
            for (int i = 0; i < 4; i++)
            {
                dicLayer[i] = new Dictionary<int, bool>();
            }

            //Database db = HostApplicationServices.WorkingDatabase;
            //using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                foreach (ObjectId DimId in btrBlock)
                {
                    Entity dimEntity = trans.GetObject(DimId, OpenMode.ForRead) as Entity;
                    if (dimEntity is RotatedDimension)
                    {
                        RotatedDimension dim = dimEntity as RotatedDimension;

                        LayerTableRecord ltr = trans.GetObject(dim.LayerId, OpenMode.ForRead) as LayerTableRecord;

                        int laynum = GetLayerName(dim, ent_hadel_key);
                        if (laynum != -1)
                        {
                            int pos = GetDimPosition(dim, bound);
                            bool val = false;

                            if (!dicLayer[pos].TryGetValue(laynum, out val))
                            {
                                dicLayer[pos].Add(laynum, false);
                            }

                            if (!dicLayer[pos][laynum] && !ltr.IsOff)
                            {
                                dicLayer[pos][laynum] = true;
                            }
                        }
                    }
                }
            }

            return dicLayer;
        }

        //
        private void ParseDimLimit(double val1, double val2, ref string down_min, ref string down_max)
        {
            double d1, d2;
            if (val1 < val2)
            {
                d1 = val1;
                d2 = val2;
            }
            else
            {
                d1 = val2;
                d2 = val1;
            }

            try
            {
                if (down_min == string.Empty)
                {
                    down_min = d1.ToString();
                }
                else
                {
                    if (double.Parse(down_min) >= d1)
                    {
                        down_min = d1.ToString();
                    }
                }

                if (down_max == string.Empty)
                {
                    down_max = d2.ToString();
                }
                else
                {
                    if (double.Parse(down_max) <= d2)
                    {
                        down_max = d2.ToString();
                    }
                }
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
            }
        }

        //[CommandMethod("yyg")]
        public void Execute()
        {
            Database db = HostApplicationServices.WorkingDatabase;
            Editor ed = Application.DocumentManager.MdiActiveDocument.Editor;
            Document doc = Application.DocumentManager.MdiActiveDocument;

            Dimexe = db.Dimexe;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    TypedValue[] filListBlock = new TypedValue[1];
                    filListBlock[0] = new TypedValue(0, "Insert");
                    SelectionFilter filterBlock = new SelectionFilter(filListBlock);
                    //选择对象
                    PromptSelectionResult resBlock = ed.SelectAll(filterBlock);
                    if (resBlock.Status == PromptStatus.OK)
                    {
                        foreach (ObjectId BlockId in resBlock.Value.GetObjectIds())//遍历选择集里的所有实体对象
                        {
                            #region 整理图纸中的尺寸对象
                            //获取图块对象
                            BlockReference BlockObject = trans.GetObject(BlockId, OpenMode.ForRead) as BlockReference;
                            BlockTableRecord btrBlock = trans.GetObject(BlockObject.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                            if (BlockObject.Hyperlinks.Count > 0)
                            {
                                Entity ent = BlockObject as Entity;
                                DimLineInfo info = new DimLineInfo(false);
                                foreach (HyperLink item in ent.Hyperlinks)
                                {
                                    info = ParseDimLineInfo(item.Name);
                                }

                                if (info.ent_hadel_key != string.Empty)
                                {
                                    EntityBound bound = GetEntityBound(ent);

                                    filListBlock[0] = new TypedValue(0, "Dimension");
                                    SelectionFilter filterdim = new SelectionFilter(filListBlock);
                                    resBlock = ed.SelectAll(filterdim);

                                    if (resBlock.Status == PromptStatus.OK)
                                    {
                                        foreach (ObjectId DimId in resBlock.Value.GetObjectIds())
                                        {
                                            Dimension dim = trans.GetObject(DimId, OpenMode.ForWrite) as Dimension;

                                            if (dim.Dimapost.IndexOf(info.ent_hadel_key + "-Lay:") >= 0)
                                            {
                                                try
                                                {
                                                    int laynum = int.Parse(dim.Dimapost.Substring((dim.Dimapost.IndexOf(":") + 1),
                                                                 (dim.Dimapost.Length - (dim.Dimapost.IndexOf(":") + 1)))) - 1;
                                                    double Usingdeep;
                                                    if (laynum == 0)
                                                    {
                                                        Usingdeep = info.fir_deep;
                                                    }
                                                    else
                                                    {
                                                        Usingdeep = info.fir_deep + laynum * info.sec_deep;
                                                    }

                                                    dim.UpgradeOpen();
                                                    if (dim is RotatedDimension)
                                                    {
                                                        RotatedDimension dimen = dim as RotatedDimension;
                                                        #region 修改文本位置
                                                        if (dimen.Rotation == 0)
                                                        {
                                                            //水平尺寸
                                                            if (Math.Abs(dimen.TextPosition.Y - bound.X) < Math.Abs(dimen.TextPosition.Y - bound.S))
                                                            {
                                                                dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.X - Usingdeep, 0);
                                                            }
                                                            else
                                                            {
                                                                dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.S + Usingdeep, 0);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            //竖直尺寸
                                                            if (Math.Abs(dimen.TextPosition.X - bound.Z) < Math.Abs(dimen.TextPosition.X - bound.Y))
                                                            {
                                                                dimen.TextPosition = new Point3d(bound.Z - Usingdeep, dimen.TextPosition.Y, 0);
                                                            }
                                                            else
                                                            {
                                                                dimen.TextPosition = new Point3d(bound.Y + Usingdeep, dimen.TextPosition.Y, 0);
                                                            }
                                                        }
                                                        #endregion
                                                    }
                                                }
                                                catch (System.Exception ex)
                                                {
                                                    Palettes.WriteLog(ex.Message, 2);
                                                    Palettes.WriteLog(ex.StackTrace, 2);
                                                }
                                            }
                                        }
                                    }
                                }

                            }
                            #endregion



                            #region 整理块中的尺寸
                            formating(trans, btrBlock, BlockId);
                            #endregion

                        }
                    }
                    trans.Commit();
                }
            }
        }
        public void Excute2()
        {
            Database db = HostApplicationServices.WorkingDatabase;
            Editor ed =Application.DocumentManager.MdiActiveDocument.Editor;
            Document doc = Application.DocumentManager.MdiActiveDocument;

            Dimexe = db.Dimexe;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                    //trans.GetObject(_database.BlockTableId, OpenMode.ForRead) as BlockTable;

                    BlockTableRecord btr= trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;



                    foreach (ObjectId BlockId in btr)//遍历选择集里的所有实体对象
                        {

                            Entity BlockEntity = trans.GetObject(BlockId, OpenMode.ForRead) as Entity;

                            #region 循环块中块，判断是否需要整理其中的尺寸
                            //if (BlockEntity is BlockReference)
                            //{
                            //    BlockReference BlockObject = trans.GetObject(BlockId, OpenMode.ForRead) as BlockReference;
                            //    BlockTableRecord btrBlockTemp = trans.GetObject(BlockObject.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                            //    formating(trans, btrBlockTemp, BlockId);
                            //}
                            BlockReference BlockObject = trans.GetObject(BlockId, OpenMode.ForRead) as BlockReference;
                            
                            #endregion


                            if (BlockEntity is Polyline || BlockEntity is BlockReference)
                            {

                                DimLineInfo info = new DimLineInfo(false);
                                foreach (HyperLink item in BlockEntity.Hyperlinks)
                                {
                                    info = ParseDimLineInfo(item.Name);
                                }

                                //创建尺寸字典,用来最后整理尺寸界线用 ,Value 尺寸对象，Key 相对位置（上下左右）                                    
                                Dictionary<RotatedDimension, string> dimList = new Dictionary<RotatedDimension, string>();
                                //当前参照对象所有待整理尺寸的最大边界范围
                                string up_min, up_max, down_min, down_max, left_min, left_max, right_min, right_max;
                                up_min = string.Empty;
                                up_max = string.Empty;
                                down_min = string.Empty;
                                down_max = string.Empty;
                                left_min = string.Empty;
                                left_max = string.Empty;
                                right_min = string.Empty;
                                right_max = string.Empty;



                                if (info.ent_hadel_key != string.Empty)
                                {
                                    EntityBound bound = GetEntityBound(BlockEntity);

                                    Dictionary<int, bool>[] dicLayer = TraveDimLayerInfo(trans, btr, info.ent_hadel_key, bound);
                                    foreach (ObjectId DimId in btr)
                                    {
                                        Entity dimEntity = trans.GetObject(DimId, OpenMode.ForRead) as Entity;
                                        LayerTable lt = (LayerTable)trans.GetObject(HostApplicationServices.WorkingDatabase.LayerTableId, OpenMode.ForWrite);

                                        if (dimEntity is Dimension)
                                        {
                                            Dimension dim = dimEntity as Dimension;
                                            int laynum = GetLayerName(dim, info.ent_hadel_key);

                                            if (laynum >= 0)
                                            {
                                                dim.UpgradeOpen();
                                                if (dim is RotatedDimension)
                                                {
                                                    RotatedDimension dimen = dim as RotatedDimension;
                                                    int pos = GetDimPosition(dimen, bound);

                                                    double offset = CalOffset(laynum, info, dicLayer[pos]);       //尺寸文本偏移
                                                    double offset1 = offset - info.sec_deep + Dimexe*2;//尺寸线偏移

                                                    if (pos == 0)
                                                    {
                                                        //尺寸向上整理
                                                        dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.S + offset, 0);
                                                        if (info.FormatT)
                                                        {
                                                            DimLineMove(dimen, bound, offset1, 0);
                                                        }

                                                        if (lt.Has(dimen.Layer))
                                                        {
                                                            LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                            if (!ltr.IsOff)
                                                            {
                                                                #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                                dimList.Add(dimen, "上");
                                                                ParseDimLimit(dimen.XLine1Point.X, dimen.XLine2Point.X, ref up_min, ref up_max);

                                                                #endregion
                                                            }
                                                        }

                                                    }
                                                    else if (pos == 1)//尺寸向下整理
                                                    {
                                                        dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.X - offset, 0);
                                                        if (info.FormatT)
                                                        {
                                                            DimLineMove(dimen, bound, offset1, 1);
                                                        }
                                                        if (lt.Has(dimen.Layer))
                                                        {
                                                            LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                            if (!ltr.IsOff)//关闭层中的尺寸不进行尺寸线末端整理
                                                            {
                                                                #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限
                                                                dimList.Add(dimen, "下");
                                                                ParseDimLimit(dimen.XLine1Point.X, dimen.XLine2Point.X, ref down_min, ref down_max);
                                                                #endregion
                                                            }
                                                        }

                                                    }
                                                    else if (pos == 2) //尺寸向左整理
                                                    {
                                                        dimen.TextPosition = new Point3d(bound.Z - offset, dimen.TextPosition.Y, 0);
                                                        if (info.FormatT)
                                                        {
                                                            DimLineMove(dimen, bound, offset1, 2);
                                                        }
                                                        if (lt.Has(dimen.Layer))
                                                        {
                                                            LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                            if (!ltr.IsOff)
                                                            {
                                                                #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                                dimList.Add(dimen, "左");
                                                                ParseDimLimit(dimen.XLine1Point.Y, dimen.XLine2Point.Y, ref left_min, ref left_max);
                                                                #endregion
                                                            }
                                                        }

                                                    }
                                                    else if (pos == 3)//尺寸向右整理
                                                    {
                                                        dimen.TextPosition = new Point3d(bound.Y + offset, dimen.TextPosition.Y, 0);
                                                        if (info.FormatT)
                                                        {
                                                            DimLineMove(dimen, bound, offset1, 3);
                                                        }
                                                        if (lt.Has(dimen.Layer))
                                                        {
                                                            LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                            if (!ltr.IsOff)
                                                            {
                                                                #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                                dimList.Add(dimen, "右");
                                                                ParseDimLimit(dimen.XLine1Point.Y, dimen.XLine2Point.Y, ref right_min, ref right_max);

                                                                #endregion
                                                            }
                                                        }
                                                    }

                                                }
                                            }
                                        }
                                    }

                                    if (info.FormatT)
                                    {
                                        #region 整理最外面的尺寸界线 通过配置文件来控制是否需要进行整理尺寸界限
                                        foreach (var item in dimList)
                                        {
                                            try
                                            {
                                                if (item.Value == "上")
                                                {
                                                    if (System.Math.Abs(item.Key.XLine1Point.X - double.Parse(up_max)) <= 0.1 || System.Math.Abs(item.Key.XLine1Point.X - double.Parse(up_min)) <= 0.1)
                                                    {
                                                        item.Key.XLine1Point = new Point3d(item.Key.XLine1Point.X, bound.S, item.Key.XLine1Point.Z);
                                                    }

                                                    if (System.Math.Abs(item.Key.XLine2Point.X - double.Parse(up_max)) <= 0.1 || System.Math.Abs(item.Key.XLine2Point.X - double.Parse(up_min)) <= 0.1)
                                                    {
                                                        item.Key.XLine2Point = new Point3d(item.Key.XLine2Point.X, bound.S, item.Key.XLine2Point.Z);
                                                    }
                                                }
                                                else if (item.Value == "下")
                                                {

                                                    if (System.Math.Abs(item.Key.XLine1Point.X - double.Parse(down_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.X - double.Parse(down_min)) < 0.1)
                                                    {
                                                        item.Key.XLine1Point = new Point3d(item.Key.XLine1Point.X, bound.X, item.Key.XLine1Point.Z);
                                                    }

                                                    if (System.Math.Abs(item.Key.XLine2Point.X - double.Parse(down_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.X - double.Parse(down_min)) < 0.1)
                                                    {
                                                        item.Key.XLine2Point = new Point3d(item.Key.XLine2Point.X, bound.X, item.Key.XLine2Point.Z);
                                                    }

                                                }
                                                else if (item.Value == "左")
                                                {

                                                    if (System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(left_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(left_min)) < 0.1)
                                                    {
                                                        item.Key.XLine1Point = new Point3d(bound.Z, item.Key.XLine1Point.Y, item.Key.XLine1Point.Z);
                                                    }

                                                    if (System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(left_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(left_min)) < 0.1)
                                                    {
                                                        item.Key.XLine2Point = new Point3d(bound.Z, item.Key.XLine2Point.Y, item.Key.XLine2Point.Z);
                                                    }

                                                }
                                                else if (item.Value == "右")
                                                {

                                                    if (System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(right_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(right_min)) < 0.1)
                                                    {
                                                        item.Key.XLine1Point = new Point3d(bound.Y, item.Key.XLine1Point.Y, item.Key.XLine1Point.Z);
                                                    }

                                                    if (System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(right_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(right_min)) < 0.1)
                                                    {
                                                        item.Key.XLine2Point = new Point3d(bound.Y, item.Key.XLine2Point.Y, item.Key.XLine2Point.Z);
                                                    }

                                                }
                                            }
                                            catch (System.Exception ex)
                                            {
                                                Palettes.WriteLog(ex.Message, 2);
                                                Palettes.WriteLog(ex.StackTrace, 2);
                                            }
                                        }
                                        #endregion
                                    }

                                    dimList.Clear();

                                }
                            }
                        }



                    
                    trans.Commit();
                }
            }
        }

        private void formating(Transaction trans, BlockTableRecord btrBlock, ObjectId BlockId)
        {

            #region 循环块表记录
            foreach (ObjectId BlcokId in btrBlock)
            {

                Entity BlockEntity = trans.GetObject(BlcokId, OpenMode.ForRead) as Entity;

                #region 循环块中块，判断是否需要整理其中的尺寸
                if (BlockEntity is BlockReference)
                {
                    BlockReference BlockObject = trans.GetObject(BlockId, OpenMode.ForRead) as BlockReference;
                    BlockTableRecord btrBlockTemp = trans.GetObject(BlockObject.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    formating(trans, btrBlockTemp, BlcokId);
                }
                #endregion


                if (BlockEntity is Polyline || BlockEntity is BlockReference)
                {

                    DimLineInfo info = new DimLineInfo(false);
                    foreach (HyperLink item in BlockEntity.Hyperlinks)
                    {
                        info = ParseDimLineInfo(item.Name);
                    }

                    //创建尺寸字典,用来最后整理尺寸界线用 ,Value 尺寸对象，Key 相对位置（上下左右）                                    
                    Dictionary<RotatedDimension, string> dimList = new Dictionary<RotatedDimension, string>();
                    //当前参照对象所有待整理尺寸的最大边界范围
                    string up_min, up_max, down_min, down_max, left_min, left_max, right_min, right_max;
                    up_min = string.Empty;
                    up_max = string.Empty;
                    down_min = string.Empty;
                    down_max = string.Empty;
                    left_min = string.Empty;
                    left_max = string.Empty;
                    right_min = string.Empty;
                    right_max = string.Empty;



                    if (info.ent_hadel_key != string.Empty)
                    {
                        EntityBound bound = GetEntityBound(BlockEntity);

                        Dictionary<int, bool>[] dicLayer = TraveDimLayerInfo(trans, btrBlock, info.ent_hadel_key, bound);
                        foreach (ObjectId DimId in btrBlock)
                        {
                            Entity dimEntity = trans.GetObject(DimId, OpenMode.ForRead) as Entity;
                            LayerTable lt = (LayerTable)trans.GetObject(HostApplicationServices.WorkingDatabase.LayerTableId, OpenMode.ForWrite);

                            if (dimEntity is Dimension)
                            {
                                Dimension dim = dimEntity as Dimension;
                                int laynum = GetLayerName(dim, info.ent_hadel_key);

                                if (laynum >= 0)
                                {
                                    dim.UpgradeOpen();
                                    if (dim is RotatedDimension)
                                    {
                                        RotatedDimension dimen = dim as RotatedDimension;
                                        int pos = GetDimPosition(dimen, bound);

                                        double offset = CalOffset(laynum, info, dicLayer[pos]);       //尺寸文本偏移
                                        double offset1 = offset - info.sec_deep + Dimexe;//尺寸线偏移

                                        if (pos == 0)
                                        {
                                            //尺寸向上整理
                                            dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.S + offset, 0);
                                            if (info.FormatT)
                                            {
                                                DimLineMove(dimen, bound, offset1, 0);
                                            }

                                            if (lt.Has(dimen.Layer))
                                            {
                                                LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                if (!ltr.IsOff)
                                                {
                                                    #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                    dimList.Add(dimen, "上");
                                                    ParseDimLimit(dimen.XLine1Point.X, dimen.XLine2Point.X, ref up_min, ref up_max);

                                                    #endregion
                                                }
                                            }

                                        }
                                        else if (pos == 1)//尺寸向下整理
                                        {
                                            dimen.TextPosition = new Point3d(dimen.TextPosition.X, bound.X - offset, 0);
                                            if (info.FormatT)
                                            {
                                                DimLineMove(dimen, bound, offset1, 1);
                                            }
                                            if (lt.Has(dimen.Layer))
                                            {
                                                LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                if (!ltr.IsOff)//关闭层中的尺寸不进行尺寸线末端整理
                                                {
                                                    #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限
                                                    dimList.Add(dimen, "下");
                                                    ParseDimLimit(dimen.XLine1Point.X, dimen.XLine2Point.X, ref down_min, ref down_max);
                                                    #endregion
                                                }
                                            }

                                        }
                                        else if (pos == 2) //尺寸向左整理
                                        {
                                            dimen.TextPosition = new Point3d(bound.Z - offset, dimen.TextPosition.Y, 0);
                                            if (info.FormatT)
                                            {
                                                DimLineMove(dimen, bound, offset1, 2);
                                            }
                                            if (lt.Has(dimen.Layer))
                                            {
                                                LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                if (!ltr.IsOff)
                                                {
                                                    #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                    dimList.Add(dimen, "左");
                                                    ParseDimLimit(dimen.XLine1Point.Y, dimen.XLine2Point.Y, ref left_min, ref left_max);
                                                    #endregion
                                                }
                                            }

                                        }
                                        else if (pos == 3)//尺寸向右整理
                                        {
                                            dimen.TextPosition = new Point3d(bound.Y + offset, dimen.TextPosition.Y, 0);
                                            if (info.FormatT)
                                            {
                                                DimLineMove(dimen, bound, offset1, 3);
                                            }
                                            if (lt.Has(dimen.Layer))
                                            {
                                                LayerTableRecord ltr = (LayerTableRecord)lt[dimen.Layer].GetObject(OpenMode.ForRead);
                                                if (!ltr.IsOff)
                                                {
                                                    #region 计算最大范围及创建待处理尺寸线字典 通过配置文件来控制是否需要进行整理尺寸界限

                                                    dimList.Add(dimen, "右");
                                                    ParseDimLimit(dimen.XLine1Point.Y, dimen.XLine2Point.Y, ref right_min, ref right_max);

                                                    #endregion
                                                }
                                            }
                                        }

                                    }
                                }
                            }
                        }

                        if (info.FormatT)
                        {
                            #region 整理最外面的尺寸界线 通过配置文件来控制是否需要进行整理尺寸界限
                            foreach (var item in dimList)
                            {
                                try
                                {
                                    if (item.Value == "上")
                                    {
                                        if (System.Math.Abs(item.Key.XLine1Point.X - double.Parse(up_max)) <= 0.1 || System.Math.Abs(item.Key.XLine1Point.X - double.Parse(up_min)) <= 0.1)
                                        {
                                            item.Key.XLine1Point = new Point3d(item.Key.XLine1Point.X, bound.S, item.Key.XLine1Point.Z);
                                        }

                                        if (System.Math.Abs(item.Key.XLine2Point.X - double.Parse(up_max)) <= 0.1 || System.Math.Abs(item.Key.XLine2Point.X - double.Parse(up_min)) <= 0.1)
                                        {
                                            item.Key.XLine2Point = new Point3d(item.Key.XLine2Point.X, bound.S, item.Key.XLine2Point.Z);
                                        }
                                    }
                                    else if (item.Value == "下")
                                    {

                                        if (System.Math.Abs(item.Key.XLine1Point.X - double.Parse(down_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.X - double.Parse(down_min)) < 0.1)
                                        {
                                            item.Key.XLine1Point = new Point3d(item.Key.XLine1Point.X, bound.X, item.Key.XLine1Point.Z);
                                        }

                                        if (System.Math.Abs(item.Key.XLine2Point.X - double.Parse(down_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.X - double.Parse(down_min)) < 0.1)
                                        {
                                            item.Key.XLine2Point = new Point3d(item.Key.XLine2Point.X, bound.X, item.Key.XLine2Point.Z);
                                        }

                                    }
                                    else if (item.Value == "左")
                                    {

                                        if (System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(left_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(left_min)) < 0.1)
                                        {
                                            item.Key.XLine1Point = new Point3d(bound.Z, item.Key.XLine1Point.Y, item.Key.XLine1Point.Z);
                                        }

                                        if (System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(left_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(left_min)) < 0.1)
                                        {
                                            item.Key.XLine2Point = new Point3d(bound.Z, item.Key.XLine2Point.Y, item.Key.XLine2Point.Z);
                                        }

                                    }
                                    else if (item.Value == "右")
                                    {

                                        if (System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(right_max)) < 0.1 || System.Math.Abs(item.Key.XLine1Point.Y - double.Parse(right_min)) < 0.1)
                                        {
                                            item.Key.XLine1Point = new Point3d(bound.Y, item.Key.XLine1Point.Y, item.Key.XLine1Point.Z);
                                        }

                                        if (System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(right_max)) < 0.1 || System.Math.Abs(item.Key.XLine2Point.Y - double.Parse(right_min)) < 0.1)
                                        {
                                            item.Key.XLine2Point = new Point3d(bound.Y, item.Key.XLine2Point.Y, item.Key.XLine2Point.Z);
                                        }

                                    }
                                }
                                catch (System.Exception ex)
                                {
                                    Palettes.WriteLog(ex.Message, 2);
                                    Palettes.WriteLog(ex.StackTrace, 2);
                                }
                            }
                            #endregion
                        }

                        dimList.Clear();

                    }
                }
            }
            #endregion
        }


        //[CommandMethod("dlf")]
        //public void test()
        //{
        //    BlockExp b = new BlockExp();
        //    b.DeleteLayerFilter();
        //}


    }

}