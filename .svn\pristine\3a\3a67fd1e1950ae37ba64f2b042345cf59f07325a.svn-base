﻿<?xml version="1.0"?>
<Node>
	<ModelName />
	<BlockName>井道平面图</BlockName>
	<Parameters>
		<Parameter>
			<Name>插入点</Name>
			<Type>String</Type>
			<Value>Origin</Value>
		</Parameter>
	</Parameters>
	<Functions />
	<ChildNodes>
		<!--骨架-->
		<Node>
			<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
			<BlockName>骨架</BlockName>
			<Parameters>
				<Parameter>
					<Name>插入点</Name>
					<Type>String</Type>
					<Value>Origin</Value>
				</Parameter>
			</Parameters>
			<Functions>
				<Function>
					<Name>SetVariable</Name>
					<OperObject>ZX井道平面图轿厢中心到原点距离X</OperObject>
					<Value>5000</Value>
				</Function>
				<Function>
					<Name>SetVariable</Name>
					<OperObject>ZX层高表到原点距离X</OperObject>
					<Value>8000</Value>
				</Function>
				<Function>
					<Name>SetVariable</Name>
					<OperObject>ZX井道平面图井道前内壁到轿厢中心距离Y</OperObject>
					<Value>3000</Value>
				</Function>
			</Functions>
			<ChildNodes />
		</Node>
		<!--井道装配-->
		<Node>
			<ModelName />
			<BlockName>井道装配</BlockName>
			<Parameters>
				<Parameter>
					<Name>插入点</Name>
					<Type>String</Type>
					<Value>Origin</Value>
				</Parameter>
			</Parameters>
			<Functions/>
			<ChildNodes>
				<!--井道-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试.dwg</ModelName>
					<BlockName>井道</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>井道平面图</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX井道宽度</OperObject>
							<Value>2100</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX井道深度</OperObject>
							<Value>2300</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>可见性1</OperObject>
							<Value>none</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--对重-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>对重</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>对重中心</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX对重宽度</OperObject>
							<Value>1100</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--轿厢-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>轿厢</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>轿厢</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿厢内宽</OperObject>
							<Value>1600</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿厢内深</OperObject>
							<Value>1500</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX开门宽</OperObject>
							<Value>900</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZXCOP到轿厢中心距离</OperObject>
							<Value>625</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>翻转状态1</OperObject>
							<Value>已翻转</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--轿门地坎-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>轿门地坎</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>轿门地坎</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿门地坎长度</OperObject>
							<Value>1840</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿门闭合宽度</OperObject>
							<Value>910</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--厅门地坎-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>厅门地坎</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>厅门地坎</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX厅门地坎长度</OperObject>
							<Value>1900</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX厅门闭合宽度</OperObject>
							<Value>930</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--对重反绳轮-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>对重反绳轮</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>对重中心</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX对重反绳轮直径</OperObject>
							<Value>520</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--对重导轨-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>对重导轨</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>对重中心</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX对重轨顶距</OperObject>
							<Value>1200</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX对重导轨高</OperObject>
							<Value>62</Value>
						</Function>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX对重导轨高1</OperObject>
							<Value>62</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--轿厢导轨-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>轿厢导轨</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>轿厢中心</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿厢轨顶距</OperObject>
							<Value>1800</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--轿厢限速器-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>轿厢限速器</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>轿厢限速器</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿厢限速器轮直径</OperObject>
							<Value>240</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
				<!--轿厢反绳轮-->
				<Node>
					<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\测试2.dwg</ModelName>
					<BlockName>轿厢反绳轮</BlockName>
					<Parameters>
						<Parameter>
							<Name>插入点</Name>
							<Type>String</Type>
							<Value>轿厢中心</Value>
						</Parameter>
					</Parameters>
					<Functions>
						<Function>
							<Name>SetVariable</Name>
							<OperObject>ZX轿厢反绳轮直径</OperObject>
							<Value>520</Value>
						</Function>
					</Functions>
					<ChildNodes />
				</Node>
			</ChildNodes>
		</Node>
		<!--层高表-->
		<Node>
			<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\层高表.dwg</ModelName>
			<BlockName>层高表</BlockName>
			<Parameters>
				<Parameter>
					<Name>插入点</Name>
					<Type>String</Type>
					<Value>层高表</Value>
				</Parameter>
			</Parameters>
			<Functions />
			<ChildNodes />
		</Node>
		<!--图框-->
		<Node>
			<ModelName>D:\01-工作\03-ZX标准版\产品化一期\05-CAD模型\图框.dwg</ModelName>
			<BlockName>图框</BlockName>
			<Parameters>
				<Parameter>
					<Name>插入点</Name>
					<Type>String</Type>
					<Value>Origin</Value>
				</Parameter>
			</Parameters>
			<Functions />
			<ChildNodes />
		</Node>
	</ChildNodes>
</Node>
