﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using Exception = System.Exception;

//using LDSCADAddin.Utils;
//using LDSCADAddin;

namespace CADWSAddin
{
    internal class BlockNode
    {
        private Database _database;
        /* public static Document AssemDoucument = null;
        Database _database;
        public Database HisDB
        {
            get { return _database; }
            set { _database = value; }
        }
         Point3d position = new Point3d();
        public Point3d Position
        {
            get { return position; }
            set { position = value; }
        }

       

        public void Dispose()
        {
            //for( int i=0;i< child)
            //和我的代码不一样啊？ 电话
          
            if (_database != null)
            {
                _database.Dispose();
                _database = null;
            }
        }
         List<CADNamingPoint> _lsNamingPoint = new List<CADNamingPoint>();
          public bool Exec( Component rootComp,string fileName)
        {
            //0 打开模型
            if (!OpenModel(rootComp,fileName)) return false;

            //1 驱动动态块
            DriveModel(rootComp);

            //2 分析数据库
            ParseNamingPoint(HisDB);

            //3 递归子模型
            for (int i = 0; i < rootComp.ChildNodes.Count; i++)
            {
                if (!string.IsNullOrEmpty(rootComp.ChildNodes[i].DrawingName))
                {
                    string ChildFilename = Path.GetDirectoryName(fileName) + "\\" + rootComp.ChildNodes[i].DrawingName + ".dwg";
                    File.Copy(rootComp.ChildNodes[i].ModelName, ChildFilename, true);
                    Exec(rootComp.ChildNodes[i], ChildFilename);
                }
             
            }

            //4 块装配
            for (int i = 0; i <  rootComp.ChildNodes.Count; i++)
            {
                if (!string.IsNullOrEmpty(rootComp.ChildNodes[i].DrawingName))
                {
                    AssemChildBlock(rootComp.CADDatabase, rootComp.ChildNodes[i]);
                }
               
            }

            //5
            //if (Parent != null)
            //{
            //    WblockDB();
            //}
            //6保存文件
             SaveAsFile(rootComp, fileName);
            return true;
        }




        //打开模型
        private bool OpenModel(Component component,string ModelName)
        {
            if( !File.Exists( ModelName))
            {
                Palettes.WriteLog( string.Format("{0}没有找到", ModelName)); return false;
            }

            if (component.Parent == null)
            {
                AssemDoucument = Application.DocumentManager.Open(ModelName);
                //if (_database==null)
               // {
                    _database = AssemDoucument.Database;  
               // }
                
            }
            else
            {
              //  if (_database == null)
              //  {
                    _database = LoadWBlock(ModelName);
               // }
                
            }

            return true;
        }
        //保存模型
        public void SaveAsFile(Component component, string ModelName)
        {
            if (!string.IsNullOrEmpty(component.DrawingName))
            {

                string fileName = Path.GetDirectoryName(ModelName)+ "\\" + component.DrawingName + ".dwg";
                Palettes.WriteLog(string.Format("保存文件 {0}", fileName));
                string path = Path.GetDirectoryName(fileName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                HisDB.SaveAs(fileName, DwgVersion.AC1015);
                //Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument = docs.Add(fileName);
            }
        }

        ///加载模型
         public static Database LoadWBlock(string blkFile)
        {
            Database blkDb = new Database(false, true);
            try
            {
                blkDb.ReadDwgFile(blkFile, System.IO.FileShare.Read, true, null);
                blkDb.CloseInput(true);
            }
            catch (System.Exception ex)
            {
                Application.ShowAlertDialog(ex.ToString());
            }
            return blkDb;
        }
        
        
        //2 分析数据库中对象
         private void ParseNamingPoint(Database HisDB)
        {
            using (DocumentLock docLock = AssemDoucument.LockDocument())
            {
               // ExecuteContext context = new ExecuteContext(HisDB);
                using (Transaction trans = HisDB.TransactionManager.StartTransaction())
                {
                    try
                    {
                        BlockTableRecord btr = GetBlockTableRecord();
                        foreach (ObjectId objId in btr)
                        {
                            if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(DBPoint))))
                            {
                                DBPoint pt = trans.GetObject(objId, OpenMode.ForRead) as DBPoint;
                                GetNamingPoint(pt);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        
                        Palettes.WriteLog(string.Format("分析命名点错误：{0},错误发生在：{1}",ex.ToString(),ex.StackTrace),2);
                    }

                }
            }
        }

        ////判断是否存在指定快
         public BlockTableRecord GetBlockTableRecord( OpenMode openMode = OpenMode.ForWrite)
        {
            try
            {
                BlockTable bt = HisDB.TransactionManager.StartTransaction().GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                return HisDB.TransactionManager.StartTransaction().GetObject(bt[BlockTableRecord.ModelSpace], openMode) as BlockTableRecord;
            }
            catch(System.Exception)
            {
                return null;
            }
        }


        
        //判断是否存在指定块
        private BlockTableRecord GetBlockTableRecord(string blkName)
        {
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                {
                    foreach (ObjectId objId in bt)
                    {
                        BlockTableRecord btr1 = objId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                        if (string.Compare(btr1.Name, blkName, true) == 0)
                        {
                            btr = btr1;
                            break;
                        }                        
                    }                                        
                }
                                
                return btr;
            }
        }
       
              

        //1驱动动态块
        public void DriveDynamicBlock(Component component,ObjectId blkRefId)
        {
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                DocumentLock docLock = AssemDoucument.LockDocument();

                try
                {
                    BlockReference blkRef = tran.GetObject(blkRefId, OpenMode.ForWrite) as BlockReference;  //报错
                    if (blkRef == null) return;

                    //1 设置属性
                    for (int i = 0; i < blkRef.AttributeCollection.Count; i++)
                    {
                        AttributeReference ar = tran.GetObject(blkRef.AttributeCollection[i], OpenMode.ForWrite) as AttributeReference;
                        Function para = component.Functions.Where(e => e.OperObject == ar.Tag).FirstOrDefault();
                        if (para != null)
                        {
                            ar.TextString = para.Value;
                        }
                    }

                    //2 设置动态属性
                    if (blkRef.IsDynamicBlock)
                    {
                        for (int i = 0; i < blkRef.DynamicBlockReferencePropertyCollection.Count; i++)
                        {
                            string propname = blkRef.DynamicBlockReferencePropertyCollection[i].PropertyName;

                            Function para = component.Functions.Where(e => e.OperObject == propname).FirstOrDefault();
                            if (para != null)
                            {
                                SetPropertyValue(blkRef.DynamicBlockReferencePropertyCollection[i], para.Value);
                            }
                        }
                    }

                    tran.Commit();
                }
                catch (Exception ex)
                {

                    Palettes.WriteLog(string.Format("设置属性错误：{0},错误发生在：{1}", ex.ToString(),ex.StackTrace),2);
                }

            }
        }
        ///设置属性值
         public static void SetPropertyValue(DynamicBlockReferenceProperty prop, string value)
        {
            double pi = 3.1415926;
            if (prop.Value is double)
            {
                if (prop.UnitsType == DynamicBlockReferencePropertyUnitsType.Angular)
                {
                    prop.Value = double.Parse(value)*pi/180;
                }
                else
                {
                    prop.Value = double.Parse(value);
                }
            }
            else if (prop.Value is int)
            {
                prop.Value = int.Parse(value);
            }
            else if (prop.Value is string)
            {
                prop.Value = value;
            }
        }


        //驱动模型
        private void DriveModel(Component component)
        {
            using (Transaction trans = HisDB.TransactionManager.StartTransaction())
            {
                ObjectId btrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                BlockTableRecord btr = trans.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                ObjectIdCollection collect = new ObjectIdCollection();

                foreach (ObjectId objId in btr)
                {         
                    collect.Add( objId);
                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(BlockReference))))
                    {
                        DriveDynamicBlock(component,objId);

                        BlockReference blkRef = trans.GetObject(objId, OpenMode.ForWrite) as BlockReference;

                        this.Position = blkRef.Position;

                        blkRef.ExplodeToOwnerSpace();

                        blkRef.Erase(true);
                        if (blkRef.IsDynamicBlock)
                        {
                            BlockTableRecord dynBtr=  trans.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForWrite) as BlockTableRecord;
                            dynBtr.Erase(true);                       
                        }

                    }
                }
                                
                trans.Commit();

                
            }
        }



        private bool CloneDynamicBlock(Database sourceDB, string blkName)
        {
            try
            {
                using (DocumentLock docLock = AssemDoucument.LockDocument())
                {
                    using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                    {
                        BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                        if (bt.Has(blkName)) return true;

                        using (Transaction tran1 = sourceDB.TransactionManager.StartTransaction())
                        {
                            BlockTable bt1 = tran1.GetObject(sourceDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                            if (!bt1.Has(blkName)) return false;

                            ObjectIdCollection collect = new ObjectIdCollection(new ObjectId[] { bt1[blkName] });

                            //当前块表记录
                            ObjectId curDBId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);
                            sourceDB.WblockCloneObjects(collect, curDBId, new IdMapping(), DuplicateRecordCloning.Replace, false);
                        }

                        tran.Commit();
                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
               Palettes.WriteLog(string.Format("克隆块{0}失败！" + ex.ToString(), blkName));
                return false;
            }

            return true;
        }


        //克隆子级对象
        private bool WblockChildNodeDB( Component rootComp)
        {
            try
            {
                using (DocumentLock docLock = AssemDoucument.LockDocument())
                {
                    Database db1 = new Database();
                    using (Transaction tran = HisDB.TransactionManager.StartTransaction())
                    {
                        BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;

                        using (Transaction tran1 = HisDB.TransactionManager.StartTransaction())
                        {
                            ObjectId childBtrId = SymbolUtilityServices.GetBlockModelSpaceId(HisDB);

                            BlockTableRecord btr1 =  tran1.GetObject(childBtrId, OpenMode.ForRead) as BlockTableRecord;
                            ObjectIdCollection collect = new ObjectIdCollection();

                            foreach (ObjectId objId in btr1)
                            {
                                collect.Add(objId);
                            }

                            HisDB.Wblock(db1, collect, Position, DuplicateRecordCloning.Replace);
                       //     db1.SaveAs(string.Format(@"d:\{0}.dwg", child.DrawingName), DwgVersion.Current);
                        }

                        tran.Commit();
                        
                        HisDB.Dispose();
                        HisDB = db1;                     

                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ex)
            {
                Palettes.WriteLog( ex.ToString());               
            }

            return true;
        }
        
        //装配下一级节点
        private void AssemChildBlock(Database rootDatabase, Component rootComp)
        {
            
            WblockChildNodeDB(rootComp);
            InsertChildNode(rootDatabase, rootComp);
        }

        private void InsertChildNode(Database rootDatabase, Component rootComp)
        {
            using (DocumentLock dlock = AssemDoucument.LockDocument())
            {
                using (Transaction tran = rootDatabase.TransactionManager.StartTransaction())
                {

                    ObjectId blkObjId = rootDatabase.Insert(rootComp.DrawingName, HisDB, false);   //有问题

                    CADNamingPoint np = GetInsetPoint(rootComp);

                    Point3d insetPoint = new Point3d();
                    if (np != null)
                    {
                        np.IsInserted = true;
                        insetPoint = np.Point;
                    }

                    BlockReference bref = new BlockReference(insetPoint, blkObjId);

                    BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                    

                    blkObjId = btr.AppendEntity(bref);
                    tran.AddNewlyCreatedDBObject(bref, true);
                   
                    bref.Dispose();                   

                    tran.Commit();
                }
            }
        }


        //获得子级节点的插入点
        private CADNamingPoint GetInsetPoint(Component component)
        {   
            for (int i = 0; i < _lsNamingPoint.Count; i++)
            {
                if (_lsNamingPoint[i].IsInserted) continue;

                string s1 = _lsNamingPoint[i].Name;
                int pos = s1.LastIndexOf('.');
                if (pos > 0)
                {
                    s1 = s1.Substring(0, pos);
                }

                if (s1 == component.DrawingName)
                {
                    
                    return _lsNamingPoint[i];
                }
            }
            return null;
        }


        //插入一个图块,从当前数据库
        private void InsertBlock(string blkName, Point3d point)
        {
            BlockTableRecord btr = GetBlockTableRecord(blkName);

            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {

                BlockReference bref = new BlockReference(point, btr.ObjectId);


                BlockTable bt = tran.GetObject(HisDB.BlockTableId, OpenMode.ForRead) as BlockTable;
                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                btr.AppendEntity(bref);
                tran.AddNewlyCreatedDBObject(bref, true);
                bref.Dispose();

                tran.Commit();
            }
        }

        private string GetDynamicBlockName(ObjectId blkRefId)
        {
            using (Transaction tran = HisDB.TransactionManager.StartTransaction())
            {
                BlockReference blkRef = tran.GetObject(blkRefId, OpenMode.ForRead) as BlockReference;
                if (blkRef == null) return "";

                if(blkRef.IsDynamicBlock)
                {
                    BlockTableRecord btr = tran.GetObject(blkRef.DynamicBlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    return btr.Name;
                }
                return "";
            }
        }
        


        //获得图形中的块引用、命名点
        private void GetNamingPoint(DBPoint pt)
        {
            ResultBuffer rb = pt.XData;
            if (rb == null) return;

            foreach (TypedValue tv in rb)
            {
                if (tv.TypeCode == (int)DxfCode.ExtendedDataAsciiString)
                {
                    if (tv.Value != null && !string.IsNullOrEmpty(tv.Value.ToString()))
                    {
                        CADNamingPoint namingPoint = new CADNamingPoint();
                        namingPoint.Point = pt.Position;
                        namingPoint.Name = tv.Value.ToString();
                        _lsNamingPoint.Add(namingPoint);

                        break;
                    }
                }
            }
            rb.Dispose();
        }
        
    }
    */

        /* public class CADNamingPoint
    {
        public string Name { get; set; }
        public Point3d Point { get; set; }

        public bool IsInserted { get; set; }
    }*/

        //}
    }
}
