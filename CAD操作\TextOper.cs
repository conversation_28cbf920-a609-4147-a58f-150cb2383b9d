﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Geometry;
using System.Xml;
using CADWSAddin.Tools;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
namespace CADWSAddin
{
    public class TextOper
    {
        private bool _foramt = true;

        public TextOper(bool format = true)
        {
            _foramt = format;
        }


        //创建单行文本
        public object InsertText(string str, Point3d pt, double fontHeight = 2.5)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            Document doc = Application.DocumentManager.MdiActiveDocument;

            DBText text;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead, false);
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite, false);
                    text = new DBText();
                    text.TextString = str;

                    text.Position = pt;
                    text.Height = fontHeight;
                    btr.AppendEntity(text);
                    trans.AddNewlyCreatedDBObject(text, true);

                    if (_foramt)
                    {
                        FormatText(text);
                    }

                    trans.Commit();
                    trans.Dispose();
                }
            }

            return text;
        }


        //创建多行文本
        public MText InsertMText(string str, Point3d pt, double width, double height, double fontHeight = 2.5)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            Document doc = Application.DocumentManager.MdiActiveDocument;
            MText mt;


            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btr = db.CurrentSpaceId.GetObject(OpenMode.ForWrite) as BlockTableRecord;
                    mt = new MText();

                    mt.Contents = str;
                    mt.Location = pt;
                    mt.Width = width;
                    mt.Height = height;
                    mt.TextHeight = fontHeight;
                    mt.SetDatabaseDefaults();

                    btr.AppendEntity(mt);
                    trans.AddNewlyCreatedDBObject(mt, true);

                    if (_foramt)
                    {
                        FormatText(mt);
                    }

                    if (mt.ActualHeight > 2 * mt.TextHeight)
                    {
                        mt.TextHeight = 2;
                    }

                    mt.Location = new Point3d(mt.Location.X, mt.Location.Y + mt.ActualHeight, 0);
                    trans.Commit();
                }
            }

            return mt;
        }

        public virtual void FormatText(object ent)
        {
            //子类实现
        }

        public String TrimMText(string text)
        {
            int found = text.IndexOf("/");
            string newText = text.Substring(found + 1).Replace("/", "").Replace("}", "");
            return newText;
        }
        public List<string> GetMText(List<string> ls)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject obj = trans.GetObject(blcokId, OpenMode.ForRead);
                    if (obj is DBText)
                    {
                        DBText mt = obj as DBText;
                        
                        if (mt.TextString.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.TextString));
                        }
                    }
                    else if (obj is MText)
                    {
                        MText mt = obj as MText;

                        if (mt.Text.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.Contents));
                        }
                    }
                    else if (obj is Dimension)
                    {
                        Dimension mt = obj as Dimension;

                        if (mt.DimensionText.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.DimensionText));
                        }

                    }
                    else if (obj is FeatureControlFrame)
                    {
                        FeatureControlFrame mt = obj as FeatureControlFrame;

                        if (mt.Text.Contains("/"))
                        {
                            ls.Add(TrimMText(mt.Text));
                        }
                    }
                }
                trans.Commit();
                return ls;
            }
        }
        public void ReplaceMText(string str1, string str2)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                    foreach (ObjectId blcokId in btrBlock)
                    {
                        DBObject obj = trans.GetObject(blcokId, OpenMode.ForWrite);
                        if (obj is DBText)
                        {
                            DBText mt = obj as DBText;

                            if (mt.TextString.Contains(str1))
                            {
                                mt.TextString=mt.TextString.Replace(str1, str2);
                            }
                        }
                        else if (obj is MText)
                        {
                            MText mt = obj as MText;

                            if (mt.Text.Contains(str1))
                            {
                                mt.Contents = mt.Contents.Replace(str1, str2);
                            }
                        }
                        else if (obj is Dimension)
                        {
                            Dimension mt = obj as Dimension;

                            if (mt.DimensionText.Contains(str1))
                            {
                                mt.DimensionText=mt.DimensionText.Replace(str1, str2);
                            }
                            
                        }
                        else if (obj is FeatureControlFrame)
                        {
                            FeatureControlFrame mt = obj as FeatureControlFrame;

                            if (mt.Text.Contains(str1))
                            {
                                mt.Text=mt.Text.Replace(str1, str2);
                            }
                        }
                    }
                    trans.Commit();
                }
            }
        }


        public void RemovePlaceMText(string str1)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    BlockTableRecord btrBlock = doc.Database.CurrentSpaceId.GetObject(OpenMode.ForRead) as BlockTableRecord;
                    foreach (ObjectId blcokId in btrBlock)
                    {
                        DBObject obj = trans.GetObject(blcokId, OpenMode.ForWrite);

                        if (obj is MText)
                        {
                            MText mt = obj as MText;

                            if (mt.Text.Contains(str1))
                            {
                                mt.Contents = mt.Contents.Replace(" ", "");
                            }
                        }
                    }
                    trans.Commit();
                }
            }
        }
    }
    public static class TextStyleOper
    {
        public static List<string> GetTextStyleName(this Database db)
        { 
            List<string> ls = new List<string>();
            try
            {
                using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                    TextStyleTable dst = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
                    foreach (ObjectId id in dst)
                    {
                        if (id.GetObject(OpenMode.ForRead).GetRXClass().DxfName == "STYLE")
                        {
                            TextStyleTableRecord dstr = (TextStyleTableRecord)id.GetObject(OpenMode.ForRead);
                            ls.Add(dstr.Name);
                        }
                    }
                }
            }
            catch
            {
            }
            return ls;
        }
        public static void ChangeTextStyle(this Database db, string oldStyleName, string newStyleName)
        {
            try
            {
                //using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    TextStyleTable dst = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
                    if (dst.Has(oldStyleName) && dst.Has(newStyleName))
                    {
                        List<MText> Mtexts = new List<MText>();
                        foreach (Entity ent in db.GetEntsInDatabase())
                        {
                            if (ent.GetRXClass().DxfName == "MTEXT")
                            {
                                MText text = (MText)ent;
                                Mtexts.Add(text);
                            }
                        }
                        foreach (MText text in Mtexts)//遍历多行文字列表
                        {
                            if (text.TextStyleName == oldStyleName)
                            {
                                text.UpgradeOpen();
                                text.TextStyleId = dst[newStyleName];
                                text.DowngradeOpen();
                            }
                        }
                        List<DBText> texts = new List<DBText>();
                        foreach (Entity ent in db.GetEntsInDatabase())
                        {
                            if (ent.GetRXClass().DxfName == "TEXT")
                            {
                                DBText text = (DBText)ent;
                                texts.Add(text);
                            }
                        }
                        foreach (DBText text in texts)//遍历单行文字列表
                        {
                            if (text.TextStyleName == oldStyleName)
                            {
                                text.UpgradeOpen();
                                text.TextStyleId = dst[newStyleName];
                                text.DowngradeOpen();
                            }
                        }
                    }
                    else return;
                    trans.Commit();
                }
            }
            catch
            {
            }
        }
        public static List<string> xmlTextList(string ChildNode)
        {
            string xmlName = "C:\\Users\\<USER>\\Documents\\Visual Studio 2012\\Projects\\CADWSAddin\\CADWSAddin\\texttest.xml";
            List<string> ls = new List<string>();
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            ChildNode = "//" + ChildNode;
            XmlNodeList nodes = doc.SelectNodes(ChildNode);
            foreach (XmlElement node in nodes)
            {
                for (int i = 0; i < node.ChildNodes.Count; i++) //列的循环,为每个列指定名称
                {
                    ls.Add(node.ChildNodes[i].Value);
                }
            }
            return ls;
        }
    }
}
