﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.ApplicationServices;
using System.Xml;



namespace CADWSAddin
{
    public class TableOper
    {
        Table _table = null;
        public TableOper(Table t)
        {
            _table = t;
        }

        public string GetTableTitle()
        {
            if (_table == null || _table.Cells.Count() == 0) return "";
            if (_table.Cells[0, 0].TextString.Contains(";"))
            {
                int found = _table.Cells[0, 0].TextString.IndexOf(";");
                return _table.Cells[0, 0].TextString.Substring(found + 1).Replace("}","");
            }
            else return _table.Cells[0, 0].TextString;
        }
        public int GetColumnsCount()
        {
            int count = _table.Columns.Count();
            return count;
        }
        public int GetRowsCount()
        {
            int count = _table.Rows.Count();
            return count;
        }
        public List<string> GetColumn()
        {
            List<string> ls = new List<string>();
            if (_table == null || _table.Rows.Count() < 2) return ls;

            for (int i = 0; i < _table.Columns.Count(); i++)
            {
                ls.Add(_table.Cells[1, i].TextString);
            }

            return ls;
        }

        public List<string> GetRow(int row)
        {
            List<string> ls = new List<string>();
            if (_table == null || row > _table.Rows.Count()) return ls;


            for (int i = 0; i < _table.Columns.Count(); i++)
            {
                ls.Add(_table.Cells[row, i].TextString);
            }

            return ls;
        }


        private void AddRow(int count = 1)
        {
            if (_table == null) return;

            int c = _table.Rows.Count();
            _table.InsertRows(c, _table.Rows[c - 1].Height, count);
        }

        public void InsertData(List<string> ls)
        {
            if (_table == null) return;

            AddRow();

            int c = _table.Rows.Count();
            for (int i = 0; i < _table.Columns.Count(); i++)
            {
                if (i < ls.Count)
                {
                    _table.Cells[c - 1, i].TextString = ls[i];
                }
            }
        }
        public void UpdateData(int row, List<string> ls)
        {
            if (_table.Rows.Count() < row) return;

            for (int j = 0; j < _table.Columns.Count() && j < ls.Count; j++)
            {
                if (ls[j] != "")
                {
                    _table.Cells[row, j].TextString = ls[j];
                    //_table.Cells[row, j].TextHeight = 2.5;
                }
            }
        }
        public List<string> htmlTable(string tableStr)
        {       
            //string tableStr1 = "<table><tbody><tr><td>1</td><td>11</td></tr><tr><td>2</td><td>22</td></tr></tbody></table>";
            List<string> ls = new List<string>();
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(tableStr);
            XmlNodeList nodes = doc.SelectNodes("/table//tr");
            foreach (XmlElement node in nodes)
            {
                for (int i = 0; i < node.ChildNodes.Count; i++) //列的循环,为每个列指定名称
                {
                   // XmlNode n = doc.CreateNode(XmlNodeType.Element, "col" + i.ToString(), "");
                  //  n.InnerXml = node.ChildNodes[i].InnerXml;
                   // node.ReplaceChild(n, node.ChildNodes[i]);
                    ls.Add(node.ChildNodes[i].InnerText);
                }
            }
            return ls;
        }
        public List<string[]> htmlTable2(string tableStr)
        {
            List<string[]> talbe = new List<string[]>();
            try
            {

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(tableStr);
                XmlNodeList nodes = doc.SelectNodes("/table//tr");
                foreach (XmlElement node in nodes)
                {
                    List<string> ls = new List<string>();
                    for (int i = 0; i < node.ChildNodes.Count; i++) //列的循环,为每个列指定名称
                    {

                        ls.Add(node.ChildNodes[i].InnerText);
                    }
                    talbe.Add(ls.ToArray());
                }
                return ConvertYToX(talbe);
            }
            catch (Exception e)
            {

                return new List<string[]>();
            }
          
        }
        private List<string[]> ConvertYToX(List<string[]> talbe)
        {
            List<string[]> temp = new List<string[]>();
            int columCount = talbe.Count;
            int rowCount = talbe.Count == 0 ? 0 : talbe[1].Length;
            for (int i  = 0; i  < rowCount; i ++)
            {
                string[] nRow = new string[columCount];
                for (int j = 0; j < columCount; j++)
                {
                    string[] r = talbe[j];
                    if (r.Length > i)
                    {
                        nRow[j] = r[i];
                    }
                    else
                    {
                        nRow[j] = "";
                    }
                }
                temp.Add(nRow);
            }
            return temp;

        }

        public void Format(List<Parameter> paras)
        {
            if (_table == null) return;

            for (int i = 2; i < _table.Rows.Count; i++)
            {
                for (int j = 0; j < _table.Columns.Count(); j++)
                {
                    _table.Cells[i, j].TextString = FormatText(_table.Cells[i, j].TextString, paras);
                }

            }

        }


        private string FormatText(string s, List<Parameter> paras)
        {
            s = s.Replace("\\{", "{");
            s = s.Replace("\\}", "}");

            string searchtext = s;
            string para = Utility.ExtractFormatPara(searchtext);
            while (para != "")
            {
                Parameter value = paras.Where(e => e.Name == para).FirstOrDefault();
                if (value != null)
                {
                    s = s.Replace("{" + para + "}", value.Format());
                    searchtext = s;
                }
                else
                {
                    int pos = searchtext.IndexOf('}');
                    if (pos != -1)
                    {
                        searchtext = searchtext.Substring(pos + 1);
                    }
                    else
                    {
                        searchtext = "";
                    }
                }

                para = Utility.ExtractFormatPara(searchtext);
            }

            return s;
        }
    }
}
