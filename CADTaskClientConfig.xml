﻿<?xml version="1.0" encoding="utf-8"?>
<CADTaskClientConfig>
  <MakeProjectMap>false</MakeProjectMap>
  <MakeDWG>true</MakeDWG>
  <MakePDF>true</MakePDF>
  <!-- ˇرըߪࠩ -->
  <MakeSpreadMap>true</MakeSpreadMap>
  <ExpendViewName>٤Ԍͼ˓ͼ1</ExpendViewName>
  <AttachmentType>PDF;DWG;JSON;</AttachmentType>
  <IsHaveAttachment>true</IsHaveAttachment>
  <!-- ԟէғʬׁ࠲Ś -->
  <IsCutline>true</IsCutline>
  <SaveObject>1</SaveObject>
  <!-- ģэτݾĿ¼ -->
  <PdsPath>D:\Drawing\AutoCAD</PdsPath>
  <!-- ٤طĿ¼ -->
  <WorkPath>D:\CADWorkDir</WorkPath>
  <!--<WorkPath>D:\CADWorkDir</WorkPath>-->
  <BakPath>D:\DrawingBak</BakPath>
  <!-- Ɏϱ`э 600úֽʽû100úӢ˔ -->
  <TaskType>600</TaskType>
  <CreateUser></CreateUser>
  <DeleteUnuseConfig></DeleteUnuseConfig>
  <WCFCADServer>net.tcp://localhost:8080/CADWCFServer</WCFCADServer>
  <WCFEdsServer>net.tcp://*************:602/Task/TaskService</WCFEdsServer>
  <RevitUrl>net.tcp://*************:9081/RevitDriver/LDS</RevitUrl>
  <SelectPTId></SelectPTId>
  <DWGVersion>2010</DWGVersion>
  <IsReturnCADstatus>true</IsReturnCADstatus>
  <UseDocumentDb>true</UseDocumentDb>
	<UseModelCache>true</UseModelCache>
	<UseDocumentDbResult>true</UseDocumentDbResult>
  <UseDocumentDbTemplate>false</UseDocumentDbTemplate>
	<!--pu的次数,如果不大于0就不执行-->
	<PuTimes>1</PuTimes>
	<!--任务状态为400后突然中断， 那该任务会在updatetime60分钟后再次执行-->
	<Status400RerunAfterMinutes>60</Status400RerunAfterMinutes>
	<!--干涉检查-->
	<InterFerenceDetection>true</InterFerenceDetection>
	<InterFerenceImage>true</InterFerenceImage>
	<MaxLogFileSize>1</MaxLogFileSize>
	<SendbackUrl>http://*************:8070/ecs/LDSWebApi/TaskOverRequest</SendbackUrl>
 <!-- EGI ТoՆ8000-->

<EGIService>http://************:8000/</EGIService>
<EGIUserNo>admin_b</EGIUserNo>
<EGIPsw>1</EGIPsw>
</CADTaskClientConfig>