﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Serialization;
using CADWSAddin.Tools;
using Zxtech.CADTaskServer.Contract;

namespace CADWSAddin.XML
{
    [Serializable]
    public class Node
    {
    
        public Node()
        {
            Functions = new List<Function>();
            Parameters = new List<Parameter>();
            ChildNodes = new List<Node>();
            IsCompelted = false;
        }
        public string ModelName { get; set; }
        public string ModeDefault { get; set; }
        public string DxfName { get; set; }
        public string DrawingName { get; set; }
        public string BlockName { get; set; }
        public string MergeFileName { get; set; }
        public double RotateAngle { get; set; }
        public double PaperSizeWidth { get; set; }
        public double PaperSizeHeight{ get; set; }

        public bool NoMerge = false;
        public List<Parameter> Parameters { get; set; }
        public List<Function> Functions { get; set; }
        public List<Node> ChildNodes { get; set; }
        public  int PartId { get; set; }
        [NonSerialized]
        private bool IsCompelted;
        [NonSerialized]
        private Node ParentNode;

        public string GetCombineFileName()
        {
            if (!string.IsNullOrWhiteSpace(MergeFileName)) return MergeFileName;

            foreach (var item in ChildNodes)
            {
                var ret = item.GetCombineFileName();
                if (!string.IsNullOrWhiteSpace(ret)) return ret;
            }

            return string.Empty;
        }





        //判断出图数
        public static int DrawingCount(List<CADTaskCode> ls)
        {
            int count = 0;
            foreach (CADTaskCode CADTaskCode in ls)
            {
                if (CADTaskCode.Para7.Contains(@"Y\"))
                    count++;
            }
            return count;
        }
        //按出图数分割任务list
        public static List<List<CADTaskCode>> DivisionList(List<CADTaskCode> ls)
        {
            ls = ls.Where(o => o.CadCodeId == 1200 || o.CadCodeId == 1300).OrderBy(o => o.SortId).ToList();
            List<CADTaskCode> list = new List<CADTaskCode>();
            List<List<CADTaskCode>> asslist = new List<List<CADTaskCode>>();
            int m = 0; //标识code在ls中的位置
            int level = 0;//所在层
            foreach (var code in ls)
            {
                if (code.CadCodeId == 1200) level++;
                else level--;
                if (level == 1 && !string.IsNullOrEmpty(code.Para7) && code.Para7.Contains(@"Y\"))
                {
                    list.Add(ls[m]);
                    int n = 1; //匹配子件判断跳出
                    int k = 0; //结合n 定义局部自增
                    do
                    {
                        k++;
                        list.Add(ls[m + k]);

                        if (ls[m + k].CadCodeId == 1200) n++;
                        else n--;

                    }
                    while (n != 0);

                    asslist.Add(list);
                    list = new List<CADTaskCode>();
                }
                m++;
            }
            foreach (var codelist in asslist)
            {
                if (!string.IsNullOrEmpty(codelist[0].Para7) && codelist[0].Para7.Contains(@"Y\"))//per 以Y开头,输出文件,y\后面是可配置的文件名
                {
                    //codelist[0]是0级的图纸, 是最后生成完要保留的文件,dwg名称是para7,pdf是否输出要看配置文件的设置, dxf是否输出是看cadtaskcode的para9
                    codelist[0].Files.Add(codelist[0].Para7.Replace(@"Y\", "") + ".dwg");
                    if (Palettes.stationInfo.bMakePDF)
                    {
                        codelist[0].Files.Add(codelist[0].Para7.Replace(@"Y\", "") + ".pdf");
                    }
                    if (!string.IsNullOrEmpty(codelist[0].Para9) && codelist[0].Para9.Contains(@"Y\"))
                    {
                        codelist[0].Files.Add(codelist[0].Para9.Replace(@"Y\", "") + ".dxf");
                    }
                }
            }

            return asslist;
        }
        public static Node CreateNode(List<CADTaskCode> ls, string Pdspath, bool makeDxf)
        {
            
                int i = 0;//遍历次数
                Node firstNode = null;
                Node currentNode = null;
                var lst = ls.Where(o => o.CadCodeId == 1200 || o.CadCodeId == 1300).OrderBy(o => o.SortId).ToList();
            try
            {
                foreach (var code in lst)
                {
                    i++;
                    if (code.CadCodeId == 1200)
                    {
                        if (currentNode == null)
                        {
                            firstNode = new Node();
                            currentNode = firstNode;
                            currentNode.Load(code, Pdspath, makeDxf);
                        }
                        else
                        {
                            var tempNode = new Node();
                            tempNode.Load(code, Pdspath, makeDxf);
                            if (!currentNode.IsCompelted)
                            {
                                currentNode.ChildNodes.Add(tempNode);
                                tempNode.ParentNode = currentNode;
                            }
                            else
                            {
                                if (currentNode.ParentNode != null)
                                {
                                    currentNode.ParentNode.ChildNodes.Add(tempNode);

                                }
                                tempNode.ParentNode = currentNode.ParentNode;
                            }
                            currentNode = tempNode;

                        }
                    }
                    else if (code.CadCodeId == 1300)
                    {
                        if (currentNode != null)
                        {
                            if (!currentNode.IsCompelted)
                                currentNode.Load(code, Pdspath, makeDxf);
                            else
                            {
                                currentNode = currentNode.ParentNode;
                                if (currentNode != null)
                                {
                                    currentNode.Load(code, Pdspath, makeDxf);
                                }
                            }
                        }
                    }
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception e1)
            {
                Palettes.WriteLog(e1.Message, 2);
                Palettes.WriteLog(e1.StackTrace, 2);
            }
            catch(Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
            }
            return firstNode;
        }
    
        public void Load(CADTaskCode code, string PdsPath, bool makeDxf)
        {
            
               
                PartId = code.PartId;
           
                if (code.CadCodeId == 1200)
                {
                    if (code.Para3 != null)
                    {
                        code.Para3 =code.Para3.TrimStart('\\');
                    }
                    ModelName = Path.Combine(PdsPath,code.Para3.TrimStart('\\', '/'));

                    SetBlockName();
                    ModeDefault = ModelName;
                    //图纸命名
                    if (code.Para7.Contains(@"Y\"))
                    {
                       this.DrawingName = code.Para7.Replace(@"Y\", "");
                    }
                    if (makeDxf && code.Para9 != null && code.Para9.Contains(@"Y\"))
                    {
                        this.DxfName = code.Para9.Replace(@"Y\", "");
                    }
                if (code.Para15!=null && code.Para15.Contains("paperSize"))
                {
                    this.PaperSizeWidth = double.Parse(code.Para15.Split(',')[1].Split('|')[0].Trim('\''));
                    this.PaperSizeHeight = double.Parse(code.Para15.Split(',')[1].Split('|')[1].Trim('\''));
                }

                var para1dic = SingleQuoteStringParser.UnpackDictionary(code.Para1);
                foreach (var pitem in para1dic)
                {
                    SetFunctionAndBlock(pitem);

                }

            }
            else if (code.CadCodeId == 1300)
                {
                
                    GetFunctions(code);
                    IsCompelted = true;
                }

                //FileName
                UpdateOtherParam(code);
        }


        private void UpdateOtherParam(CADTaskCode code)
        {
            if (!string.IsNullOrWhiteSpace(code.Para1))
            {
                var splitChar = '\'';
                if (code.Para1.StartsWith("|") && code.Para1.EndsWith("|"))
                {
                    splitChar = '|';
                }
                var kvs = code.Para1.TrimStart(splitChar).TrimEnd(splitChar).Split(new List<string> { splitChar + "," + splitChar }.ToArray(), StringSplitOptions.None).ToList();
                for (int i = 0; i < kvs.Count; i += 2)
                {
                    if (string.IsNullOrWhiteSpace(kvs[i])) continue;

                    if (kvs[i].Contains("FileName"))
                    {
                        var val = kvs[i + 1];
                        MergeFileName = string.IsNullOrWhiteSpace(val) ? string.Empty : val;
                    }
                    else if(kvs[i].Contains("RotateAngle"))
                    {
                        var val = kvs[i + 1];
                        RotateAngle = Utility.ConvertToDouble(val);
                    }
                    else if (kvs[i].Contains("NoMerge"))
                    {
                        var val = kvs[i + 1];
                        NoMerge = !string.IsNullOrEmpty(val) && val.ToUpper() != "N" && val.ToUpper() != "NO" && val.ToUpper() != "FALSE" && val != "0";
                    }
                }
            }
        }
       
      
        public List<Function> GetFunctions( CADTaskCode code)
        {
            //数字变量
            var para2dic= SingleQuoteStringParser.UnpackDictionary(code.Para2);
            foreach (var fitem in para2dic)
            {
                Function xmlF = new Function();
                xmlF.Name = "SetVariable";
                xmlF.OperObject = fitem.Key;
                xmlF.Value = fitem.Value;
                Functions.Add(xmlF);
            }
         
            //可见性&&翻转变量
            var para1dic= SingleQuoteStringParser.UnpackDictionary(code.Para1);
            foreach (var pitem in para1dic)
            {
                SetFunctionAndBlock(pitem);
           
            }
            var para4dic = SingleQuoteStringParser.UnpackListStringString(code.Para4);
            foreach (var fitem in para4dic)
            {
                Function xmlF = new Function();
                xmlF.Name = "SetBlock";
                xmlF.OperObject = fitem.Key;
                xmlF.Value = fitem.Value;
                Functions.Add(xmlF);
            }
            return Functions;
        }

        public string ToXmlStr(Node firstNode)
        {
            return "";
        }
        public static Regex FunctionRegex = new Regex(@"(.*)@(.*)", RegexOptions.None);
        public void SetFunctionAndBlock(KeyValuePair<string, string> kvItem)
        {
          var match =  FunctionRegex.Match(kvItem.Key);
          if (!match.Success) return;
          string head = match.Groups[1].Value;
          string operObject = match.Groups[2].Value;
          string funcName = string.Empty;
          switch (head)
          {
              case "DWG-VIS":
                  funcName = "SetVariable";
                  break;
              case "DWG-TXT":
                  funcName = "SetText";
                  break;
              case "DWG-TS":
                  funcName = "SetTextStyle";
                  break;
              case "DWG-DS":
                  funcName = "SetDimStyle";
                  break;
              case "DWG-LS":
                  funcName = "SetLayerVariable";
                  break;
              case "DWG-LLW":
                  funcName = "SetLayerLineWeight";
                  break;
              case "DWG-TB":
                  funcName = "SetTableVariable";
                  break;
              case "DWG-CD":
                  funcName = "SetDimVariable";
                  break;
              case "DWG-DIM":
                  funcName = "SetAddDim";//增加标注
                  break;
              

                // 块名
                case "DWG-IB":
                  this.BlockName = operObject;
                  break;
              case "DWG-MC":
                string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(this.ModelName);
                string ext=Path.GetExtension(this.ModelName);
                string dir = Path.GetDirectoryName(this.ModelName);
                this.ModelName = Path.Combine(dir, fileNameWithoutExtension + kvItem.Value, ext);
                    break;

                case "CONFIG":
                    if (operObject == "NoMerge")
                    {
                        if (kvItem.Value.ToLower()=="y")
                        {
                            this.NoMerge = true;
                        }
                        
                    }
                    break;

                default:
                  funcName = string.Empty;
                  break;
          }
          if(string.IsNullOrEmpty(funcName))
              return;
          Function func = new Function();
          func.Name= funcName;
          func.OperObject = operObject;
          func.Value = kvItem.Value;
          this.Functions.Add(func);
        }
        public void SetBlockName()
        {
            var fileName = FileName.Paser(this.ModelName);
            if (fileName.IsPaserSucess)
            
                this.ModelName=fileName.ModelName;
                this.BlockName = fileName.BlockName;
            }
 
        }

    }

    [Serializable]
    public class Parameter
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
    }

    [Serializable]
    public class Function
    {
        public string Name { get; set; }
        public string OperObject { get; set; }
        public string Value { get; set; }
    }

   public class FileName
   {
        public string ModelName {get;set;}
        public string BlockName {get;set;}
        public bool IsPaserSucess=false;
        public static Regex RegBlock = new Regex(@"(.*)@(.*).dwg", RegexOptions.IgnoreCase);

        public static  FileName Paser(string fileName)
        {
            FileName fName = new FileName();
            var match = RegBlock.Match(fileName);
            if (match.Success)
            {
                fName.IsPaserSucess=true;
                fName.ModelName=match.Groups[1].Value+".dwg";
                fName.BlockName = match.Groups[2].Value;
            }
           return fName;
        }
    
   }


