﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using System.Xml;

namespace CADWSAddin.Tools
{
    /// <summary>
    /// 标注样式操作类
    /// </summary>
    public static class DimStyleTools
    {
        /// <summary>
        /// 创建一个新的标注样式
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <param name="styleName">标注样式名</param>
        /// <returns>返回新建的标注样式的Id</returns>
        public static ObjectId AddDimStyle(this Database db, string styleName)
        {
            //打开标注样式表
            DimStyleTable dst = (DimStyleTable)db.DimStyleTableId.GetObject(OpenMode.ForWrite);
            if (!dst.Has(styleName))//如果不存在名为styleName的标注样式，则新建一个标注样式
            {
                //定义一个新的标注样式表记录
                DimStyleTableRecord dstr = new DimStyleTableRecord();
                dstr.Name = styleName;//设置标注样式名
                dst.UpgradeOpen();//切换标注样式表的状态为写以添加新的标注样式
                dst.Add(dstr);//将标注样式表记录的信息添加到标注样式表中
                //把标注式表记录添加到事务处理中
                db.TransactionManager.AddNewlyCreatedDBObject(dstr, true);
                dst.DowngradeOpen();//为了安全，将标注样式表的状态切换为读
            }
            return dst[styleName];//返回新添加的标注样式表记录的ObjectId
        }


        public static List<string> GetDimStyleName(this Database db)
        {
            List<string> ls = new List<string>();
            try
            {
                using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                    DimStyleTable dst = (DimStyleTable)db.DimStyleTableId.GetObject(OpenMode.ForRead);
                    foreach(ObjectId id in dst)
                    {
                        if (id.GetObject(OpenMode.ForRead).GetRXClass().DxfName == "DIMSTYLE")
                        {
                            DimStyleTableRecord dstr = (DimStyleTableRecord)id.GetObject(OpenMode.ForRead);
                            ls.Add(dstr.Name);
                        }
                    }
                }
            }
            catch
            {
            }
            return ls;
        }
        public static void ChangeDimStyle(this Database db, string oldStyleName, string newStyleName)
        {
            try
            {
                using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                    DimStyleTable dst = (DimStyleTable)db.DimStyleTableId.GetObject(OpenMode.ForRead);
                    if (dst.Has(oldStyleName) && dst.Has(newStyleName))
                    {
                        List<Dimension> dims = new List<Dimension>();
                        foreach (Entity ent in db.GetEntsInDatabase())
                        {
                            if (ent.GetRXClass().DxfName == "DIMENSION")
                            {
                                Dimension dim = (Dimension)ent;
                                dims.Add(dim);
                                //dim.SetDimstyleData(dstr);
                            }
                        }
                        foreach (Dimension dim in dims)//遍历标注列表
                        {
                            if (dim.DimensionStyleName == oldStyleName)
                            {
                                dim.UpgradeOpen();
                                dim.DimensionStyle = dst[newStyleName];
                                dim.DowngradeOpen();
                            }
                        }
                    }
                    else return;
                    trans.Commit();
                }
            }
            catch
            {
            }
        }
        public static List<string> xmlDimList(string ChildNode)
        {
            string xmlName = "C:\\Users\\<USER>\\Documents\\Visual Studio 2012\\Projects\\CADWSAddin\\CADWSAddin\\dimtest1.xml";
            List<string> ls = new List<string>();
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlName);
            ChildNode = "//" + ChildNode;
            XmlNodeList nodes = doc.SelectNodes(ChildNode);
            foreach (XmlElement node in nodes)
            {
                for (int i = 0; i < node.ChildNodes.Count; i++) //列的循环,为每个列指定名称
                {
                    ls.Add(node.ChildNodes[i].Value);
                }
            }
            return ls;
        }
    }
}
