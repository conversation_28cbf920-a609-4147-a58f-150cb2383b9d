﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.ApplicationServices;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.LayerManager;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;

namespace CADWSAddin
{
    public class DelHideLayer
    {
        public void Execute()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction acTrans = doc.Database.TransactionManager.StartTransaction())
                {
                    LayerTable lt;

                    lt = (LayerTable)acTrans.GetObject(doc.Database.LayerTableId, OpenMode.ForRead);

                    if (lt.Has("0") == true)
                    {
                        doc.Database.Clayer = lt["0"];
                    }

                    foreach (var item in lt)
                    {
                        LayerTableRecord ltr = (LayerTableRecord)acTrans.GetObject(item, OpenMode.ForRead);

                        if (ltr.IsOff)
                        {
                            ltr.UpgradeOpen();
                            ObjectIdCollection OidC = GetEntitiesOnLayer(ltr.Name);
                            try
                            {
                                if (OidC.Count > 0)
                                {
                                    for (int i = 0; i < OidC.Count; i++)
                                    {
                                        DBObject ent = acTrans.GetObject(OidC[i], OpenMode.ForWrite, true);
                                        ent.Erase();
                                    }

                                    ltr.Erase();

                                }
                                else
                                {
                                    ltr.Erase();
                                }
                            }
                            catch (Exception ex)
                            {
                                //"擦拭图层：{0}错误，错误内容为：{1}"
                                Palettes.WriteLog(string.Format(LanguageHelper.GetString("WipeLayerError"), ltr.Name,ex.ToString()+ex.StackTrace));
                            }
                           
                        }
                    }
                    acTrans.Commit();

                }
            }

            DeleteLayerFilter();

        }

        private static ObjectIdCollection GetEntitiesOnLayer(string layerName)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;

            Editor ed = doc.Editor;
            // Build a filter list so that only entities
            // on the specified layer are selected
            TypedValue[] tvs = new TypedValue[1] { new TypedValue((int)DxfCode.LayerName, layerName) };
            SelectionFilter sf = new SelectionFilter(tvs);
            PromptSelectionResult psr = ed.SelectAll(sf);
            if (psr.Status == PromptStatus.OK) return new ObjectIdCollection(psr.Value.GetObjectIds());
            else return new ObjectIdCollection();
        }



        public void DeleteLayerFilter()
        {


            Document doc = Application.DocumentManager.MdiActiveDocument;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction acTrans = doc.Database.TransactionManager.StartTransaction())
                {
                    Database db = doc.Database;
                    Editor ed = doc.Editor;
                    try
                    {

                        LayerFilterTree lft = db.LayerFilters;
                        LayerFilterCollection lfc = lft.Root.NestedFilters;


                        //  PromptIntegerOptions pio =
                        //  new PromptIntegerOptions(
                        //  "\n\nEnter index of filter to delete"

                        //  );

                        //pio.LowerLimit = 1;
                        //pio.UpperLimit = lfc.Count;
                        //PromptIntegerResult pir = ed.GetInteger(pio);



                        LayerFilter lf = lfc[0];
                        if (lf.AllowDelete)
                        {

                            lfc.Remove(lf);
                            db.LayerFilters = lft;

                        }

                    }

                    catch (System.Exception ex)
                    {

                        ed.WriteMessage("\nException: {0}", ex.ToString());

                    }
                }
            }

        }

    }
}
