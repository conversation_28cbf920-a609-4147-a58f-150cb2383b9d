﻿using Autodesk.AutoCAD.ApplicationServices;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zxtech.CADTaskServer;
using Zxtech.CADTaskServer.Contract;

namespace CADWSAddin
{
    class ConvertPdfTaskInfo
    {
        public string EPD_taskId { get; set; }
        public string FilePath { get; set; }
    }

    class DwgToPdfTaskProcess
    {
        private static PlotingPDF _pdfConv = new PlotingPDF();

        public static void Process(Guid taskGuid, string url, List<CADTaskCode> ls)
        {
            try
            {
                DocumentMgr.ColseAll();

                var list = new List<string>();
                foreach (var it in ls)
                {
                    var dir = it.Para1;
                    if (string.IsNullOrWhiteSpace(dir) || !Directory.Exists(dir))
                    {
                        Palettes.WriteLog(string.Format("Folds '{0}' is not exist in task '{1}'.", dir, taskGuid.ToString()));
                        continue;
                    }

                    var fs = Directory.GetFiles(dir, "*.dwg", SearchOption.TopDirectoryOnly);
                    if (fs.Count() > 0)
                    {
                        ConvertToPdfs(fs.ToList());
                        Utility.AddStringToList(list, dir);
                    }
                    else Palettes.WriteLog(string.Format("DWG file is none in path '{0}', task '{1}'.", dir, taskGuid.ToString()));
                }

                if (list.Count > 0)
                {
                    var taskServer = Palettes.taskServer;
                    var taskInfo = taskServer.formCADTaskServer.taskInfo;
                    var succ = taskServer.UpdateTaskPartPropWithModel(taskInfo.EpdTaskId, new List<CADTaskPropFromModel>(), (taskInfo != null && taskInfo.EpdTaskId.ToByteArray().Count() > 0)
                                    ? taskInfo.EpdTaskId : Guid.Empty, list[0]);
                    if(succ)
                    {
                        Palettes.WriteLog(string.Format("UpdateTaskPartPropWithModel() succ in task '{0}'.", taskGuid.ToString()));
                    }
                    else
                    {
                        Palettes.WriteLog(string.Format("UpdateTaskPartPropWithModel() failed in task '{0}'.", taskGuid.ToString()));
                    }
                }
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.ToString(), 2);
            }
        }

        private static void ConvertToPdfs(List<string> files)
        {
            foreach (var f in files)
            {
                try
                {
                    var doc = Application.DocumentManager.Open(f, true);
                    if (doc == null)
                    {
                        Palettes.WriteLog(string.Format("file '{0}' open failed.", f), 2);
                        continue;
                    }

                    ConvertPdf(doc, Path.ChangeExtension(f, ".pdf"));
                }
                catch (Exception ex)
                {
                    Palettes.WriteLog(ex.ToString(), 2);
                }
                finally
                {
                    Palettes.WriteLog(string.Format("file '{0}' -> pdf complete.", f));
                    DocumentMgr.ColseAll();
                }
            }
        }

        private static void ConvertPdf(Document doc, string pdfPath)
        {
            if (doc == null) return;

            Utility.DeleteFile(pdfPath);
            try
            {
                _pdfConv.beginPrint(pdfPath, null, null, doc);
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.ToString(), 2);
            }
        }
    }    
}
