﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;

namespace CADWSAddin
{
    public class BlockExp
    {
        private static uint _HaveEntiy;
        public static uint HaveEntiy
        {
            get { return _HaveEntiy; }
            set
            {
                _HaveEntiy = value;
            }
        }

        public void Execute(bool all)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            //all = false;
            TypedValue[] filListBlock = new TypedValue[1];
            filListBlock[0] = new TypedValue(0, "Insert");
            SelectionFilter filterBlock = new SelectionFilter(filListBlock);
            //选择对象
            PromptSelectionResult resBlock = ed.SelectAll(filterBlock);

            if (resBlock.Status == PromptStatus.OK)
            {
                //doc.SendStringToExecute("EXPLODE ", false, false, false);
                using (DocumentLock doclock = doc.LockDocument())
                {
                    using (Transaction trans = db.TransactionManager.StartTransaction())
                    {
                        BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForWrite, false) as BlockTable;
                        BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite, false) as BlockTableRecord;


                        foreach (ObjectId BlockId in resBlock.Value.GetObjectIds())
                        {
                            BlockReference BlockObject = (BlockReference)trans.GetObject(BlockId, OpenMode.ForWrite); //获取图块对象
                            if (all)
                            {
                                try
                                {
                                    //BlockTableRecord btrBlock1 = trans.GetObject(BlockObject.DynamicBlockTableRecord, OpenMode.ForWrite) as BlockTableRecord;
                                    //string bkName = btrBlock1.Name.ToLower();
                                    //btrBlock1.Explodable = true;

                                    DBObjectCollection newobj = new DBObjectCollection();
                                    BlockObject.Explode(newobj);
                                    foreach (Entity item in newobj)
                                    {
                                        btr.AppendEntity(item);
                                        trans.AddNewlyCreatedDBObject(item, true);
                                    }

                                    //BlockObject.ExplodeToOwnerSpace();
                                    BlockObject.Erase(true);
                                    //if (BlockObject.Name.Contains("*U"))
                                    //{
                                    //    BlockObject.ExplodeToOwnerSpace();
                                    //    BlockObject.Erase();
                                    //}
                                }
                                catch (Autodesk.AutoCAD.Runtime.Exception ex)
                                {
                                    if (ex.ErrorStatus == ErrorStatus.CannotScaleNonUniformly)
                                    {
                                        //"无法炸开{0}块！去掉块统一比例属性"
                                        Application.ShowAlertDialog(string.Format(LanguageHelper.GetString("Exception_CtExplodeBlockRemoveScaleAtt"), BlockObject.Name));
                                        return;
                                    }
                                    else
                                    {
                                        Application.ShowAlertDialog(ex.ErrorStatus.ToString());
                                        return;
                                    }

                                }


                            }
                            else
                            {
                                if (BlockObject.Name.ToLower() == "layout")
                                {
                                    BlockObject.ExplodeToOwnerSpace();
                                    BlockObject.Erase();
                                }

                                //如果Layout需要驱动 打开以下代码。
                                BlockTableRecord btrBlock1 = trans.GetObject(BlockObject.DynamicBlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                                string bkName = btrBlock1.Name.ToLower();

                                if (bkName == "layout")
                                {
                                    BlockObject.ExplodeToOwnerSpace();
                                    BlockObject.Erase();
                                }
                            }
                        }
                        trans.Commit();
                    }
                }
            }

            //遍历是否还有匿名块，有则炸开
            resBlock = ed.SelectAll(filterBlock);
            if (resBlock.Status == PromptStatus.OK)
            {
                if (resBlock.Value.Count > 0)
                {
                    if (all)
                    {
                        Execute(true);
                    }
                }
            }

        }



        //public void Execute(bool all)
        //{
        //    Database db = HostApplicationServices.WorkingDatabase;
        //    Document doc = Application.DocumentManager.MdiActiveDocument;
        //    Editor ed = doc.Editor;

        //    //all = false;
        //    TypedValue[] filListBlock = new TypedValue[1];
        //    filListBlock[0] = new TypedValue(0, "Insert");
        //    SelectionFilter filterBlock = new SelectionFilter(filListBlock);
        //    //选择对象
        //    PromptSelectionResult resBlock = ed.SelectAll(filterBlock);

        //    if (resBlock.Status == PromptStatus.OK)
        //    {

        //        using (DocumentLock doclock = doc.LockDocument())
        //        {
        //            using (Transaction trans = db.TransactionManager.StartTransaction())
        //            {
        //                if (resBlock.Value.Count > 0)
        //                {
        //                    ed.SetImpliedSelection(resBlock.Value.GetObjectIds());
        //                }
        //                trans.Commit();
        //            }
        //        }



        //        //if (all)
        //        //{
        //        //    doc.SendStringToExecute("x ", false, false, false);
        //        //}
        //    }
        //    else
        //    {
        //        using (DocumentLock doclock = doc.LockDocument())
        //        {
        //            using (Transaction trans = db.TransactionManager.StartTransaction())
        //            {

        //                //ObjectId blockID = ObjectId.Null;                    // 用于返回所创建的块的对象Id
        //                BlockTableRecord record = new BlockTableRecord();      //创建一个BlockTableRecord类的对象，表示所要创建的块
        //                record.Name = "Tempblock";                             //设置块名  
        //                record.Origin = new Point3d(0, 0, 0);                //设置块的基点
        //                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForWrite);
        //                if (!bt.Has(record.Name))
        //                {
        //                    bt.Add(record);
        //                    trans.AddNewlyCreatedDBObject(record, true);
        //                }
        //                else
        //                {
        //                    record = (BlockTableRecord)trans.GetObject(bt["Tempblock"], OpenMode.ForRead);
        //                }

        //                BlockReference br = new BlockReference(new Point3d(0, 0, 0), record.ObjectId);
        //                BlockTableRecord modelSpaceBtr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        //                ObjectId brId = modelSpaceBtr.AppendEntity(br);
        //                trans.AddNewlyCreatedDBObject(br, true);

        //                trans.Commit();

        //            }
        //            resBlock = ed.SelectAll(filterBlock);
        //            if (resBlock.Status == PromptStatus.OK)
        //            {
        //                ed.SetImpliedSelection(resBlock.Value.GetObjectIds());
        //            }

        //        }
        //     }

        //    ////遍历是否还有匿名块，有则炸开
        //    //resBlock = ed.SelectAll(filterBlock);
        //    //if (resBlock.Status == PromptStatus.OK)
        //    //{
        //    //    if (resBlock.Value.Count > 0)
        //    //    {
        //    //        if (all)
        //    //        {
        //    //            Execute(true);
        //    //        }
        //    //    }
        //    //}
        //}

    }
}
