﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCADExt;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Script.Serialization;


namespace CADWSAddin

{
    public class DimMgr : ExecuteContext
    {
        public DimMgr(Database db, bool? iscutLine)
            : base(db)
        {
            _iscutLine = iscutLine;
        }

        List<DimDatum> _lsDimDatum = new List<DimDatum>();
        List<DimensionXLine> _lsDimXLine = new List<DimensionXLine>();

        bool FormatXLine = true;
        private bool? _iscutLine = false;
        string HL_name = "EntityKey";
        private bool _innerCutLine = false;

        public void CreateDimLayerFrame(Extents3d bound, int layer, float baseHight, float layerHeight)
        {
            StartTransaction();
            BlockTableRecord btr = GetBlockTableRecord();



            Point2d pt1 = new Point2d(bound.MinPoint.X - baseHight, bound.MaxPoint.Y + baseHight);
            Point2d pt2 = new Point2d(bound.MaxPoint.X + baseHight, bound.MaxPoint.Y + baseHight);
            Point2d pt3 = new Point2d(bound.MaxPoint.X + baseHight, bound.MinPoint.Y - baseHight);
            Point2d pt4 = new Point2d(bound.MinPoint.X - baseHight, bound.MinPoint.Y - baseHight);

            Polyline line1 = CADCommon.CreateDashedFrame(pt1, pt2, pt4, pt3);

            btr.AppendEntity(line1);
            Transaction.AddNewlyCreatedDBObject(line1, true);
            for (int i = 1; i < layer; i++)
            {
                Point2d pt11 = new Point2d(pt1.X - i * layerHeight, pt1.Y + i * layerHeight);
                Point2d pt12 = new Point2d(pt2.X + i * layerHeight, pt2.Y + i * layerHeight);
                Point2d pt13 = new Point2d(pt3.X + i * layerHeight, pt3.Y - i * layerHeight);
                Point2d pt14 = new Point2d(pt4.X - i * layerHeight, pt4.Y - i * layerHeight);

                Polyline line = CADCommon.CreateDashedFrame(pt11, pt12, pt14, pt13);


                btr.AppendEntity(line);
                Transaction.AddNewlyCreatedDBObject(line, true);
            }

            CommitTransaction();
        }


        public void CreateRotatedDimension()
        {
            StartTransaction();
            BlockTableRecord btr = GetBlockTableRecord();

            Point3d pt1 = new Point3d(0, 0, 0);
            Point3d pt2 = new Point3d(100, 0, 0);
            Point3d pt3 = new Point3d(0, 50, 0);

            RotatedDimension dim = new RotatedDimension(Math.PI / 6, pt1, pt2, pt3, "", ObjectId.Null);

            btr.AppendEntity(dim);
            Transaction.AddNewlyCreatedDBObject(dim, true);

            CommitTransaction();

        }


        public void CreateAngleDimension()
        {
            StartTransaction();
            BlockTableRecord btr = GetBlockTableRecord();

            Point3d pt1 = new Point3d(0, 0, 0);
            Point3d pt2 = new Point3d(50, 0, 0);
            Point3d pt3 = new Point3d(30, 30, 0);



            LineAngularDimension2 dim = new LineAngularDimension2(pt1, pt2, pt1, pt3, pt3, "", ObjectId.Null);

            btr.AppendEntity(dim);
            Transaction.AddNewlyCreatedDBObject(dim, true);

            CommitTransaction();
        }

        public void CreateMLeder()
        {
            StartTransaction();
            BlockTableRecord btr = GetBlockTableRecord();

            Point3d pt1 = new Point3d(0, 0, 0);
            Point3d pt2 = new Point3d(50, 0, 0);
            Point3d pt3 = new Point3d(30, 30, 0);



            MLeader leader = new MLeader();


            //btr.AppendEntity(dim);
            //Transaction.AddNewlyCreatedDBObject(dim, true);

            CommitTransaction();
        }


        /// <summary>
        /// 获得尺寸定义基准
        /// </summary>
        public void ParseDimDatum()
        {
#if _DEBUG
            Palettes.WriteLog("ParseDimDatum() start...");
#endif
            _innerCutLine = false;
            _lsDimDatum.Clear();

            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(DB.BlockTableId, OpenMode.ForRead) as BlockTable;

                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(Polyline)))) continue;

                    Polyline pl = tran.GetObject(objId, OpenMode.ForRead) as Polyline;

                    if (pl.Hyperlinks.Count == 0) continue;
                    HyperLink link = pl.Hyperlinks[0];

                    if (link.Name.IndexOf(HL_name) >= 0)
                    {
                        Extents3d ext = pl.GeometricExtents;
                        DimDatum datum = new DimDatum() {
                            MinX = ext.MinPoint.X,
                            MinY = ext.MinPoint.Y,
                            MaxX = ext.MaxPoint.X,
                            MaxY = ext.MaxPoint.Y
                        };
                        string LineW = link.Name;

                        if (link.Name.Substring(link.Name.Length - 1, 1) == "T")
                        {
                            LineW = link.Name.Remove(link.Name.Length - 2, 2);
                            datum.FormatT = true;

                            if (!_innerCutLine) _innerCutLine = true;
                        }
                        else
                        {
                            datum.FormatT = false;
                        }

                        //EntityKey:e35701;DimLine:30,15
                        if (LineW.IndexOf("DimLine") >= 0)
                        {
                            //带有尺寸线间距的参照记录
                            string Ent_handel_temp = LineW.Split(';')[0];
                            string Dimdeep = LineW.Split(';')[1].Split(':')[1];

#if _DEBUG
                            Palettes.WriteLog("Ent_handel_temp: " + Ent_handel_temp + " Dimdeep: " + Dimdeep);
#endif

                            datum.BaseKey = Ent_handel_temp.Substring(Ent_handel_temp.IndexOf(":") + 1,
                                            Ent_handel_temp.Length - (Ent_handel_temp.IndexOf(":") + 1));

                            try
                            {
                                datum.BaseDistance = double.Parse(Dimdeep.Split(',')[0]);
                                datum.SpaceDistance = double.Parse(Dimdeep.Split(',')[1]);
                            }
                            catch (System.Exception ex)
                            {
                                Palettes.WriteLog(ex.Message, 2);
                                Palettes.WriteLog(ex.StackTrace, 2);
                            }
                        }

                        _lsDimDatum.Add(datum);
                    }
                    pl.Dispose();
                }
                bt.Dispose();
                btr.Dispose();

            }
        }


        private void ArrangeDimension()
        {
#if _DEBUG
            Palettes.WriteLog("ArrangeDimension() start...");
#endif
            _lsDimXLine.Clear();

            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(DB.BlockTableId, OpenMode.ForRead) as BlockTable;
                LayerTable lt = tran.GetObject(DB.LayerTableId, OpenMode.ForRead) as LayerTable;

                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                var hideLayers = new List<int>();
                var showLayers = new List<int>();
                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) continue;

                    RotatedDimension dim = tran.GetObject(objId, OpenMode.ForRead) as RotatedDimension;
                    LayerTableRecord ltr = tran.GetObject(dim.LayerId, OpenMode.ForRead) as LayerTableRecord;

                    var index = GetDimensionLayerIndex(dim);
                    if (ltr.IsOff || ltr.IsFrozen)
                    {
                        if (index != null) Utils.AddItemToList(hideLayers, (int)index);
                    }
                    else
                    {
                        if (index != null) Utils.AddItemToList(showLayers, (int)index);
                    }
                    dim.Dispose();
                    ltr.Dispose();
                }

                for (int i = 0; i < hideLayers.Count; ++i)
                {
                    if (showLayers.Contains(hideLayers[i])) { hideLayers.RemoveAt(i); --i; }
                }

#if _DEBUG
                if (hideLayers.Count > 0)
                {
                    string text = "HideLayers: ";
                    foreach (var it in hideLayers) text += string.Format("{0},", it);
                    Palettes.WriteLog(text.TrimEnd(','));
                }
#endif
                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) continue;

                    RotatedDimension dim = tran.GetObject(objId, OpenMode.ForWrite) as RotatedDimension;
                    LayerTableRecord ltr = (LayerTableRecord)lt[dim.Layer].GetObject(OpenMode.ForRead);

                    if (ltr.IsOff || ltr.IsFrozen) continue;

                    int layer = 0;
                    DimDatum datum = GetDimensionDatum(dim, ref layer);
                    if (datum == null) continue;

#if _DEBUG
                    Palettes.WriteLog(string.Format("Dim block name '{1}', dimexe: {0}", dim.Dimexe, dim.BlockName));
                    Palettes.WriteLog("start print layer distance...");
#endif

                    double ll = 0;
                    if (layer > 0)
                    {
                        for (int i = 1; i <= layer; ++i)
                        {
                            if (hideLayers.Contains(i))
                            {
#if _DEBUG
                                Palettes.WriteLog(string.Format("Skip hide layer: {0}", i));
#endif
                                continue;
                            }

#if _DEBUG
                            Palettes.WriteLog(string.Format("Add spaceDistance: {0}, layer: {1}", datum.SpaceDistance, i));
#endif
                            ll += (i == 1 ? datum.BaseDistance : datum.SpaceDistance);
                        }
                    }
                    else
                    {
                        for (int i = -1; i >= layer; --i)
                        {
                            if (hideLayers.Contains(i))
                            {
#if _DEBUG
                                Palettes.WriteLog(string.Format("Skip hide layer: {0}", i));
#endif
                                continue;
                            }

#if _DEBUG
                            Palettes.WriteLog(string.Format("Reduce spaceDistance: {0}, layer: {1}", datum.SpaceDistance, i));
#endif
                            ll -= (i == -1 ? datum.BaseDistance : datum.SpaceDistance);
                        }
                    }
#if _DEBUG
                    Palettes.WriteLog("end print layer distance...");
#endif

                    DimensionXLine dimxline = new DimensionXLine() {
                        DimId = objId,
                        LayerNum = layer,
                        OffsetDis = Math.Abs(ll),
                        Datum = datum
                    };

                    // 获取旧的方向字符串
                    string oldDirection = GetDimOldDireation(dim)?.ToLower();
                    if (dim.Rotation > 1.4 && dim.Rotation < 1.7)
                    {
                        // 竖直方向左右五度
                        bool isLeft = oldDirection == "left" || string.IsNullOrEmpty(oldDirection) && (dim.TextPosition.X < (datum.MaxX + datum.MinX) / 2);
                        bool isRight = oldDirection == "right" || string.IsNullOrEmpty(oldDirection) && (dim.TextPosition.X >= (datum.MaxX + datum.MinX) / 2);

                        if (isLeft)
                        {
                            // 向左对齐
                            dim.TextPosition = new Point3d(datum.MinX - ll, dim.TextPosition.Y, 0);
                            dimxline.AlignDir = Direction.Left;
                        }
                        else if (isRight)
                        {
                            // 向右对齐
                            dim.TextPosition = new Point3d(datum.MaxX + ll, dim.TextPosition.Y, 0);
                            dimxline.AlignDir = Direction.Right;
                        }
                    }
                    else
                    {

                        // 水平方向
                        bool isUp = oldDirection == "up" || string.IsNullOrEmpty(oldDirection) && (dim.TextPosition.Y < (datum.MaxY + datum.MinY) / 2);
                        bool isDown = oldDirection == "down" || string.IsNullOrEmpty(oldDirection) && (dim.TextPosition.Y >= (datum.MaxY + datum.MinY) / 2);

                        if (isUp)
                        {
                            // 向上对齐
                            dim.TextPosition = new Point3d(dim.TextPosition.X, datum.MaxY + ll, 0);
                            dimxline.AlignDir = Direction.Up;
                        }
                        else if (isDown)
                        {
                            // 向下对齐
                            dim.TextPosition = new Point3d(dim.TextPosition.X, datum.MinY - ll, 0);
                            dimxline.AlignDir = Direction.Down;
                        }

                    }
#if _DEBUG
                    Palettes.WriteLog(dimxline.ToString());
#endif
                    _lsDimXLine.Add(dimxline);
                }

                tran.Commit();
                bt.Dispose();
                btr.Dispose();
                //DeleteDimapost();
                if (FormatXLine)
                {
                    AdjustDimXline(_iscutLine ?? _innerCutLine);
                }
                //AssocPoint.DimRegen(DB.UnmanagedObject);
            }
        }

        private string GetDimOldDireation(RotatedDimension dim)
        {

            if (string.IsNullOrEmpty(dim.Dimapost)) return null;
            string[] arr = dim.Dimapost.Split(new char[] { ':' });
            if (arr.Length < 3) return null;
            return arr[2];

        }

        public void AddDimDirection()
        {
            _lsDimXLine.Clear();
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(DB.BlockTableId, OpenMode.ForRead) as BlockTable;
                LayerTable lt = tran.GetObject(DB.LayerTableId, OpenMode.ForRead) as LayerTable;

                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) continue;

                    RotatedDimension dim = tran.GetObject(objId, OpenMode.ForWrite) as RotatedDimension;
                    LayerTableRecord ltr = (LayerTableRecord)lt[dim.Layer].GetObject(OpenMode.ForRead);

                    if (ltr.IsOff || ltr.IsFrozen) continue;

                    int layer = 0;
                    DimDatum datum = GetDimensionDatum(dim, ref layer);
                    if (datum == null) continue;

                    if ((dim.Dimapost.Split(new char[] { ':' }).Length) == 2)
                    {
                        if (dim.Rotation > 1.4 && dim.Rotation < 1.7)
                        {
                            if (dim.TextPosition.X <= dim.XLine1Point.X)          //左
                            {
                                dim.Dimapost = dim.Dimapost + ":Left";
                            }
                            else if (dim.TextPosition.X > dim.XLine1Point.X)         //右
                            {
                                dim.Dimapost = dim.Dimapost + ":Right";

                            }

                        }
                        else
                        {
                            if (dim.TextPosition.Y >= dim.XLine1Point.Y)          //上
                            {
                                dim.Dimapost = dim.Dimapost + ":Up";
                            }
                            else if (dim.TextPosition.Y < dim.XLine1Point.Y)         //下
                            {
                                dim.Dimapost = dim.Dimapost + ":Down";

                            }
                        }
                    }
                }

                tran.Commit();
                bt.Dispose();
                btr.Dispose();
            }
        }
        public void DeleteDimapost()
        {
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(DB.BlockTableId, OpenMode.ForRead) as BlockTable;
                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId objId in btr)
                {
                    if (!objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) continue;

                    RotatedDimension dim = tran.GetObject(objId, OpenMode.ForWrite) as RotatedDimension;
                    if (!string.IsNullOrEmpty(dim.Dimapost))
                    {
                        dim.Dimapost = "";
                    }
                }
                tran.Commit();
                bt.Dispose();
                btr.Dispose();
            }
        }

        private void AdjustDimXline(bool iscutline)
        {
#if _DEBUG
            Palettes.WriteLog("AdjustDimXline() start...");
#endif
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                List<string> lsBase = _lsDimXLine.Select(e => e.Datum.BaseKey).Distinct().ToList();
                if (lsBase.Count == 0)
                {
#if _DEBUG
                    Palettes.WriteLog("lsBase.Count == 0");
#endif
                    return;
                }

                for (int i = 0; i < lsBase.Count; i++)
                {
                    #region 上
                    List<DimensionXLine> lsXlineUp = _lsDimXLine.Where(e => e.Datum.BaseKey == lsBase[i] && e.AlignDir == Direction.Up && e.LayerNum > 0).OrderBy(e => e.LayerNum).ToList();
                    if (lsXlineUp.Count > 0)
                    {
                        AdjustUpDimXline(lsXlineUp, iscutline, lsXlineUp[0].Datum.MaxY);
                    }

                    //List<DimensionXLine> lsXlineUp1 = _lsDimXLine.Where(e => e.AlignDir == Direction.Up && e.LayerNum < 0).OrderByDescending(e => e.LayerNum).ToList();
                    //if (lsXlineUp1.Count > 0)
                    //{
                    //    AdjustDownDimXline(lsXlineUp1, lsXlineUp1[0].Datum.MaxY);
                    //}

                    #endregion

                    #region  下
                    List<DimensionXLine> lsXlineDown = _lsDimXLine.Where(e => e.Datum.BaseKey == lsBase[i] && e.AlignDir == Direction.Down && e.LayerNum > 0).OrderBy(e => e.LayerNum).ToList();
                    if (lsXlineDown.Count > 0)
                    {
                        AdjustDownDimXline(lsXlineDown, iscutline, lsXlineDown[0].Datum.MinY);
                    }
                    //List<DimensionXLine> lsXlineDown1 = _lsDimXLine.Where(e => e.AlignDir == Direction.Down && e.LayerNum < 0).OrderByDescending(e => e.LayerNum).ToList();
                    //if (lsXlineDown1.Count > 0)
                    //{
                    //    AdjustUpDimXline(lsXlineDown1, lsXlineDown1[0].Datum.MinY);
                    //}
                    #endregion

                    #region 左
                    List<DimensionXLine> lsXlineLeft = _lsDimXLine.Where(e => e.Datum.BaseKey == lsBase[i] && e.AlignDir == Direction.Left && e.LayerNum > 0).OrderBy(e => e.LayerNum).ToList();
                    if (lsXlineLeft.Count > 0)
                    {
                        AdjustLeftDimXline(lsXlineLeft, iscutline, lsXlineLeft[0].Datum.MinX);
                    }
                    //List<DimensionXLine> lsXlineLeft1 = _lsDimXLine.Where(e => e.AlignDir == Direction.Left && e.LayerNum < 0).OrderByDescending(e => e.LayerNum).ToList();
                    //if (lsXlineLeft1.Count > 0)
                    //{
                    //    AdjustRightDimXline(lsXlineLeft1, lsXlineLeft1[0].Datum.MinX);
                    //}
                    #endregion

                    #region 右
                    List<DimensionXLine> lsXlineRight = _lsDimXLine.Where(e => e.Datum.BaseKey == lsBase[i] && e.AlignDir == Direction.Right && e.LayerNum > 0).OrderBy(e => e.LayerNum).ToList();
                    if (lsXlineRight.Count > 0)
                    {
                        AdjustRightDimXline(lsXlineRight, iscutline, lsXlineRight[0].Datum.MaxX);
                    }
                    //List<DimensionXLine> lsXlineRight1 = _lsDimXLine.Where(e => e.AlignDir == Direction.Right && e.LayerNum < 0).OrderByDescending(e => e.LayerNum).ToList();
                    //if (lsXlineRight1.Count > 0)
                    //{
                    //    AdjustLeftDimXline(lsXlineRight1, lsXlineRight1[0].Datum.MaxX);
                    //}
                    #endregion

                }
                tran.Commit();
            }
        }


        private void AdjustUpDimXline(List<DimensionXLine> lsXlineUp, bool cutLine, double baseVlaue)
        {
#if _DEBUG
            Palettes.WriteLog(string.Format("AdjustUpDimXline() start...{0}", cutLine));
#endif
            var minPin = Palettes.IniConfigData.DimMinPinLength;
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                for (int i = 0; i < lsXlineUp.Count; i++)
                {
                    RotatedDimension dim = tran.GetObject(lsXlineUp[i].DimId, OpenMode.ForWrite) as RotatedDimension;
                    if (cutLine)
                    {
                        dim.XLine1Point = lsXlineUp[i].XLine1 = new Point3d(dim.XLine1Point.X, baseVlaue, 0);
                        dim.XLine2Point = lsXlineUp[i].XLine2 = new Point3d(dim.XLine2Point.X, baseVlaue, 0);

                        if (Math.Abs(lsXlineUp[i].LayerNum) > 1)
                        {
                            List<DimensionXLine> lsXlineUp1 = lsXlineUp.Where(e => Math.Abs(e.LayerNum) < Math.Abs(lsXlineUp[i].LayerNum)).ToList();

                            List<DimensionXLine> lsXlineUp1_1 = GetInterfereDim(tran, lsXlineUp1, lsXlineUp[i].XLine1, 0);
                            if (lsXlineUp1_1.Count > 0)
                            {
                                int ix = lsXlineUp1_1.Count - 1;
                                var th = GetTextHeight(tran, lsXlineUp1_1, lsXlineUp[i].XLine1, 0);
                                Palettes.WriteLog(string.Format("Dim '{0}' point1 text height: {1}", lsXlineUp[i].Datum.BaseKey, th));

                                var newY = Math.Min(dim.TextPosition.Y, baseVlaue + lsXlineUp1_1[ix].OffsetDis + dim.Dimexe + th);
                                if (dim.TextPosition.Y - newY < minPin) { newY = dim.TextPosition.Y - minPin; Palettes.WriteLog(string.Format("DimMinPinLength: {0}", minPin)); }

                                dim.XLine1Point = new Point3d(dim.XLine1Point.X, newY, 0);
                            }

                            List<DimensionXLine> lsXlineUp1_2 = GetInterfereDim(tran, lsXlineUp1, lsXlineUp[i].XLine2, 0);
                            if (lsXlineUp1_2.Count > 0)
                            {
                                int ix = lsXlineUp1_2.Count - 1;
                                var th = GetTextHeight(tran, lsXlineUp1_2, lsXlineUp[i].XLine2, 0);
                                Palettes.WriteLog(string.Format("Dim '{0}' point2 text height: {1}", lsXlineUp[i].Datum.BaseKey, th));

                                var newY = Math.Min(dim.TextPosition.Y, baseVlaue + lsXlineUp1_2[ix].OffsetDis + dim.Dimexe + th);
                                if (dim.TextPosition.Y - newY < minPin) { newY = dim.TextPosition.Y - minPin; Palettes.WriteLog(string.Format("DimMinPinLength: {0}", minPin)); }

                                dim.XLine2Point = new Point3d(dim.XLine2Point.X, newY, 0);
                            }
                        }
                    }
                    else
                    {
                        dim.XLine1Point = lsXlineUp[i].XLine1 = new Point3d(dim.XLine1Point.X, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineUp[i].XLine2 = new Point3d(dim.XLine2Point.X, dim.XLine2Point.Y, 0);
                    }
                    dim.Dispose();

                }
                tran.Commit();
            }
#if _DEBUG
            Palettes.WriteLog("AdjustUpDimXline() end...");
#endif
        }
        private double GetTextHeight(Transaction tran, List<DimensionXLine> lsXlineUp1_2, Point3d point1, int ori = 0)
        {
            double ret = 0;

            if (lsXlineUp1_2.Count > 0)
            {
                var dim = tran.GetObject(lsXlineUp1_2[lsXlineUp1_2.Count - 1].DimId, OpenMode.ForRead) as RotatedDimension;
                if (dim != null) ret = dim.Dimtxt + dim.Dimgap;
            }

            return ret;
        }
        private void AdjustDownDimXline(List<DimensionXLine> lsXlineDown, bool cutLine, double baseValue)
        {
#if _DEBUG
            Palettes.WriteLog(string.Format("AdjustDownDimXline() start...{0}", cutLine));
#endif
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                //bool CutLine = false;
                for (int i = 0; i < lsXlineDown.Count; i++)
                {
                    RotatedDimension dim = tran.GetObject(lsXlineDown[i].DimId, OpenMode.ForWrite) as RotatedDimension;
                    if (cutLine)
                    {
                        dim.XLine1Point = lsXlineDown[i].XLine1 = new Point3d(dim.XLine1Point.X, baseValue, 0);
                        dim.XLine2Point = lsXlineDown[i].XLine2 = new Point3d(dim.XLine2Point.X, baseValue, 0);

                        if (Math.Abs(lsXlineDown[i].LayerNum) > 1)
                        {
                            List<DimensionXLine> lsXlineDown1 = lsXlineDown.Where(e => Math.Abs(e.LayerNum) < Math.Abs(lsXlineDown[i].LayerNum)).ToList();

                            List<DimensionXLine> lsXlineDown1_1 = GetInterfereDim(tran, lsXlineDown1, lsXlineDown[i].XLine1, 0);
                            if (lsXlineDown1_1.Count > 0)
                            {
                                int ix = lsXlineDown1_1.Count - 1;
                                dim.XLine1Point = new Point3d(dim.XLine1Point.X, baseValue - lsXlineDown1_1[ix].OffsetDis/* - dim.Dimexe*/, 0);
                            }

                            List<DimensionXLine> lsXlineDown1_2 = GetInterfereDim(tran, lsXlineDown1, lsXlineDown[i].XLine2, 0);
                            if (lsXlineDown1_2.Count > 0)
                            {
                                int ix = lsXlineDown1_2.Count - 1;
                                dim.XLine2Point = new Point3d(dim.XLine2Point.X, baseValue - lsXlineDown1_2[ix].OffsetDis/* - dim.Dimexe*/, 0);
                            }
                        }

                    }
                    else
                    {
                        dim.XLine1Point = lsXlineDown[i].XLine1 = new Point3d(dim.XLine1Point.X, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineDown[i].XLine2 = new Point3d(dim.XLine2Point.X, dim.XLine2Point.Y, 0);
                    }
                    dim.Dispose();


                }
                tran.Commit();
            }
#if _DEBUG
            Palettes.WriteLog("AdjustDownDimXline() end...");
#endif
        }


        private void AdjustLeftDimXline(List<DimensionXLine> lsXlineLeft, bool cutLine, double baseValue)
        {
#if _DEBUG
            Palettes.WriteLog(string.Format("AdjustLeftDimXline() start...{0}", cutLine));
#endif
            var minPin = Palettes.IniConfigData.DimMinPinLength;
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                for (int i = 0; i < lsXlineLeft.Count; i++)
                {
                    RotatedDimension dim = tran.GetObject(lsXlineLeft[i].DimId, OpenMode.ForWrite) as RotatedDimension;
                    if (cutLine)
                    {
                        dim.XLine1Point = lsXlineLeft[i].XLine1 = new Point3d(baseValue, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineLeft[i].XLine2 = new Point3d(baseValue, dim.XLine2Point.Y, 0);

                        if (Math.Abs(lsXlineLeft[i].LayerNum) > 1)
                        {
                            List<DimensionXLine> lsXlineLeft1 = lsXlineLeft.Where(e => Math.Abs(e.LayerNum) < Math.Abs(lsXlineLeft[i].LayerNum)).ToList();

                            List<DimensionXLine> lsXlineLeft1_1 = GetInterfereDim(tran, lsXlineLeft1, lsXlineLeft[i].XLine1, 1);
                            if (lsXlineLeft1_1.Count > 0)
                            {
                                int ix = lsXlineLeft1_1.Count - 1;
                                var th = GetTextHeight(tran, lsXlineLeft1_1, lsXlineLeft[i].XLine1, 1);
                                Palettes.WriteLog(string.Format("Dim '{0}' point1 text height: {1}", lsXlineLeft[i].Datum.BaseKey, th));
                                var newX = Math.Max(dim.TextPosition.X, baseValue - lsXlineLeft1_1[ix].OffsetDis - dim.Dimexe - th);
                                if (newX - dim.TextPosition.X < minPin) { newX = dim.TextPosition.X + minPin; Palettes.WriteLog(string.Format("DimMinPinLength: {0}", minPin)); }

                                dim.XLine1Point = new Point3d(newX, dim.XLine1Point.Y, 0);
                            }

                            List<DimensionXLine> lsXlineLeft1_2 = GetInterfereDim(tran, lsXlineLeft1, lsXlineLeft[i].XLine2, 1);
                            if (lsXlineLeft1_2.Count > 0)
                            {
                                int ix = lsXlineLeft1_2.Count - 1;
                                var th = GetTextHeight(tran, lsXlineLeft1_2, lsXlineLeft[i].XLine2, 1);
                                Palettes.WriteLog(string.Format("Dim '{0}' point2 text height: {1}", lsXlineLeft[i].Datum.BaseKey, th));
                                var newX = Math.Max(dim.TextPosition.X, baseValue - lsXlineLeft1_2[ix].OffsetDis - dim.Dimexe - th);
                                if (newX - dim.TextPosition.X < minPin) { newX = dim.TextPosition.X + minPin; Palettes.WriteLog(string.Format("DimMinPinLength: {0}", minPin)); }

                                dim.XLine2Point = new Point3d(newX, dim.XLine2Point.Y, 0);
                            }
                        }
                    }
                    else
                    {
                        dim.XLine1Point = lsXlineLeft[i].XLine1 = new Point3d(dim.XLine1Point.X, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineLeft[i].XLine2 = new Point3d(dim.XLine2Point.X, dim.XLine2Point.Y, 0);
                    }

                    dim.Dispose();
                }
                tran.Commit();
            }
#if _DEBUG
            Palettes.WriteLog("AdjustLeftDimXline() end...");
#endif
        }


        private void AdjustRightDimXline(List<DimensionXLine> lsXlineRight, bool cutLine, double baseValue)
        {
#if _DEBUG
            Palettes.WriteLog(string.Format("AdjustRightDimXline() start...{0}", cutLine));
#endif
            using (Transaction tran = DB.TransactionManager.StartTransaction())
            {
                for (int i = 0; i < lsXlineRight.Count; i++)
                {
                    RotatedDimension dim = tran.GetObject(lsXlineRight[i].DimId, OpenMode.ForWrite) as RotatedDimension;
                    if (cutLine)
                    {
                        dim.XLine1Point = lsXlineRight[i].XLine1 = new Point3d(baseValue, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineRight[i].XLine2 = new Point3d(baseValue, dim.XLine2Point.Y, 0);


                        if (Math.Abs(lsXlineRight[i].LayerNum) > 1)
                        {
                            List<DimensionXLine> lsXlineRight1 = lsXlineRight.Where(e => Math.Abs(e.LayerNum) < Math.Abs(lsXlineRight[i].LayerNum)).ToList();

                            List<DimensionXLine> lsXlineRight1_1 = GetInterfereDim(tran, lsXlineRight1, lsXlineRight[i].XLine1, 1);
                            if (lsXlineRight1_1.Count > 0)
                            {
                                int ix = lsXlineRight1_1.Count - 1;
                                dim.XLine1Point = new Point3d(baseValue + lsXlineRight1_1[ix].OffsetDis/* + dim.Dimexe*/, dim.XLine1Point.Y, 0);
                            }

                            List<DimensionXLine> lsXlineRight1_2 = GetInterfereDim(tran, lsXlineRight1, lsXlineRight[i].XLine2, 1);
                            if (lsXlineRight1_2.Count > 0)
                            {
                                int ix = lsXlineRight1_2.Count - 1;
                                dim.XLine2Point = new Point3d(baseValue + lsXlineRight1_2[ix].OffsetDis/* + dim.Dimexe*/, dim.XLine2Point.Y, 0);
                            }
                        }
                    }
                    else
                    {
                        dim.XLine1Point = lsXlineRight[i].XLine1 = new Point3d(dim.XLine1Point.X, dim.XLine1Point.Y, 0);
                        dim.XLine2Point = lsXlineRight[i].XLine2 = new Point3d(dim.XLine2Point.X, dim.XLine2Point.Y, 0);
                    }
                    dim.Dispose();
                }
                tran.Commit();
            }
#if _DEBUG
            Palettes.WriteLog("AdjustRightDimXline() end...");
#endif
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="lsDimXLine"></param>
        /// <param name="pt"></param>
        /// <param name="dir">0 水平相交 1 垂直相交</param>
        /// <returns></returns>
        private List<DimensionXLine> GetInterfereDim(Transaction tran, List<DimensionXLine> lsDimXLine, Point3d pt, int dir = 0)
        {
            List<DimensionXLine> ls = new List<DimensionXLine>();

            bool isFind = false;
            for (int i = 0; i < lsDimXLine.Count; i++)
            {
                isFind = false;

                if (dir == 0)
                {
                    if (pt.X > lsDimXLine[i].XLine1.X && pt.X < lsDimXLine[i].XLine2.X ||
                        pt.X > lsDimXLine[i].XLine2.X && pt.X < lsDimXLine[i].XLine1.X)
                    {
                        ls.Add(lsDimXLine[i]);
                        isFind = true;
                    }
                }
                else if (dir == 1)
                {
                    if (pt.Y > lsDimXLine[i].XLine1.Y && pt.Y < lsDimXLine[i].XLine2.Y ||
                        pt.Y > lsDimXLine[i].XLine2.Y && pt.Y < lsDimXLine[i].XLine1.Y)
                    {
                        ls.Add(lsDimXLine[i]);
                        isFind = true;
                    }
                }

                if (!isFind)
                {
                    var dim = tran.GetObject(lsDimXLine[i].DimId, OpenMode.ForRead) as RotatedDimension;
                    if (dim != null)
                    {
                        var gExt = dim.GeometricExtents;
                        if (dir == 0 && pt.X != lsDimXLine[i].XLine1.X && pt.X != lsDimXLine[i].XLine2.X//&& (pt.X > Math.Max(lsDimXLine[i].XLine1.X, lsDimXLine[i].XLine2.X) || pt.X < Math.Min(lsDimXLine[i].XLine1.X, lsDimXLine[i].XLine2.X))
                            && pt.X > gExt.MinPoint.X && pt.X < gExt.MaxPoint.X
                            || dir == 1 && pt.Y != lsDimXLine[i].XLine1.Y && pt.Y != lsDimXLine[i].XLine2.Y //&& (pt.Y > Math.Max(lsDimXLine[i].XLine1.Y, lsDimXLine[i].XLine2.Y) || pt.Y < Math.Min(lsDimXLine[i].XLine1.Y, lsDimXLine[i].XLine2.Y))
                            && pt.Y > gExt.MinPoint.Y && pt.Y < gExt.MaxPoint.Y)
                        {
                            ls.Add(lsDimXLine[i]);
                        }
                    }
                }
            }

            return ls;
        }

        private int? GetDimensionLayerIndex(RotatedDimension dim)
        {
            if (string.IsNullOrEmpty(dim.Dimapost)) return null;

            string[] arr = dim.Dimapost.Split(new char[] { ':' });
            if (arr.Length < 2) return null;

            return int.Parse(arr[1]);
        }
        private DimDatum GetDimensionDatum(RotatedDimension dim, ref int layer)
        {
            if (string.IsNullOrEmpty(dim.Dimapost)) return null;

            string[] arr = dim.Dimapost.Split(new char[] { ':' });
            if (arr.Length < 2) return null;

            arr[0] = arr[0].TrimEnd("-Lay".ToArray());

            DimDatum datum = _lsDimDatum.Where(e => e.BaseKey == arr[0]).FirstOrDefault();
            if (datum == null) return null;

            try
            {
                layer = int.Parse(arr[1]);
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
            }
            return datum;
        }

        public void DimTidy()
        {
            ParseDimDatum();
            if (_lsDimDatum.Count == 0)
            {
#if _DEBUG
                Palettes.WriteLog("DimTidy() _lsDimDatum.Count = 0");
#endif
                return;
            }

            ArrangeDimension();
        }

        /// <summary>
        /// 修改非标尺寸为指定的尺寸样式
        /// </summary>
        public void ModifyLayerofNoStandardDim(string paramJson,double CarryingCapacity)
        {
            var noStandardLayer = Palettes.stationInfo.NoStandardLayer ;
            if (string.IsNullOrEmpty(noStandardLayer)) 
            {
                Palettes.WriteLog($"No Exist NoStandardLayer  {noStandardLayer}.", 2);
                return;
            }
            
            Palettes.WriteLog($"NoStandardLayer is  {noStandardLayer}.", 0);

            
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            List<ParameterGroup> parameterGroups = serializer.Deserialize<List<ParameterGroup>>(Palettes.NoStandardDimJson);
            var parameters = parameterGroups.FirstOrDefault(o => o.CarryingCapacity == Palettes.CarryingCapacity).Parameters;

            Database db = HostApplicationServices.WorkingDatabase;
            Editor ed = Application.DocumentManager.MdiActiveDocument.Editor;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    TypedValue[] values = new TypedValue[]
               {
                    new TypedValue((int)DxfCode.Start,"*dim*")
               };

                    SelectionFilter filter = new SelectionFilter(values);
                    PromptSelectionResult psr = ed.SelectAll(filter);
                    if (psr.Status == PromptStatus.OK)
                    {
                        SelectionSet sSet = psr.Value;
                        ObjectId[] ids = sSet.GetObjectIds();
                        if (ids.Length == 0) return;

                        for (int i = 0; i < ids.Length; i++)
                        {
                            RotatedDimension rotDim = trans.GetObject(ids[i], OpenMode.ForWrite) as RotatedDimension;
                            var paraKey = string.Empty;
                            if (rotDim == null || !rotDim.Dimpost.Contains("@")) continue;
                            paraKey = rotDim.Dimpost.Split('@')[1];
                            var dimValue = Math.Round(rotDim.Measurement,4);
                            var parameter = parameters.FirstOrDefault(p => p.Name == paraKey);
                            //对比值
                            if (parameter == null)
                            {
                                Console.WriteLine($"Parameter {paraKey} not found in JSON.");
                                continue;
                            }
                            switch (parameter.GetCompareEnum)
                            {
                                case CompareStr.Equal:
                                    if (dimValue == parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.NotEqual:
                                    if (dimValue != parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.LessThan:
                                    if (dimValue < parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.GreaterThan:
                                    if (dimValue > parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.LessOrEqual:
                                    if (dimValue <= parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.GreaterOrEqual:
                                    if (dimValue >= parameter.Value)
                                    {
                                        rotDim.Layer = noStandardLayer;
                                    }
                                    break;
                            }
                            rotDim.Dimpost = "";
                        }
                    }


                    TypedValue[] textvalues = new TypedValue[]
                    {
                    new TypedValue((int)DxfCode.Start,"text")
                    };

                    SelectionFilter textFilter = new SelectionFilter(textvalues);
                    PromptSelectionResult textPsr = ed.SelectAll(textFilter);
                    if (textPsr.Status == PromptStatus.OK)
                    {
                        SelectionSet sSet = textPsr.Value;
                        ObjectId[] ids = sSet.GetObjectIds();
                        if (ids.Length == 0) return;

                        for (int i = 0; i < ids.Length; i++)
                        {
                            DBText dbtext = trans.GetObject(ids[i], OpenMode.ForWrite) as DBText;
                            if (dbtext == null || !dbtext.TextString.Contains("@")) continue;
                            var paraKey = string.Empty;
                            paraKey = dbtext.TextString.Split('@')[1];
                            var textContent = dbtext.TextString.Split('@')[0];
                            var textNumValue = Math.Round(double.Parse(textContent),4);
                            dbtext.TextString = textContent;
                            var parameter = parameters.FirstOrDefault(p => p.Name == paraKey);
                            //对比值
                            if (parameter == null)
                            {
                                Console.WriteLine($"Parameter {paraKey} not found in JSON.");
                                continue;
                            }
                            switch (parameter.GetCompareEnum)
                            {
                                case CompareStr.Equal:
                                    if (textNumValue == parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.NotEqual:
                                    if (textNumValue != parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.LessThan:
                                    if (textNumValue < parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.GreaterThan:
                                    if (textNumValue > parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.LessOrEqual:
                                    if (textNumValue <= parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                                case CompareStr.GreaterOrEqual:
                                    if (textNumValue >= parameter.Value)
                                    {
                                        dbtext.Layer = noStandardLayer;
                                    }
                                    break;
                            }
                        }
                    }
                }
                catch (System.Exception)
                {

                    throw;
                }
                finally 
                {
                    trans.Commit();
                }
               
                
            }

        }
    }


    class DimDatum
    {
        public bool FormatT { get; set; }

        public double MinX { get; set; }
        public double MaxX { get; set; }

        public double MinY { get; set; }
        public double MaxY { get; set; }

        public double BaseDistance { get; set; }
        public double SpaceDistance { get; set; }

        public string BaseKey { get; set; }

    }

    enum Direction
    {
        Up = 1,
        Down = 2,
        Left = 3,
        Right = 4,
        Up_Neg,
        Down_Neg,
        Left_Neg,
        Right_Neg
    }


    class DimensionXLine
    {
        public ObjectId DimId { get; set; }

        public Direction AlignDir { get; set; }

        public DimDatum Datum { get; set; }

        public int LayerNum { get; set; }
        public double OffsetDis { get; set; }


        public Point3d XLine1 { get; set; }
        public Point3d XLine2 { get; set; }

        public string ToString()
        {
            return string.Format("Datum.BaseKey: {4}; DimID: {0}; AlignDir: {1}; LayerNum: {2}; OffsetDis: {3};", DimId, AlignDir, LayerNum, OffsetDis, Datum.BaseKey);
        }
    }

   


  
    public enum CompareStr
    {
        Unknown,        // 未知类型
        GreaterThan,    // >
        LessThan,       // <
        NotEqual,       // <>
        Equal,          // =
        GreaterOrEqual, // >=
        LessOrEqual     // <=
    }

   
    public class ParameterSetting
    {
        public string Name { get; set; }
        public double Value { get; set; }

        public string DrawingCompareStandard { get; set; }

        public CompareStr GetCompareEnum
        {
            get
            {
                if (string.IsNullOrEmpty(DrawingCompareStandard))
                    return CompareStr.Unknown;

                switch (DrawingCompareStandard)
                {
                    case ">":
                        return CompareStr.GreaterThan;
                    case "<":
                        return CompareStr.LessThan;
                    case "<>":
                        return CompareStr.NotEqual;
                    case "=":
                        return CompareStr.Equal;
                    case ">=":
                        return CompareStr.GreaterOrEqual;
                    case "<=":
                        return CompareStr.LessOrEqual;
                    default:
                        return CompareStr.Unknown;
                }
            }
        }
    }

   
    public class ParameterGroup
    {
        public int CarryingCapacity { get; set; }  // 载重吨数
        public List<ParameterSetting> Parameters { get; set; }  // 参数列表
    }
}

