﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows.OPM;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Interop.Common;
using System;
using System.Reflection;
using System.Runtime.InteropServices;
using Autodesk.AutoCAD.DatabaseServices;


namespace CADWSAddin
{
    #region Our Custom Property
    [
        Guid("F60AE3DA-0373-4d24-82D2-B2646517ABCB"), ProgId("OPMNetMyPoint.CustomProperty.1"),

        // No class interface is generated for this class and
        // no interface is marked as the default.
        // Users are expected to expose functionality through
        // interfaces that will be explicitly exposed by the object
        // This means the object can only expose interfaces we define

        ClassInterface(ClassInterfaceType.None),
        // Set the default COM interface that will be used for
        // Automation. Languages like: C#, C++ and VB allow to 
        //query for interface's we're interested in but Automation 
        // only aware languages like javascript do not allow to 
        // query interface(s) and create only the default one

        ComDefaultInterface(typeof(IDynamicProperty2)),
        ComVisible(true)
    ]
    public class CustomPropertyOfPoint : IDynamicProperty2
    {
        private IDynamicPropertyNotify2 m_pSink = null;

        // Unique property ID
        public void GetGUID(out Guid propGUID)
        {
            propGUID = new Guid("F60AE3DA-0373-4d24-82D2-B2646517ABCB");

        }

        // Property display name
        public void GetDisplayName(out string szName)
        {
            szName = "名称";
        }

        // Show/Hide property in the OPM, for this object instance
        public void IsPropertyEnabled(object pUnk, out int bEnabled)
        {
            bEnabled = 1;
        }

        // Is property showing but disabled
        public void IsPropertyReadOnly(out int bReadonly)
        {
            bReadonly = 0;
        }

        // Get the property description string
        public void GetDescription(out string szName)
        {
            szName = "定位点名称";
        }

        // OPM will typically display these in an edit field
        // optional: meta data representing property type name,
        // ex. ACAD_ANGLE
        public void GetCurrentValueName(out string szName)
        {
            throw new System.NotImplementedException();
        }

        // What is the property type, ex. VT_R8

        public void GetCurrentValueType(out ushort varType)
        {
            varType = 8; // VT_BSTR
        }

        // Get the property value, passes the specific object
        // we need the property value for.

        public void GetCurrentValueData(object pUnk, ref object pVarData)
        {
            if (pUnk == null) return;

            AcadObject obj = pUnk as AcadObject;
            if (obj == null) return;

            Document doc = Application.DocumentManager.MdiActiveDocument;

            using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
            {
                DBPoint ptobj = trans.GetObject(new ObjectId((IntPtr)obj.ObjectID), OpenMode.ForRead) as DBPoint;
                ResultBuffer rb = ptobj.XData;

                bool bHas = false;
                if (rb != null)
                {
                    foreach (TypedValue tv in rb)
                    {
                        if (tv.TypeCode == (int)DxfCode.ExtendedDataAsciiString)
                        {
                            pVarData = tv.Value;
                            bHas = true;
                            break;
                        }
                    }

                }

                if (!bHas)
                {
                    pVarData = "";
                }


            }

        }

        // Set the property value, passes the specific object we
        // want to set the property value for

        public void SetCurrentValueData(object pUnk, object varData)
        {

            if (pUnk == null || varData == null) return;

            AcadObject obj = pUnk as AcadObject;
            if (obj == null) return;


            Document doc = Application.DocumentManager.MdiActiveDocument;
            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    DBPoint ptobj = trans.GetObject(new ObjectId((IntPtr)obj.ObjectID), OpenMode.ForWrite) as DBPoint;

                    RegAppTableRecord app = new RegAppTableRecord();
                    app.Name = "JDL";

                    SymbolTable table = (SymbolTable)trans.GetObject(doc.Database.RegAppTableId, OpenMode.ForWrite, false);
                    if (!table.Has(app.Name))
                    {
                        table.Add(app);
                        trans.AddNewlyCreatedDBObject(app, true);
                    }


                    ResultBuffer rb = new ResultBuffer();

                    rb.Add(new TypedValue((int)DxfCode.ExtendedDataRegAppName, app.Name));
                    rb.Add(new TypedValue((int)DxfCode.ExtendedDataAsciiString, varData.ToString()));


                    ptobj.XData = rb;
                    rb.Dispose();
                    trans.Commit();

                }

            }


        }


        static void AddRegAppTableRecord(string regAppName)
        {

            Document doc = Application.DocumentManager.MdiActiveDocument;

            Editor ed = doc.Editor;

            Database db = doc.Database;

            Transaction trans = doc.TransactionManager.StartTransaction();

            RegAppTable rat = (RegAppTable)trans.GetObject(db.RegAppTableId, OpenMode.ForRead, false);

            if (!rat.Has(regAppName))
            {

                rat.UpgradeOpen();

                RegAppTableRecord ratr = new RegAppTableRecord();

                ratr.Name = regAppName;

                rat.Add(ratr);

                trans.AddNewlyCreatedDBObject(ratr, true);

            }

            trans.Commit();

            trans.Dispose();

        }




        public void Connect(object pSink)
        {
            m_pSink = (IDynamicPropertyNotify2)pSink;
        }

        public void Disconnect()
        {
            m_pSink = null;
        }
    }
    #endregion

    //#region Application Entry Point
    //public class MyEntryPoint : IExtensionApplication
    //{
    //    protected internal CustomPropertyOfPoint custProp = null;

    //    public void Initialize()
    //    {
    //        Assembly.LoadFrom("asdkOPMNetExt.dll");

    //        // Add the Dynamic Property

    //        Dictionary classDict = SystemObjects.ClassDictionary;
    //        RXClass pointDesc = (RXClass)classDict.At("AcDbPoint");

    //        IPropertyManager2 pPropMan = (IPropertyManager2)xOPM.xGET_OPMPROPERTY_MANAGER(pointDesc);

    //        custProp = new CustomPropertyOfPoint();
    //        pPropMan.AddProperty((object)custProp);
    //    }

    //    public void Terminate()
    //    {
    //        // Remove the Dynamic Property

    //        Dictionary classDict = SystemObjects.ClassDictionary;
    //        RXClass pointDesc = (RXClass)classDict.At("AcDbPoint");


    //        IPropertyManager2 pPropMan = (IPropertyManager2)xOPM.xGET_OPMPROPERTY_MANAGER(pointDesc);
    //        pPropMan.RemoveProperty((object)custProp);
    //        custProp = null;

    //    }
    //}
    //#endregion
}
