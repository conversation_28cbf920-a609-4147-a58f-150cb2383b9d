﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Application = Autodesk.AutoCAD.ApplicationServices.Core.Application;

using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Geometry;


namespace CADWSAddin
{
    public class DocOper
    {
        private Document _document = null;

        public DocOper(Document doc)
        {
            _document = doc;
        }



        //设置图层可见性
        public bool SetLayerVisibility(string sLay, bool bVisible)
        {
            if (_document == null) return false;

            Document doc = Application.DocumentManager.MdiActiveDocument;

            using (DocumentLock doclock = doc.LockDocument())
            {
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    LayerTable lt;
                    lt = (LayerTable)trans.GetObject(doc.Database.LayerTableId, OpenMode.ForWrite);


                    foreach (var item in lt)
                    {
                        LayerTableRecord ltr = (LayerTableRecord)trans.GetObject(item, OpenMode.ForWrite);

                        if (ltr.Name == sLay)
                        {
                            ltr.IsOff = !bVisible;
                        }
                    }

                    trans.Commit();
                }
            }
            return true;
        }

        public BlockReference InsertBlockFromFile(string fileName, string blkName, Point3d pt)
        {
            if (_document == null) return null;

            BlockReference blkRef = null;
            using (DocumentLock doclck = _document.LockDocument())
            {
                using (Transaction tran = _document.TransactionManager.StartTransaction())
                {
                    Database blkDb = new Database(false, true);

                    blkDb.ReadDwgFile(fileName, System.IO.FileShare.Read, true, null);
                    blkDb.CloseInput(true);


                    ObjectId blkObjId = _document.Database.Insert(blkName, blkDb, false);
                    blkRef = new BlockReference(pt, blkObjId);


                    BlockTable bt = tran.GetObject(_document.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord modelBTR = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    blkObjId = modelBTR.AppendEntity(blkRef);
                    tran.AddNewlyCreatedDBObject(blkRef, true);
                }
            }

            return blkRef;
        }
    }
}
