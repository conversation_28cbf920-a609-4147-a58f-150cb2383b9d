﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;

namespace CADWSAddin
{
    public class ExecuteContext:IDisposable
    {
        Database _db;        
        Transaction _trans;

        public ExecuteContext( Database db)
        {
            _db = db;      
        }
        
        public Database DB
        {
            get { return _db; }
        }

        public Transaction Transaction { get { return _trans; } }

        public Transaction StartTransaction()
        {
            _trans = _db.TransactionManager.StartTransaction();
            return _trans;
        }


        //获得块表记录
        public BlockTableRecord GetBlockTableRecord( OpenMode openMode = OpenMode.ForWrite)
        {
            try
            {
                BlockTable bt = _trans.GetObject(_db.BlockTableId, OpenMode.ForRead) as BlockTable;
                return _trans.GetObject(bt[BlockTableRecord.ModelSpace], openMode) as BlockTableRecord;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public void CommitTransaction()
        {
            _trans.Commit();
        }

        public void Dispose()
        {
            if (_trans != null)
            {
                _trans.Dispose();
            }
        }



    }
}
