ImageRuntimeVersion: v4.0.30319
Assembly OPMNetExt, Version=1.0.*, Culture=固定语言(固定国家/地区): 
	hash=SHA1, flags=PublicKey
Assembly mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Assembly accoremgd, Version=20.0.*, Culture=固定语言(固定国家/地区): 
	hash=None, flags=None
Assembly Acdbmgd, Version=20.0.*, Culture=固定语言(固定国家/地区): 
	hash=None, flags=None
Assembly acmgd, Version=20.0.*, Culture=固定语言(固定国家/地区): 
	hash=None, flags=None
Assembly System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Assembly System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Class Autodesk.AutoCADExt.AssocPoint: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
    Void .ctor(): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
  Methods:
    Exec(): PrivateScope, Public, Static, HideBySig
    Test(): PrivateScope, Public, Static, HideBySig
    DimRegen(IntPtr): PrivateScope, Public, Static, HideBySig
    GetDimAssocs(IntPtr): PrivateScope, Public, Static, HideBySig
Interface Autodesk.AutoCAD.Windows.OPM.IPropertyManager2: AutoLayout, AnsiClass, Class, Public, ClassSemanticsMask, Abstract, BeforeFieldInit
  Methods:
    AddProperty(Object(In, HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    RemoveProperty(Object(In, HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDynamicProperty(Int32(In) IsLong, Object&(Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDynamicPropertyByName(String(In, HasFieldMarshal), Object&(Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDynamicPropertyCountEx(Int32*(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDynamicClassInfo(Object(In, HasFieldMarshal), Object&(Out, HasFieldMarshal) IsExplicitlyDereferenced, UInt32*(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
Interface Autodesk.AutoCAD.Windows.OPM.IDynamicProperty2: AutoLayout, AnsiClass, Class, Public, ClassSemanticsMask, Abstract, BeforeFieldInit
  Methods:
    GetGUID(Guid&(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDisplayName(String&(Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    IsPropertyEnabled(Object(In, HasFieldMarshal), Int32&(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    IsPropertyReadOnly(Int32&(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetDescription(String&(Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetCurrentValueName(String&(Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetCurrentValueType(UInt16&(Out)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetCurrentValueData(Object(In, HasFieldMarshal), Object&(In, Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    SetCurrentValueData(Object(In, HasFieldMarshal), Object(In, HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    Connect(Object(In, HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    Disconnect(): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
Interface Autodesk.AutoCAD.Windows.OPM.IDynamicPropertyNotify2: AutoLayout, AnsiClass, Class, Public, ClassSemanticsMask, Abstract, BeforeFieldInit
  Methods:
    OnChanged(Object(In, HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetCurrentSelectionSet(Object&(In, Out, HasFieldMarshal) IsExplicitlyDereferenced): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
Class Autodesk.AutoCAD.Windows.OPM.AcMgdOPMPropertyExtension: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
  :Autodesk.AutoCAD.Runtime.RXObject
  Methods:
    GetPropertyManager(): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask
    SetPropertyManager(Object): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask
Class Autodesk.AutoCAD.Windows.OPM.AcMgdOPMPropertyExtensionFactory: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
  :Autodesk.AutoCAD.Runtime.RXObject
  Methods:
    CreateOPMObjectProtocol(RXClass, Int32 IsLong): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask
Class Autodesk.AutoCAD.Windows.OPM.xOPM: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
  Methods:
    xGET_OPMEXTENSION_CREATE_PROTOCOL(): PrivateScope, Public, Static, HideBySig
    xGET_OPMPROPERTY_MANAGER(RXClass): PrivateScope, Public, Static, HideBySig
