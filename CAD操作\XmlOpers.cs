﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using Zxtech.CADTaskServer.Contract;
//类已废止，待后期删除
namespace CADWSAddin
{
    public class XmlOpers : CADTaskCode
    {
        public XmlOpers()
        {
        }
        public string ModelName { get; set; }
        public string BlockName { get; set; }
        private XmlOpers Parent = null;
        private List<Parameter> Parameters = new List<Parameter>();
        private List<Function> Functions = new List<Function>();
        private List<XmlOpers> ChildNodes = new List<XmlOpers>();
        public void AddParameter(Parameter p)
        {
            Parameters.Add(p);
        }
        public void AddFunction(Function f)
        {
            Functions.Add(f);
        }

        public void AddChildNode(XmlOpers child)
        {
            ChildNodes.Add(child);
            child.SetParent(this);
        }
        public void SetParent(XmlOpers p)
        {
            Parent = p;
        }
        public List<XmlOpers> ls{get;set;}

        public bool Exe()
        {
            //1遍历子件
            for (int i = 0; i < ls.Count(); i++)
            {
                ls[i].Exe();
                try
                {

                }
                catch
                {
                }
            }
            return true;
        }
        public void AddXml(List<CADTaskCode> ls)
        {
            XmlDocument doc = new XmlDocument();
            XmlDeclaration dec = doc.CreateXmlDeclaration("1.0", "gb2312", null);
            doc.AppendChild(dec);
            //创建1级根节点
             CreateNode(doc, ls);
            doc.Save(@"C:\Users\<USER>\Desktop\12\x.xml");
            //Console.Write(doc.OuterXml);
        }
        public void CreateNode(XmlDocument doc,List<CADTaskCode> ls)
        {
                //创建一个根节点（一级）
                XmlElement root = doc.CreateElement("Node");
                doc.AppendChild(root);
                //创建节点（二级）
                XmlNode ModelName = doc.CreateElement("ModelName");
                XmlNode BlockName = doc.CreateElement("BlockName");
                XmlNode Parameters = doc.CreateElement("Parameters");
                XmlNode Functions = doc.CreateElement("Functions");
                XmlNode ChildNodes = doc.CreateElement("ChildNodes");
                XmlNode PartId = doc.CreateElement("PartId");
            //创建节点（三级）
            CreateParameter(doc, Parameters);
                CreateFunction(doc, Functions);
                for (int i = 0; i< ls.Count;i++)
                {
                    if (ls[i].CadCodeId == 1200)
                    {
                        CreateChildNodes(doc, ChildNodes, ls, i + 1);
                        root.AppendChild(ModelName);
                        root.AppendChild(BlockName);
                        root.AppendChild(Parameters);
                        root.AppendChild(Functions);
                        root.AppendChild(ChildNodes);
                        root.AppendChild(PartId);
                }
                }
        }
        public int CreateChildNodes(XmlDocument doc, XmlNode ChildNodes, List<CADTaskCode> ls, int j)
        {
            XmlElement Node = doc.CreateElement("Node");
            XmlElement e1 = doc.CreateElement("ModelName");
            XmlElement e6 = doc.CreateElement("PartId");
            //e1.InnerText = "test";
            XmlElement e2 = doc.CreateElement("BlockName");
            XmlElement e3 = doc.CreateElement("Parameters");
            XmlElement e4 = doc.CreateElement("Functions");
            XmlElement e5 = doc.CreateElement("ChildNodes");

            Node.AppendChild(e1);
            Node.AppendChild(e6);
            Node.AppendChild(e2);
            Node.AppendChild(e3);
            CreateParameter(doc, e3);
            Node.AppendChild(e4);
            CreateFunction(doc, e4);
            Node.AppendChild(e5);
            if (ls[j].CadCodeId == ls[j + 1].CadCodeId && ls[j + 1].CadCodeId == 1200)//判断是否有准备状态子件
            {
                j++;
                CreateChildNodes(doc, e5, ls, j);
            }
            //else if (ls[j + 2].CadCodeId == ls[j + 3].CadCodeId && ls[j + 2].CadCodeId == 1300)//判断是否有结束状态子件
            //{
            //    j++;
            //    CreateChildNodes(doc, e5, ls, j);
            //}
            ChildNodes.AppendChild(Node);
            return j;
        }
        public void CreateParameter(XmlDocument doc,XmlNode Parameters)
        { 
            XmlElement Parameter = doc.CreateElement("Parameter");
            XmlElement e1 = doc.CreateElement("Name");
            XmlElement e2 = doc.CreateElement("Type");
            XmlElement e3 = doc.CreateElement("Value");
            Parameter.AppendChild(e1);
            Parameter.AppendChild(e2);
            Parameter.AppendChild(e3);
            Parameters.AppendChild(Parameter);
        }
        public void CreateFunction(XmlDocument doc, XmlNode Functions)
        {
            XmlElement Function = doc.CreateElement("Function");
            XmlElement e1 = doc.CreateElement("Name");
            e1.InnerText = "test";
            XmlElement e2 = doc.CreateElement("OperObject");
            XmlElement e3 = doc.CreateElement("Value");
            Function.AppendChild(e1);
            Function.AppendChild(e2);
            Function.AppendChild(e3);
            Functions.AppendChild(Function);
        }

        public XmlAttribute CreateAttribute(XmlNode node)
        {
            try
            {
                XmlDocument doc = node.OwnerDocument;
                XmlAttribute attr = null;
                //attr = doc.CreateAttribute(attributeName);
                //attr.Value = value;
                node.Attributes.SetNamedItem(attr);
                return attr;
            }
            catch (Exception err)
            {
                string desc = err.Message;
                return null;
            }
        }
    }
}

//public class XmlOpers
//{
//    public string Node;
//    public string ModelName;
//    public string BlockName;
//    public Parameter[] Parameters;
//    public Function[] Functions;
//    public ChildNode[] ChildNodes;
//}
//public class ChildNode
//{
//    public XmlOpers[] XmlOpers;
//}
//public class XmlOper
//{
//    public string Node;
//    public string ModelName;
//    public string BlockName;
//    public Parameter[] Parameters;
//    public Function[] Functions;
//    public ChildNode[] ChildNodes;
//}


