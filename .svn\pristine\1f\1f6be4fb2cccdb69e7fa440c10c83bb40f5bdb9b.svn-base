﻿namespace CADWSAddin.Tools
{
    partial class UCTreeView
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(UCTreeView));
            this.treeViewEnts = new System.Windows.Forms.TreeView();
            this.dataGridViewEnts = new System.Windows.Forms.DataGridView();
            this.imageListNode = new System.Windows.Forms.ImageList(this.components);
            this.名称 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.坐标X = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.坐标Y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.编号 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewEnts)).BeginInit();
            this.SuspendLayout();
            // 
            // treeViewEnts
            // 
            this.treeViewEnts.Dock = System.Windows.Forms.DockStyle.Top;
            this.treeViewEnts.Location = new System.Drawing.Point(0, 0);
            this.treeViewEnts.Name = "treeViewEnts";
            this.treeViewEnts.Size = new System.Drawing.Size(252, 97);
            this.treeViewEnts.TabIndex = 0;
            this.treeViewEnts.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeViewEnts_AfterSelect);
            // 
            // dataGridViewEnts
            // 
            this.dataGridViewEnts.AllowUserToAddRows = false;
            this.dataGridViewEnts.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridViewEnts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewEnts.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.名称,
            this.坐标X,
            this.坐标Y,
            this.编号});
            this.dataGridViewEnts.Dock = System.Windows.Forms.DockStyle.Top;
            this.dataGridViewEnts.EditMode = System.Windows.Forms.DataGridViewEditMode.EditOnEnter;
            this.dataGridViewEnts.Location = new System.Drawing.Point(0, 97);
            this.dataGridViewEnts.Name = "dataGridViewEnts";
            this.dataGridViewEnts.RowTemplate.Height = 23;
            this.dataGridViewEnts.Size = new System.Drawing.Size(252, 150);
            this.dataGridViewEnts.TabIndex = 1;
            this.dataGridViewEnts.CellBeginEdit += new System.Windows.Forms.DataGridViewCellCancelEventHandler(this.dataGridViewEnts_CellBeginEdit);
            this.dataGridViewEnts.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridViewEnts_CellValueChanged);
            // 
            // imageListNode
            // 
            this.imageListNode.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageListNode.ImageStream")));
            this.imageListNode.TransparentColor = System.Drawing.Color.Transparent;
            this.imageListNode.Images.SetKeyName(0, "acad.ico");
            this.imageListNode.Images.SetKeyName(1, "door.ico");
            this.imageListNode.Images.SetKeyName(2, "window.ico");
            // 
            // 名称
            // 
            this.名称.DataPropertyName = "名称";
            this.名称.HeaderText = "名称";
            this.名称.Name = "名称";
            this.名称.Width = 54;
            // 
            // 坐标X
            // 
            this.坐标X.DataPropertyName = "坐标X";
            this.坐标X.HeaderText = "坐标X";
            this.坐标X.Name = "坐标X";
            this.坐标X.ReadOnly = true;
            this.坐标X.Width = 60;
            // 
            // 坐标Y
            // 
            this.坐标Y.DataPropertyName = "坐标Y";
            this.坐标Y.HeaderText = "坐标Y";
            this.坐标Y.Name = "坐标Y";
            this.坐标Y.ReadOnly = true;
            this.坐标Y.Width = 60;
            // 
            // 编号
            // 
            this.编号.DataPropertyName = "编号";
            this.编号.HeaderText = "编号";
            this.编号.Name = "编号";
            this.编号.ReadOnly = true;
            this.编号.Width = 54;
            // 
            // UCTreeView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.dataGridViewEnts);
            this.Controls.Add(this.treeViewEnts);
            this.Name = "UCTreeView";
            this.Size = new System.Drawing.Size(252, 270);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewEnts)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.TreeView treeViewEnts;
        internal System.Windows.Forms.DataGridView dataGridViewEnts;
        private System.Windows.Forms.ImageList imageListNode;
        private System.Windows.Forms.DataGridViewTextBoxColumn 名称;
        private System.Windows.Forms.DataGridViewTextBoxColumn 坐标X;
        private System.Windows.Forms.DataGridViewTextBoxColumn 坐标Y;
        private System.Windows.Forms.DataGridViewTextBoxColumn 编号;

    }
}
