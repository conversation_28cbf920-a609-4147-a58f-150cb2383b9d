﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Geometry;

namespace CADWSAddin
{
    public class CADCommon
    {
        //2015/7/10根据路径获取数据库
        static public Database GetDatabaseFromFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return null;
            Database db = new Database(true, true);
            db.ReadDwgFile(fileName, FileOpenMode.OpenTryForReadShare, true, null);
            return db;
        }
        static public BlockReference GetBlcokFromFile(string fileName, string blkName)
        {
            if (string.IsNullOrEmpty(fileName)) return null;

            Database db = new Database(true, true);
            //db.ReadDwgFile(fileName, System.IO.FileShare.Read, true, null);//修改原因：不能共享读取文件。
            db.ReadDwgFile(fileName,FileOpenMode.OpenTryForReadShare, true, null);
            db.CloseInput(true);

            //如果不指定块，返回整个数据库
            if (string.IsNullOrEmpty(blkName)) return null;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = trans.GetObject(db.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    //DBObject ent = trans.GetObject(blcokId, OpenMode.ForRead) as Entity;
                    if (string.Compare(blcokId.ObjectClass.DxfName, "INSERT", true) == 0)//块
                    {
                        BlockReference childBlock = blcokId.GetObject(OpenMode.ForRead) as BlockReference;
                        if (childBlock.IsDynamicBlock)
                        {
                            //获取动态块所属的动态块表记录
                            ObjectId idDyn = childBlock.DynamicBlockTableRecord;
                            //打开动态块块表记录
                            BlockTableRecord btr = (BlockTableRecord)idDyn.GetObject(OpenMode.ForRead);
                            if (blkName == btr.Name)//修改原因：动态块块名需要通过块表获取；
                            {
                                return childBlock;
                            }
                        }
                   
                    else
                        if (childBlock.Name == blkName)
                        {
                            return childBlock;
                        }
                    }
                }

            }

            return null;
        }




        static public List<BlockReference> GetDatabaseBlockRef(Database db)
        {
            List<BlockReference> lsBlk = new List<BlockReference>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTableRecord btrBlock = trans.GetObject(db.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId blcokId in btrBlock)
                {
                    DBObject ent = trans.GetObject(blcokId, OpenMode.ForRead) as Entity;
                    if (string.Compare(blcokId.ObjectClass.DxfName, "INSERT", true) == 0)           //块
                    {
                        BlockReference childBlock = trans.GetObject(blcokId, OpenMode.ForRead) as BlockReference;
                        lsBlk.Add(childBlock);

                    }
                }
                trans.Commit();
            }
            return lsBlk;
        }

        public static CADNamingPoint GetNamingPoint(DBPoint pt)
        {
            ResultBuffer rb = pt.XData;
            if (rb == null) return null;

            CADNamingPoint namingPoint = null;

            foreach (TypedValue tv in rb)
            {
                if (tv.TypeCode == (int)DxfCode.ExtendedDataAsciiString)
                {
                    if (tv.Value != null && !string.IsNullOrEmpty(tv.Value.ToString()))
                    {
                        namingPoint = new CADNamingPoint();
                        namingPoint.Point = pt.Position;
                        namingPoint.Name = tv.Value.ToString();
                        namingPoint.CADObjectId = pt.ObjectId;

                        break;
                    }
                }
            }

            rb.Dispose();
            return namingPoint;
        }
        public static Polyline CreateDashedFrame(Point2d upperLeft, Point2d upperRight, Point2d lowerLeft,
           Point2d lowerRight)
        {
            Polyline pl = new Polyline(4);
            pl.Closed = true;
            pl.AddVertexAt(0, upperLeft, 0, 0, 0);
            pl.AddVertexAt(1, upperRight, 0, 0, 0);
            pl.AddVertexAt(2, lowerRight, 0, 0, 0);
            pl.AddVertexAt(3, lowerLeft, 0, 0, 0);

            return pl;

        }

    }
}
