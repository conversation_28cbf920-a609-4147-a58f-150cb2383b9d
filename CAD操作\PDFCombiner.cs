﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;

using iTextSharp.text;
using iTextSharp.text.pdf;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Autodesk.AutoCAD.ApplicationServices;
using Document = iTextSharp.text.Document;


namespace CADWSAddin
{
    public class PDFCombiner
    {
        /// <summary>
        /// 合并PDF文件
        /// </summary>
        /// <param name="lsFile">输入文件</param>
        /// <param name="outfile">输出文件</param>
        public void Combine(List<string> lsFile, string outfile)
        {
            try
            {
              
                PdfReader reader;
                Document document = new Document();
                PdfWriter writer = PdfWriter.GetInstance(document, new FileStream(outfile, FileMode.Create));

                document.Open();
                PdfContentByte cb = writer.DirectContent;
                PdfImportedPage newPage;
                for (int i = 0; i < lsFile.Count; i++)
                {
                    reader = new PdfReader(lsFile[i]);
                    int iPageNum = reader.NumberOfPages;
                    for (int j = 1; j <= iPageNum; j++)
                    {
                        int angle = reader.GetPageRotation(j);
                        Rectangle size = reader.GetPageSize(j);

                        if (angle == 90 || angle == 270)
                        {
                            document.SetPageSize(new Rectangle(size.Height, size.Width));
                        }
                        else
                        {
                            document.SetPageSize(size);
                        }

                        document.NewPage();
                        newPage = writer.GetImportedPage(reader, j);

                        switch (angle)
                        {
                            case 90:
                                newPage.SetMatrix(0, -1, 1, 0, 0, size.Width);
                                break;
                            case 180:
                                newPage.SetMatrix(-1, 0, 0, -1, size.Width, size.Height);
                                break;
                            case 270:
                                newPage.SetMatrix(0, 1, -1, 0, size.Height, 0);
                                break;

                        }

                        cb.AddTemplate(newPage, 0, 0);
                    }
                }
                document.Close();
            }
            catch (Exception ex)
            {   //"合成文件失败！\r\n"            
                Palettes.WriteLog(string .Format(LanguageHelper.GetString("SynthesizeFileFailed") + "\r\n"+ ex.ToString()),2);
                
                throw ex;
            }
        }
        /// <summary>
        /// 合成pdf文件
        /// </summary>
        /// <param name="fileList">文件名list</param>
        /// <param name="outMergeFile">输出路径</param>
        public void MergePdfFiles(List<string> fileList, string outMergeFile)
        {
            foreach (string file in fileList)
            {
                if (IsFileLocked(file))
                {
                    throw new Exception($"文件 {file} 被占用，无法合并！");
                }
            }
            iTextSharp.text.Document document = new iTextSharp.text.Document();
            PdfReader reader=null;
            PdfWriter writer=null;
            try
            {
			    if (fileList == null)
                    return;
                
               // Rectangle rec = new Rectangle(1660, 1000);
                
                using (FileStream fs = new FileStream(outMergeFile, FileMode.Create))
                {
                    writer = PdfWriter.GetInstance(document, fs); 
                    document.Open();
                    PdfContentByte cb = writer.DirectContent;
                    PdfImportedPage newPage;
                    foreach (var file in fileList)
                    {
                        Palettes.WriteLog(string.Format("Merge pdf file：'{0}'", file));
                        using (FileStream subfs = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                        {
                            reader = new PdfReader(subfs);

                            int iPageNum = reader.NumberOfPages;
                            for (int j = 1; j <= iPageNum; j++)
                            {
                                int angle = reader.GetPageRotation(j);
                                Rectangle size = reader.GetPageSize(j);

                                if (angle == 90 || angle == 270)
                                {
                                    document.SetPageSize(new Rectangle(size.Height, size.Width));
                                }
                                else
                                {
                                    document.SetPageSize(size);
                                }

                                document.NewPage();
                                newPage = writer.GetImportedPage(reader, j);
                               
                                switch (angle)
                                {
                                    case 90:
                                        newPage.SetMatrix(0, -1, 1, 0, 0, size.Width);
                                        break;
                                    case 180:
                                        newPage.SetMatrix(-1, 0, 0, -1, size.Width, size.Height);
                                        break;
                                    case 270:
                                        newPage.SetMatrix(0, 1, -1, 0, size.Height, 0);
                                        break;
                                }
                                cb.AddTemplate(newPage, 0, 0);
                            }
                            reader.Close();
                            reader = null;
                        }
                    }
                    document.Close();
                    document = null;


                }
            }
            catch (Exception ex)
            {
                //"合成文件失败!"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("SynthesizeFileFailed")+"\r\n" + ex.ToString()), 2);
                throw ex;
            }
            finally {

                writer.Close();
                if (document!=null)
                {
                    document.Close();
                }
                if (reader != null)
                {
                    reader.Close();
                }

            }
            
        }

        private bool IsFileLocked(string filePath)
        {
            try
            {
                using (FileStream fs = File.Open(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None))
                {
                    return false;
                }
            }
            catch (IOException)
            {
                return true;
            }
        }
    }
}
