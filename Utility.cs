﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Runtime.Serialization;
using System.Text;
using Autodesk.AutoCAD.Geometry;

namespace CADWSAddin
{
    public class Utility
    {
        public const string BendTableFile = "BA_BT@";
        public const string BendDeduction = "BA_BD@";
        public const string BendAllowance = "BA_BA@";
        public const string KFactor = "BA_K@";
        public const string BendPreSuffix = "BA_";

        public const string CAD2000 = "2000";
        public const string CAD2004 = "2004";
        public const string CAD2007 = "2007";
        public const string CAD2010 = "2010";
        public const string CAD2013 = "2013";
        public const string CAD2014 = "2014";
        public const string CAD2018 = "2018";


        static public System.Text.StringBuilder ModelErrorList { get; set; }
        //private static string _bendAllowanceDefaultPath;
        public static string BendAllowanceDefaultPath { get; set; }

        public static string GetCurrentAssemblyFoldsPath()
        {
            return System.IO.Path.GetDirectoryName(typeof(Component).Assembly.CodeBase.Replace("file:///", ""));
        }

        public static void DeleteFile(string file)
        {
            try
            {
                if(File.Exists(file)) File.Delete(file);
            }
            catch (Exception ex1)
            {
                Palettes.WriteLog(ex1.ToString());
            }
        }

        static public string ExtractFormatPara(string text, char c1 = '{', char c2 = '}')
        {
            string para = "";
            bool leftMatch = false;
            int start = 0, end = 0;

            for (int i = 0; i < text.Length; i++)
            {
                if (text[i] == c1 && !leftMatch)
                {
                    if ((i + 1) < text.Length && text[i + 1] != '\\')
                    {
                        start = i;
                        leftMatch = true;
                    }
                }
                if (i > start && text[i] == c2 && leftMatch)
                {
                    end = i;
                    para = text.Substring(start + 1, end - start - 1);
                    break;
                }
            }
            return para;
        }

        public static Vector3d ConvertPoint3dToVector3d(Point3d pos)
        {
            return new Vector3d(pos.X, pos.Y, pos.Z);
        }

        public static double ConvertToDouble(string text)
        {
            double v = 0;
            if (double.TryParse(text, out v)) return v;

            return 0;
        }

        public static double AngleToRadian(double angle)
        {
            return Math.PI * angle / 180;
        }

        public static double RadianToAngle(double radian)
        {
            return radian * 180 / Math.PI;
        }

        public static void AddStringToList(List<string> list, string text)
        {
            if(null == list.FirstOrDefault(p => p.ToLower() == text.ToLower())) list.Add(text);
        }
    }
    /// <summary>
    /// 序列化辅助类
    /// </summary>
    /// <typeparam name="TPacketType"></typeparam>
    public class DataContractSerializeHelper<TPacketType> where TPacketType : class
    {
        /// <summary>
        /// 序列化WCF数据对象到流，流没有关闭，请自行关闭
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static Stream ToStream(TPacketType obj)
        {
            var dcs = new DataContractSerializer(typeof(TPacketType));
            var stream = new MemoryStream();
            dcs.WriteObject(stream, obj);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        /// <summary>
        /// 反序列化流到WCF数据对象，流在内部自动关闭
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        public static TPacketType DeserializeFromStream(Stream stream)
        {
            var dcs = new DataContractSerializer(typeof(TPacketType));

            var listSeries = (TPacketType)dcs.ReadObject(stream);
            stream.Close();
            return listSeries;
        }
        public static byte[] Pack(TPacketType obj)
        {
            if (obj == null)
            {
                return null;
            }
            var _memory = new MemoryStream();

            var ser = new DataContractSerializer(typeof(TPacketType));

            ser.WriteObject(_memory, obj);//将所有对象数据（开始 XML 元素、内容和结束元素）写入 XML 文档或流。
            _memory.Position = 0;
            var read = new byte[_memory.Length];
            _memory.Read(read, 0, read.Length);
            _memory.Close();
            return read;

        }

        public static TPacketType UnPack(byte[] buf)
        {
            var _newOjb = default(TPacketType);
            if (buf == null)
            {
                return _newOjb;
            }
            using (var _memory = new MemoryStream(buf))
            {
                _memory.Position = 0;
                var ser = new DataContractSerializer(typeof(TPacketType));
                try
                {
                    _newOjb = (TPacketType)ser.ReadObject(_memory);//读取一个 XML 文档或文档流，并返回反序列化的对象
                }
                catch
                {
                }
            }
            return _newOjb;
        }

        public static void PackToFile(string filePath, TPacketType obj)
        {
            var b = Pack(obj);
            File.WriteAllBytes(filePath, b);
        }

        public static TPacketType UnPackFromFile(string filePath)
        {
            var b = File.ReadAllBytes(filePath);
            return UnPack(b);
        }
        public static string PackToXML(TPacketType obj)
        {
            try
            {
                var bytes = Pack(obj);
                string xml = Encoding.UTF8.GetString(bytes);
                return xml;
            }
            catch
            {
                return string.Empty;
            }
        }
        public static TPacketType UnPackFromXML(string xml)
        {
            try
            {
                var bytes = Encoding.UTF8.GetBytes(xml);
                return UnPack(bytes);
            }
            catch
            {
                return null;
            }

        }
        public static TPacketType Clone(TPacketType obj)
        {
            return UnPack(Pack(obj));
        }

    }

    class WebUtils
    {
        public static bool Post(string url, object param)
        {
            bool ret = false;
            try
            {
                var wr = System.Net.WebRequest.Create(url) as System.Net.HttpWebRequest;
                wr.Method = "POST";
                wr.ContentType = "application/json";
                wr.Timeout = 6000 * 40;

                if (param != null)
                {
                    using (var sw = new StreamWriter(wr.GetRequestStream()))
                    {
                        sw.Write(ConvertJsonString(param));
                    }
                }

                string result = string.Empty;
                using (var res = wr.GetResponse() as System.Net.HttpWebResponse)
                {
                    ret = IsSuccedOfResultBool(res, out result);

                    if (ret) Palettes.WriteLog(string.Format("Callback url '{0}' succ.", url));
                    else Palettes.WriteLog(string.Format("Callback url '{0}' failed, result value: '{1}'.", url, result));
                }
            }
            catch (Exception ex)
            {
                Palettes.WriteLog(ex.ToString());
            }

            return ret;
        }

        private static bool IsSuccedOfResultBool(System.Net.HttpWebResponse res, out string result)
        {
            var contentCode = res.ContentEncoding;
            if (string.IsNullOrWhiteSpace(contentCode)) contentCode = "UTF-8";

            using (var resStream = res.GetResponseStream())
            {
                var sr = new StreamReader(resStream, Encoding.GetEncoding(contentCode));
                result = sr.ReadToEnd();

                return result.Replace("\\", "").Trim('"').ToLower() == "true";
            }
        }

        public static string ConvertJsonString(object obj)
        {
            var serialize = new System.Runtime.Serialization.DataContractSerializer(obj.GetType());
            using (var memStream = new MemoryStream())
            {
                serialize.WriteObject(memStream, obj);

                //var btArr = new byte[memStream.Length];
                //memStream.Read(btArr, 0, (int)memStream.Length);
                return Encoding.UTF8.GetString(memStream.ToArray());
            }
        }
    }
}
