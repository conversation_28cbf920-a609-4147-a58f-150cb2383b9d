﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.PlottingServices;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADWSAddin
{
    public class PlotingPDF
    {
        public PlotRotation rotAng = PlotRotation.Degrees000;
        public string devName = "DWG To PDF.pc3";
        public string devPaper = "ISO_full_bleed_A3_(420.00_x_297.00_MM)";

        //开始打印
        public void beginPrint(string fileName, string devpapertype, string scale, Document doc = null)
        {
            try
            {
                for (; ; )
                {
                    if (PlotFactory.ProcessPlotState == ProcessPlotState.NotPlotting)
                    {
                        Thread.Sleep(500);
                        break;
                    }
                }
                if (fileName == null)
                {
                    return;
                }
                if (devpapertype != null && devpapertype != "")
                {
                    if (devpapertype.ToUpper() == "A0")
                    {
                        devPaper = "ISO_full_bleed_A0_(841.00_x_1189.00_MM)";
                    }
                    else if (devpapertype.ToUpper() == "A1")
                    {
                        devPaper = "ISO_full_bleed_A1_(841.00_x_594.00_MM)";
                    }
                    else if (devpapertype.ToUpper() == "A2")
                    {
                        devPaper = "ISO_full_bleed_A2_(594.00_x_420.00_MM)";
                    }
                    else if (devpapertype.ToUpper() == "A3")
                    {
                        devPaper = "ISO_full_bleed_A3_(420.00_x_297.00_MM)";
                    }
                    else if (devpapertype.ToUpper() == "A4")
                    {
                        devPaper = "ISO_full_bleed_A4_(297.00_x_210.00_MM)";
                    }
                }
                Database db = null;
                if (doc == null) doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null) return;

                Application.SetSystemVariable("BACKGROUNDPLOT", 0);            //否则打印速度会很慢

                Editor ed = doc.Editor;
                ed.Regen();
                db = doc.Database;

                #region 事务范围
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    // We'll be plotting the current layout
                    #region 打印设置
                    BlockTableRecord btr = (BlockTableRecord)tr.GetObject(db.CurrentSpaceId, OpenMode.ForRead);
                    Layout lo = (Layout)tr.GetObject(btr.LayoutId, OpenMode.ForRead);

                    // We need a PlotInfo object
                    // linked to the layout

                    PlotInfo pi = new PlotInfo();
                    pi.Layout = btr.LayoutId;

                    // We need a PlotSettings object based on the layout settings which we then customize

                    PlotSettings ps = new PlotSettings(lo.ModelType);
                    ps.CopyFrom(lo);
                    ps.SetShadePlot(PlotSettingsShadePlotType.Wireframe, btr.Id);  //by zld
                                                                                   // The PlotSettingsValidator helps
                                                                                   // create a valid PlotSettings object

                    PlotSettingsValidator psv = PlotSettingsValidator.Current;
                    psv.SetPlotConfigurationName(ps, devName, devPaper);
                    try
                    {
                        // 确保名称完全匹配（包括扩展名）
                        string styleSheetName = "monochrome.ctb";

                        // 检查样式表是否存在
                        string[] availableCTBs = psv.GetPlotStyleSheetList().Cast<string>().ToArray();
                        if (!availableCTBs.Contains(styleSheetName, StringComparer.OrdinalIgnoreCase))
                        {
                            Palettes.WriteLog($"错误：打印样式表 {styleSheetName} 不存在！", 2);
                            return;
                        }

                        psv.SetCurrentStyleSheet(ps, styleSheetName);
                    }
                    catch (System.Exception ex)
                    {
                        Palettes.WriteLog($"设置打印样式表失败: {ex.Message}", 2);
                        return;
                    }
                    psv.SetPlotPaperUnits(ps, PlotPaperUnit.Millimeters);
                    // We'll plot the extents, centered and 
                    // scaled to fit  zld   add plot setting 
                    double minx = ps.PlotWindowArea.MinPoint.X;
                    double miny = ps.PlotWindowArea.MinPoint.Y;//
                    double maxx = ps.PlotWindowArea.MaxPoint.X;
                    double maxy = ps.PlotWindowArea.MaxPoint.Y;
                    double xX = Math.Abs(maxx - minx);
                    double yY = Math.Abs(maxy - miny);
                    if (yY > xX)
                    {
                        psv.SetPlotRotation(ps, PlotRotation.Degrees090);
                    }
                    else
                    {
                        psv.SetPlotRotation(ps, rotAng);
                    }
                    psv.SetPlotType(ps, Autodesk.AutoCAD.DatabaseServices.PlotType.Extents);

                    if (scale != null && scale != "")
                    {
                        try
                        {
                            CustomScale Scale = new CustomScale(double.Parse(scale.Split(':')[0]), double.Parse(scale.Split(':')[1]));
                            psv.SetCustomPrintScale(ps, Scale);
                        }
                        catch (System.Exception ex)
                        {
                            Palettes.WriteLog(ex.Message, 2);
                            Palettes.WriteLog(ex.StackTrace, 2);
                        }
                    }
                    else
                    {
                        // CustomScale userScale = ps.CustomPrintScale;
                        psv.SetUseStandardScale(ps, true);
                        psv.SetStdScaleType(ps, StdScaleType.ScaleToFit);
                    }
                    psv.SetPlotCentered(ps, true);
                    if (devpapertype != null && devpapertype != "")
                    {
                        if (devpapertype.ToUpper() == "A0")
                        {
                            psv.SetPlotRotation(ps, PlotRotation.Degrees090);
                        }
                    }
                    // We need to link the PlotInfo to the PlotSettings and then validate it
                    pi.OverrideSettings = ps;
                    PlotInfoValidator piv = new PlotInfoValidator();
                    piv.MediaMatchingPolicy = MatchingPolicy.MatchEnabled;
                    piv.Validate(pi);

                    #endregion

                    // A PlotEngine does the actual plotting(can also create one for Preview)

                    using (PlotEngine pe = PlotFactory.CreatePublishEngine())
                    {
                        // Create a Progress Dialog to provide info and allow thej user to cancel 
                        #region Create a Progress Dialog
                        using (PlotProgressDialog ppd = new PlotProgressDialog(false, 1, true))
                        {
                            //正在打印
                            ppd.set_PlotMsgString(PlotMessageIndex.DialogTitle, LanguageHelper.GetString("Msg_PrintNow"));
                            //取消当前打印
                            ppd.set_PlotMsgString(PlotMessageIndex.CancelJobButtonMessage, LanguageHelper.GetString("Msg_CancelCurrentPrinting"));
                            //取消本页打印
                            ppd.set_PlotMsgString(PlotMessageIndex.CancelSheetButtonMessage, LanguageHelper.GetString("Msg_CancelPrintingThisPage"));
                            //打印集合进度
                            ppd.set_PlotMsgString(PlotMessageIndex.SheetSetProgressCaption, LanguageHelper.GetString("Msg_PrintCollectionProgress"));
                            //打印页进度
                            ppd.set_PlotMsgString(PlotMessageIndex.SheetProgressCaption, LanguageHelper.GetString("Msg_PrintPageProgress"));

                            ppd.LowerPlotProgressRange = 0;
                            ppd.UpperPlotProgressRange = 100;
                            ppd.PlotProgressPos = 0;

                            // Let's start the plot, at last

                            ppd.OnBeginPlot();
                            ppd.IsVisible = false;
                            pe.BeginPlot(ppd, null);

                            // We'll be plotting a single document

                            pe.BeginDocument(pi, doc.Name, null, 1, true, fileName);

                            // Which contains a single sheet
                            ppd.OnBeginSheet();

                            ppd.LowerSheetProgressRange = 0;
                            ppd.UpperSheetProgressRange = 100;
                            ppd.SheetProgressPos = 0;

                            PlotPageInfo ppi = new PlotPageInfo();
                            pe.BeginPage(ppi, pi, true, null);
                            pe.BeginGenerateGraphics(null);
                            pe.EndGenerateGraphics(null);

                            // Finish the sheet
                            pe.EndPage(null);
                            ppd.SheetProgressPos = 100;
                            ppd.OnEndSheet();

                            // Finish the document

                            pe.EndDocument(null);

                            // And finish the plot

                            ppd.PlotProgressPos = 100;
                            ppd.OnEndPlot();
                            pe.EndPlot(null);
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                        }
                        #endregion
                    }

                    tr.Commit();
                    ps.Dispose();
                    tr.Dispose(); 
                }
                Thread.Sleep(1000);
                //for (; ; )
                //{
                //    if (PlotFactory.ProcessPlotState == ProcessPlotState.NotPlotting)
                //    {
                //        return;
                //    }
                //    else
                //    {
                //        Palettes.WriteLog(string.Format("Waiting for print complete..."));
                //        Thread.Sleep(2000);
                //    }
                //}

                int timeout = 30; // 最大等待30秒
                while (timeout-- > 0)
                {
                    if (PlotFactory.ProcessPlotState == ProcessPlotState.NotPlotting )
                    {
                        Palettes.WriteLog(string.Format("lotFactory.ProcessPlotState == ProcessPlotState.NotPlotting"));
                        if (!IsFileLocked(fileName))
                        {
                            Palettes.WriteLog(string.Format("!IsFileLocked({0})", fileName));
                        }
                        
                        return;
                    }
                    
                    Palettes.WriteLog(string.Format( "Waiting for print{0} complete", fileName));
                    Thread.Sleep(1000);
                }
                Palettes.WriteLog(string.Format("Error：The file{0} may not have been fully released!", fileName),2);
                #endregion
            }
            catch (Exception e)
            {
                Palettes.WriteLog(string.Format("Print pdf file failed. {0}", e.ToString()));
            }
        }

        private bool IsFileLocked(string filePath)
        {
            try
            {
                using (FileStream fs = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
                {
                    return false;
                }
            }
            catch (IOException ex)
            {
                Palettes.WriteLog($"文件占用检测失败: {ex.HResult}",2);
                return true;
            }
        }

    }
}
