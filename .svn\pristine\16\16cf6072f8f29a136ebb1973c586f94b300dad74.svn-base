﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;

namespace CADWSAddin
{
    public class CADNamingPoint
    {
        public string Name { get; set; }
        public Point3d Point { get; set; }

        public ObjectId CADObjectId { get; set; }

        public bool IsInserted { get; set; }


        ObjectId brfIf = ObjectId.Null;
        //块中的命名点
        public ObjectId BlockReferenceId
        {
            get { return brfIf; }
            set { brfIf = value; }
        }
    }
}
