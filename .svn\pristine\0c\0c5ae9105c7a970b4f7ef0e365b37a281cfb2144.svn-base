﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;
using CADWSAddin.Tools;
using System.IO;
using Autodesk.AutoCAD.Runtime;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Exception = System.Exception;

namespace CADWSAddin
{
    public class DwgInfo
    {
        public string DwgFile{get;private set;}
        public string BlockName { get; private set; }
        public string DrawingNO { get; private set; }
        Document AssemDoucument = null;
        private Database _database;
        public DwgInfo(string dwgFileName)
        {
           // DrawingNO = System.IO.Path.GetFileNameWithoutExtension(dwgFileName);
            var fileName = FileName.Paser(dwgFileName);
            if (fileName.IsPaserSucess)
            {

                this.DwgFile = fileName.ModelName;
                this.BlockName = fileName.BlockName;
            }
            else
            {
                this.DwgFile = dwgFileName;
            }
        }
        public List<string> GetALLZXVariable()
        {

            /*下载暂时关闭  var fileByte=  Component.GetDrawing(DrawingNO);
             if (fileByte == null)
             {
                 Palettes.WriteLog(string.Format("没找到文件 DrawingNO {0}", DrawingNO), 2);
                 return new List<string>();
             }
             var plugInpath = System.IO.Path.GetDirectoryName(typeof(Component).Assembly.Location);
             var tempPath= System.IO.Path.Combine(plugInpath, "Temp");
             if (!System.IO.Directory.Exists(tempPath))
             {
                 System.IO.Directory.CreateDirectory(tempPath);
             }
             DwgFile = Path.Combine(tempPath, DrawingNO + ".dwg");

             File.WriteAllBytes(DwgFile, fileByte);*/



            List<string> result = new List<string>();
            Database db = new Database(false, true);
            //db.ReadDwgFile(fileName, System.IO.FileShare.Read, true, null);//修改原因：不能共享读取文件。
            db.ReadDwgFile(this.DwgFile, FileOpenMode.OpenTryForReadShare, true, null);
            db.CloseInput(true);

            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                foreach (var itemobject in bt)
                {
                    BlockTableRecord btr = trans.GetObject(itemobject, OpenMode.ForRead) as BlockTableRecord;
                    //this.richTextBox1.AppendText(btr.Name + "  块名" + "\r\n");
                    foreach (ObjectId blockId in btr)
                    {
                        if (string.Compare(blockId.ObjectClass.DxfName, "INSERT", true) != 0) continue;

                        BlockReference childBlock = blockId.GetObject(OpenMode.ForRead) as BlockReference;

                        if (!childBlock.IsDynamicBlock) continue;
                        if (!string.IsNullOrEmpty(this.BlockName)&&childBlock.GetBlockName() != this.BlockName) continue;

                        BlockTableRecord btrBlock1 = childBlock.DynamicBlockTableRecord.GetObject(OpenMode.ForRead) as BlockTableRecord;

                        for (int i = 0; i < childBlock.DynamicBlockReferencePropertyCollection.Count; i++)
                        {
                            string name = childBlock.DynamicBlockReferencePropertyCollection[i].PropertyName;

                            if (name.StartsWith("ZX", true, null))
                            {
                                if (!result.Contains(name))
                                {
                                    result.Add(name);
                                }
                            }
                        }
                    }

                    // trans.Commit();
                }
            }
            db.Dispose();
            return result;
        }
        private bool OpenModel(string ModelName)
        {
            if (!File.Exists(ModelName))
            {
                //"{0}没有找到"
                Palettes.WriteLog(string.Format(LanguageHelper.GetString("NotFoundModel"), ModelName)); return false;
            }

                
                AssemDoucument = Application.DocumentManager.Add(ModelName);
                _database = AssemDoucument.Database;
                  

            return true;
        }
        public List<string> GetALLZXVariablepoint()
        {
            /*下载暂时关闭 var fileByte = Component.GetDrawing(DrawingNO);
             if (fileByte == null)
             {
                 Palettes.WriteLog(string.Format("没找到文件 DrawingNO {0}", DrawingNO), 2);
                 return new List<string>();
             }
             var plugInpath = System.IO.Path.GetDirectoryName(typeof(Component).Assembly.Location);
             var tempPath = System.IO.Path.Combine(plugInpath, "Temp");
             if (!System.IO.Directory.Exists(tempPath))
             {
                 System.IO.Directory.CreateDirectory(tempPath);
             }
            // DelectDir(tempPath);
             DwgFile = Path.Combine(tempPath, DrawingNO + ".dwg");

             File.WriteAllBytes(DwgFile, fileByte);*/
            //"分析命名点：开始!!!"
            Palettes.WriteLog(string.Format(LanguageHelper.GetString("StartParsingNamedPoint")), 2);

            List<string> result = new List<string>();
            Database db = new Database(false, true);
            //db.ReadDwgFile(fileName, System.IO.FileShare.Read, true, null);//修改原因：不能共享读取文件。
            db.ReadDwgFile(this.DwgFile, FileOpenMode.OpenTryForReadShare, true, null);
            db.CloseInput(true);
           /* if (OpenModel(DwgFile))
            {
                Palettes.WriteLog(string.Format("打开模型成功!!!"), 2);
            }
            else
            {
                return result;
            }*/
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                foreach (var itemobject in bt)
                {
                    BlockTableRecord btr = trans.GetObject(itemobject, OpenMode.ForRead) as BlockTableRecord;
                    //this.richTextBox1.AppendText(btr.Name + "  块名" + "\r\n");
                    foreach (ObjectId objId in btr)
                    {
                        if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(DBPoint))))
                        {
                            DBPoint pt = trans.GetObject(objId, OpenMode.ForRead) as DBPoint;
                            List<string> ss = GetNamingPoint(pt);
                            foreach (var ssitem in ss)
                            {
                                //"分析命名点：{0}!!!"
                                Palettes.WriteLog(string.Format(LanguageHelper.GetString("ParsingNamedPoint"), ssitem), 2);
                                result.Add(ssitem);
                            }
                            // result.Add(pt.XData);
                        }
                    }

                    // trans.Commit();
                }
            }


            return result.Distinct().ToList();
        }
        public static void DelectDir(string srcPath)
        {
            try
            {
                DirectoryInfo dir = new DirectoryInfo(srcPath);
                FileSystemInfo[] fileinfo = dir.GetFileSystemInfos();  //返回目录中所有文件和子目录
                foreach (FileSystemInfo i in fileinfo)
                {
                    if (i is DirectoryInfo)            //判断是否文件夹
                    {
                        DirectoryInfo subdir = new DirectoryInfo(i.FullName);
                        subdir.Delete(true);          //删除子目录和文件
                    }
                    else
                    {
                        File.Delete(i.FullName);      //删除指定文件
                    }
                }
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private List<string> GetNamingPoint(DBPoint pt )
        {
            List<string> result=new List<string>();
            ResultBuffer rb = pt.XData;
            if (rb == null) return result;

            foreach (TypedValue tv in rb)
            {
                if (tv.TypeCode == (int)DxfCode.ExtendedDataAsciiString)
                {
                    if (tv.Value != null && !string.IsNullOrEmpty(tv.Value.ToString()) && tv.Value.ToString().StartsWith("ZX", true, null))
                    {
                        CADNamingPoint namingPoint = new CADNamingPoint();
                        namingPoint.Point = pt.Position;
                        namingPoint.Name = tv.Value.ToString();
                        namingPoint.CADObjectId = pt.ObjectId;
                        //"增加命名点：{0}"
                        Palettes.WriteLog(string.Format(LanguageHelper.GetString("AddParsingNamedPoint"), namingPoint.Name));
                        result.Add(namingPoint.Name);

                        break;
                    }
                }
            }
            rb.Dispose();
            return result;
        }

        //private void getaaa()
        //{
        //      BlockTableRecord btr;

        //        if (string.IsNullOrEmpty(parentBlkName)|| !bt.Has(parentBlkName))
        //        {
        //            btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

        //        }
        //        else
        //        {

        //            btr = trans.GetObject(bt[parentBlkName], OpenMode.ForWrite) as BlockTableRecord;
        //        }
        //        // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        //        // BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
        //        // BlockTableRecord btr = trans.GetObject(bt["井道装配"], OpenMode.ForWrite) as BlockTableRecord;


        //       // List<Dimension> dims = new List<Dimension>();
        //        foreach (ObjectId blockId in btr)
        //        {

        //          //  Palettes.WriteLog(blockId.ObjectClass.DxfName);
        //            //if (blockId.ObjectClass.DxfName == "DIMENSION")
        //            //{
        //            //    Dimension dime = blockId.GetObject(OpenMode.ForWrite) as Dimension;
        //            //    dims.Add(dime);
        //            //}

        //            if (string.Compare(blockId.ObjectClass.DxfName, "INSERT", true) != 0) continue;

        //            BlockReference childBlock = blockId.GetObject(OpenMode.ForWrite) as BlockReference;
        //            if (childBlock == null) continue;
        //            if (!childBlock.IsDynamicBlock) continue;
        //}

       

    }

}
