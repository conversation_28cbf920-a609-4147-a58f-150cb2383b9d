﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Drawing;
using System.Linq;
using System.Text;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using CADWSAddin;
using CADWSAddin.Tools;
using Zxtech.CADTaskServer.Contract;
using Zxtech.CADTaskServer;
using System.Xml.Linq;
using System.IO;
using System.Windows.Forms;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Configuration;
using System.ServiceModel;
using System.ServiceModel.Security.Tokens;
using System.Threading;
using System.Xml;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;


[assembly: CommandClass(typeof(CADWSAddin.WSCommand))]
[assembly: CommandClass(typeof(CADWSAddin.Palettes))]
//[assembly: CommandClass(typeof(CADWSAddin.MyDropTarget))]
//[assembly: CommandClass(typeof(Zxtech.EdisService.Contract.TaskPropFromModel))]
//[assembly: CommandClass(typeof(FormCADTaskServer))]

namespace CADWSAddin
{
    public class WSCommand : IDisposable
    {
        bool _isCommanding = false;
        string _workDir = string.Empty, _fileName = string.Empty, _filePath = string.Empty;
        
		[CommandMethod("GetTask")]
        public void GetTask()
        {
            TaskMgr task = new TaskMgr();
            task.ParsePara3("\\123.dwg");
            CADTaskServer CS = new CADTaskServer();
          //  List<CADTaskCode> ls = CS.GetTaskCadCodeList();
        }
        static int maxLevel = 0;
        Component rootComp = null;

        /// <summary>
        /// 图纸生成测试
        /// </summary>
        [CommandMethod("LDS", CommandFlags.Session)]
        public void RTEXT( XDocument xdoc,string PdsPath,bool? IscutLine ,string WorkPath,string  filePath)
        {           
            StopPrccess();
            //if (rootComp != null) rootComp.Dispose();
            
            //XML解析
            rootComp = Component.ParseTaskFile(xdoc);
            //try
            //{
            //    GC.Collect();
            //    //GC.WaitForPendingFinalizers();
            //}
            //catch(Autodesk.AutoCAD.Runtime.Exception e1)
            //{
            //    Palettes.WriteLog(e1.Message);
            //}
            //catch (System.Exception ex)
            //{
            //    Palettes.WriteLog(ex.Message);
            //}
           
            string fileName = WorkPath + "\\" + filePath + "\\" + rootComp.DrawingName + ".dwg";
           // Palettes.WriteLog(string.Format("保存文件 {0}", fileName));

            string path = Path.GetDirectoryName(fileName);
            if (!Directory.Exists(WorkPath + "\\" + filePath))
            {
                Directory.CreateDirectory(WorkPath + "\\" + filePath);
            }
            //DocumentCollection docs = null; ;
            // Palettes.WriteLog(string.Format("下载图纸  {0} ", Path.GetFileName(rootComp.ModelName)));

            /*   下载暂时关闭
              var fileByte = Component.GetDrawing(Path.GetFileName(rootComp.ModelName));
            if (fileByte == null)
            {
                Palettes.WriteLog(string.Format("没找到图纸  {0} ", Path.GetFileName(rootComp.ModelName), 2));
                return;
            }

            File.WriteAllBytes(fileName, fileByte);*/

            var myTaskCode = Palettes._listTaskCodes!=null&& Palettes._listTaskCodes.Count> Palettes._assListIndex ? Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == rootComp.PartId):null;
            if (myTaskCode!=null && myTaskCode.UseCache == true)
            {
                //holdonfiles 是因为可能会有pdf和dxf文件, 不再进行驱动子图和装配父图,也就不生成对应的pdf和dxf文件, 所以这里使用添加文件名称列表
                string cachePath = WorkPath + "\\" + filePath + "\\";
                Palettes.AddHoldOnFiles(cachePath,myTaskCode.Files);

                int interv = 100;
                if (Palettes.stationInfo.bMakePDF)
                {
                    interv = 4000;
                    if (Palettes.IniConfigData.UserInterval > 0) interv = Palettes.IniConfigData.UserInterval;
                }
            }
            else
            {
                if (File.Exists(rootComp.ModelName))
                {
                    //"复制图纸 ： {0} 到：{1}"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("CopyDrawing"), rootComp.ModelName, fileName));
                    File.Copy(rootComp.ModelName, fileName, true);
                    if (!rootComp.Exec(fileName, IscutLine,false))
                    {
                        Palettes._listTaskCodes?[Palettes._assListIndex].FirstOrDefault(o => o.PartId == rootComp.PartId).AddErInfo("NotOpenDrawing");
                        return;
                    }
                    rootComp.LockDoc();
                    //rootComp.BlockSelectAndExplode();  //炸开块 
                    //rootComp.ExecEnd();
                    //rootComp.Dispose();
                    //"炸开块:"
                    if (Palettes.stationInfo!=null)
                    {
                        Palettes.WriteLog(LanguageHelper.GetString("ExplodeBlock") + ":" + Palettes.stationInfo!=null?Palettes.stationInfo.bMakeDRW.ToString():null);
                        if (Palettes.stationInfo.bMakeDRW)
                        {
#if DEBUG
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，炸开块开始", 1);
#endif
                            using (Application.DocumentManager.MdiActiveDocument.LockDocument())
                            {
                                //"炸开块"
                                Palettes.WriteLog(LanguageHelper.GetString("ExplodeBlock"));
                                BlockTools.BlockSelectAndExplode();
                            }
#if DEBUG
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，炸开块结束", 1);
                            //尺寸上移
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，尺寸上移开始", 1);
                            rootComp.MoveDimOrderUp();
#endif

                            if (Palettes.stationInfo.DeleteNoStandardParam)
                            {
                                DimMgr dimMgrRoot = new DimMgr(rootComp.AssemDoucument.Database, true);

                                dimMgrRoot.ModifyLayerofNoStandardDim(Palettes.NoStandardDimJson, Palettes.CarryingCapacity);
                            }
#if DEBUG
                            Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff") + "统计各部分耗时，尺寸上移结束", 1);
#endif
                        }
                    }
                    
                    ////删除隐藏图层
                    //"删除隐藏图层"
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，删除隐藏尺寸开始", 1);
#endif
                    Palettes.WriteLog(LanguageHelper.GetString("DeleteHiddenLayer"));
                    // new DelHideLayer().Execute();
                    Application.DocumentManager.MdiActiveDocument.SendCommand("DelHide ");
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，删除隐藏尺寸结束", 1);
#endif
                    //图形没有使用的块较多,需要多次pu才可以清除干净
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，pu开始", 1);
#endif
                    if (rootComp.PurgeItems())
                    {
                        Palettes.WriteLog("Clear,Pu");
                    }
#if DEBUG
                    Palettes.WriteLog(DateTime.Now.ToString("HH:mm:ss.fff")+"统计各部分耗时，pu结束", 1);
#endif
                    if (rootComp.PaperSizeWidth>0&&rootComp.PaperSizeHeight>0)
                    {
                        rootComp.AddDrawingBorder();
                    }

                    
                    _workDir = WorkPath;
                    _fileName = fileName;
                    _filePath = filePath;

                    if (!string.IsNullOrEmpty(rootComp.DrawingName))
                    {
                        //rootComp.AssemDoucument.Editor.UpdateScreen();
                        //rootComp.AssemDoucument.Editor.Regen();

                        //if (Palettes.stationInfo.bMakeDXF)
                        //{
                        //    string DxfFile = WorkPath + "\\" + filePath + "\\" + rootComp.DrawingName + ".dxf"; ;
                        //    Palettes.WriteLog(string.Format("生成文件 {0}", DxfFile));
                        //    rootComp.AssemDoucument.Database.DxfOut(DxfFile, 0, GetVersion(Palettes.stationInfo.DWGVersion));
                        //    Palettes.AddHoldOnFiles(DxfFile);
                        //}
                    }
                    DimMgr dimMgr = new DimMgr(rootComp.HisDB, false);//
                    dimMgr.DeleteDimapost(); 
                    
                    rootComp.AssemDoucument.Editor.Regen();
                    Palettes.WriteLog(string.Format("AssemDoucument Regen()"));
                    
                    bool saveSuccess = false;
                    int retryCount = 0;
                    while (!saveSuccess && retryCount < 5) 
                    {
                        try
                        {
                            //if (Palettes.stationInfo.KeepOpenLastDoc)
                            //{
                            //    rootComp.AssemDoucument.Database.Save();//save不会出现有$符号开头的图层，测试是在KeepOpenLastDoc的情况下发生的
                            //}
                            //else
                            //{
                            //    var newName = Path.GetFileNameWithoutExtension(_fileName);
                            //    //rootComp.AssemDoucument.SendStringToExecute("_qsave\n", false, false, true);
                                rootComp.AssemDoucument.Database.SaveAs(_fileName, Utils.GetVersion(Palettes.stationInfo!= null?Palettes.stationInfo.DWGVersion:null));
                            //}
                            //"导出CAD图纸：{0}.DWG,CAD图纸版本 ：{1}"
                            Palettes.WriteLog(string.Format(LanguageHelper.GetString("ExportDrawingAndVersion"), fileName, Palettes.stationInfo != null ? Palettes.stationInfo.DWGVersion : null), 0);
                            saveSuccess = true;
                        }
                        catch (Autodesk.AutoCAD.Runtime.Exception ex) 
                        {
                            System.Threading.Thread.Sleep(500);   
                            retryCount++;
                        }
                        catch (System.Exception ex)
                        {
                            System.Threading.Thread.Sleep(500); 
                            retryCount++;
                        }
                    }
                    Palettes.AddHoldOnFiles(_fileName);
                    if (rootComp.NoMerage)
                    {
                        Palettes.AddNoMergeFiles(_fileName);
                    }
                    if (Palettes.totalCommands.Count>0)
                    {
                        Palettes.totalCommands.FirstOrDefault(o => o.DriveCommand.PartId == rootComp.PartId).DriveCommand.Files.Add(_fileName);
                    }
                    
                    if (!string.IsNullOrEmpty(rootComp.DrawingName))
                    {
                        if (Palettes.stationInfo!=null && Palettes.stationInfo.bMakePDF)
                        {
                            string pdfFile = _workDir + "\\" + _filePath + "\\" + rootComp.DrawingName + ".pdf"; ;
                            //"生成文件 {0}"
                            Palettes.WriteLog(string.Format(LanguageHelper.GetString("Makefile"), pdfFile));

                            // PlotTools.PlotToPDF(pdfFile);
                            new PlotingPDF().beginPrint(pdfFile, null, null, rootComp.AssemDoucument);
                            Palettes.AddHoldOnFiles(pdfFile);
                            if (rootComp.NoMerage)
                            {
                                Palettes.AddNoMergeFiles(pdfFile);
                            }
                            if (Palettes.totalCommands.Count > 0)
                            {
                                Palettes.totalCommands.FirstOrDefault(o => o.DriveCommand.PartId == rootComp.PartId).DriveCommand.Files.Add(pdfFile);

                            }//LoadWSAddin.Instance.EventHandlePlotEnd += new Autodesk.AutoCAD.PlottingServices.EndPlotEventHandler((o, ee) =>
                            //{
                            //    Palettes.ProcessNextPartTask();
                            //});
                        }
                        //else
                        //{
                        //    Palettes.ProcessNextPartTask();
                        //}
                    }
                    else
                    {
                        Palettes.WriteLog(string.Format("ERROR - file '{0}' DrawingName is null.", _fileName));
                    }
                    rootComp.UnlockDoc();
                    ////clear blocks
                    //rootComp.AssemDoucument.CommandEnded += Doc_CommandEnded;
                    //rootComp.AssemDoucument.CommandCancelled += Doc_CommandEnded;
                    //rootComp.AssemDoucument.CommandFailed += Doc_CommandEnded;
                    //rootComp.AssemDoucument.SendStringToExecute("-PURGE B\n*\nN\n", true, false, false);

                    int interv = 100;
                    if (Palettes.stationInfo!=null &&Palettes.stationInfo.bMakePDF)
                    {
                        interv = 4000;
                        if (Palettes.IniConfigData.UserInterval > 0) interv = Palettes.IniConfigData.UserInterval;
                    }

                    //            var tm = new System.Windows.Forms.Timer();
                    //            tm.Enabled = true;
                    //            tm.Interval = interv;
                    //
                    //            tm.Tick += (o, e) => { if (_isCommanding) return; tm.Enabled = false; tm.Stop(); tm = null; Palettes.WriteLog("tm Start()..."); Palettes.ProcessNextPartTask(); };
                    //            tm.Start();
                }
                else
                {
                    //"没找到图纸 '{0}', 图纸复制失败。"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("NotFoundCopyDrawingFailed"), Path.GetFileName(rootComp.ModelName), 2));
                    if (Palettes._listTaskCodes?.Count>0)
                    {
                        Palettes._listTaskCodes[Palettes._assListIndex].FirstOrDefault(o => o.PartId == rootComp.PartId).AddErInfo("NotFoundDrawing");

                    }
                    //抛出异常则在此处就发邮件并停止之后的模型查找及装配
                    //throw new FileNotFoundException(string.Format(LanguageHelper.GetString("NotFoundCopyDrawingFailed"), Path.GetFileName(rootComp.ModelName)), rootComp.ModelName);
                    //return;
                }

            }
            Palettes.ProcessNextPartTask();
        }


        void Doc_CommandEnded(object sender, CommandEventArgs e)
        {
            _isCommanding = true;

            rootComp.AssemDoucument.CommandEnded -= Doc_CommandEnded;
            rootComp.AssemDoucument.CommandCancelled -= Doc_CommandEnded;
            rootComp.AssemDoucument.CommandFailed -= Doc_CommandEnded;

            try
            {
#if _DEBUG
                Palettes.WriteLog("start Doc_CommandEnded ..."); 
#endif
                string dwgVer = Palettes.stationInfo!=null ? Palettes.stationInfo.DWGVersion:null;
                rootComp.AssemDoucument.Database.SaveAs(_fileName, Utils.GetVersion(dwgVer));
                Palettes.WriteLog(string.Format("save version '{0}'", dwgVer));
#if _DEBUG
                Palettes.WriteLog("start DisposeComponents ...");
#endif
                DisposeComponents();
#if _DEBUG
                Palettes.WriteLog("end DisposeComponents ...");
#endif
            }
            catch (Autodesk.AutoCAD.Runtime.Exception e1)
            {
                Palettes.WriteLog(e1.Message);
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(ex.Message);
            }
            finally
            {
                _isCommanding = false;
            }
        }


        void DisposeComponents()
        {
            if(rootComp != null) rootComp.Dispose();
            rootComp = null;
        }

        public void Dispose()
        {
            //DisposeComponents();
        }
        
        [CommandMethod("DelHide", CommandFlags.Modal)]
        public void DelHide()
        {
            new DelHideLayer().Execute();
        }


        [CommandMethod("MYTest", CommandFlags.Modal)]
        public static  void MYTest()
        {
            var config = ConfigurationManager.OpenExeConfiguration("CADWSAddin.dll");
            var settings = config.AppSettings.Settings;
            string endPointAddress = settings["CADTaskService"].Value;
           //安全性 none  是否启用可靠会话 false
            NetTcpBinding binding = new NetTcpBinding(SecurityMode.None, false)
                {
                    ReceiveTimeout = new TimeSpan(0, 10, 10),
                    TransferMode = TransferMode.Streamed,///传输方式
                    MaxReceivedMessageSize = 1073741824,
                    SendTimeout = new TimeSpan(0, 10, 0)
                    
                };
             binding.ReaderQuotas = new XmlDictionaryReaderQuotas
                                       {
                                           MaxArrayLength = 1073741824
                                           
                                       };
          
            var endPoint = new EndpointAddress(endPointAddress);
         
            // var taskService = new CADWSAddin.ServiceReferenceCAD.CADTaskServiceClient(binding, endPoint);
         // var t=  taskService.GetCurrentModelFile("ZXLDS通用-剖面图",null,"RD",false,"dwg",false);

        }
       
        [CommandMethod("ZoomScale", CommandFlags.Modal)]
        static public void ZoomScale()
        {
            // Get the current document
            Document acDoc = Application.DocumentManager.MdiActiveDocument;

            // Get the current view
            using (ViewTableRecord acView = acDoc.Editor.GetCurrentView())
            {
                // Get the center of the current view
                Point3d pCenter = new Point3d(acView.CenterPoint.X,
                                              acView.CenterPoint.Y, 0);

                // Set the scale factor to use
                double dScale = 1;

                // Scale the view using the center of the current view
               
                Zoom(new Point3d(), new Point3d(), pCenter, 1 / dScale);
            }
        }
        static void Zoom(Point3d pMin, Point3d pMax, Point3d pCenter, double dFactor)
        {
            // Get the current document and database
            Document acDoc = Application.DocumentManager.MdiActiveDocument;
            Database acCurDb = acDoc.Database;

            int nCurVport = System.Convert.ToInt32(Application.GetSystemVariable("CVPORT"));

            // Get the extents of the current space when no points 
            // or only a center point is provided
            // Check to see if Model space is current
            if (acCurDb.TileMode == true)
            {
                if (pMin.Equals(new Point3d()) == true &&
                    pMax.Equals(new Point3d()) == true)
                {
                    pMin = acCurDb.Extmin;
                    pMax = acCurDb.Extmax;
                }
            }
            else
            {
                // Check to see if Paper space is current
                if (nCurVport == 1)
                {
                    // Get the extents of Paper space
                    if (pMin.Equals(new Point3d()) == true &&
                        pMax.Equals(new Point3d()) == true)
                    {
                        pMin = acCurDb.Pextmin;
                        pMax = acCurDb.Pextmax;
                    }
                }
                else
                {
                    // Get the extents of Model space
                    if (pMin.Equals(new Point3d()) == true &&
                        pMax.Equals(new Point3d()) == true)
                    {
                        pMin = acCurDb.Extmin;
                        pMax = acCurDb.Extmax;
                    }
                }
            }

            // Start a transaction
            using (Transaction acTrans = acCurDb.TransactionManager.StartTransaction())
            {
                // Get the current view
                using (ViewTableRecord acView = acDoc.Editor.GetCurrentView())
                {
                    Extents3d eExtents;

                    // Translate WCS coordinates to DCS
                    Matrix3d matWCS2DCS;
                    matWCS2DCS = Matrix3d.PlaneToWorld(acView.ViewDirection);
                    matWCS2DCS = Matrix3d.Displacement(acView.Target - Point3d.Origin) * matWCS2DCS;
                    matWCS2DCS = Matrix3d.Rotation(-acView.ViewTwist,
                                                    acView.ViewDirection,
                                                    acView.Target) * matWCS2DCS;

                    // If a center point is specified, define the min and max 
                    // point of the extents
                    // for Center and Scale modes
                    if (pCenter.DistanceTo(Point3d.Origin) != 0)
                    {
                        pMin = new Point3d(pCenter.X - (acView.Width / 2),
                                            pCenter.Y - (acView.Height / 2), 0);

                        pMax = new Point3d((acView.Width / 2) + pCenter.X,
                                            (acView.Height / 2) + pCenter.Y, 0);
                    }

                    // Create an extents object using a line
                    using (Line acLine = new Line(pMin, pMax))
                    {
                        eExtents = new Extents3d(acLine.Bounds.Value.MinPoint,
                                                    acLine.Bounds.Value.MaxPoint);
                    }

                    // Calculate the ratio between the width and height of the current view
                    double dViewRatio;
                    dViewRatio = (acView.Width / acView.Height);

                    // Tranform the extents of the view
                    matWCS2DCS = matWCS2DCS.Inverse();
                    eExtents.TransformBy(matWCS2DCS);

                    double dWidth;
                    double dHeight;
                    Point2d pNewCentPt;

                    // Check to see if a center point was provided (Center and Scale modes)
                    if (pCenter.DistanceTo(Point3d.Origin) != 0)
                    {
                        dWidth = acView.Width;
                        dHeight = acView.Height;

                        if (dFactor == 0)
                        {
                            pCenter = pCenter.TransformBy(matWCS2DCS);
                        }

                        pNewCentPt = new Point2d(pCenter.X, pCenter.Y);
                    }
                    else // Working in Window, Extents and Limits mode
                    {
                        // Calculate the new width and height of the current view
                        dWidth = eExtents.MaxPoint.X - eExtents.MinPoint.X;
                        dHeight = eExtents.MaxPoint.Y - eExtents.MinPoint.Y;

                        // Get the center of the view
                        pNewCentPt = new Point2d(((eExtents.MaxPoint.X + eExtents.MinPoint.X) * 0.5),
                                                    ((eExtents.MaxPoint.Y + eExtents.MinPoint.Y) * 0.5));
                    }

                    // Check to see if the new width fits in current window
                    if (dWidth > (dHeight * dViewRatio)) dHeight = dWidth / dViewRatio;

                    // Resize and scale the view
                    if (dFactor != 0)
                    {
                        acView.Height = dHeight * dFactor;
                        acView.Width = dWidth * dFactor;
                    }

                    // Set the center of the view
                    acView.CenterPoint = pNewCentPt;

                    // Set the current view
                    acDoc.Editor.SetCurrentView(acView);
                }

                // Commit the changes
                acTrans.Commit();
            }
        }
          [CommandMethod("RunXml")]
        public void RunXml()
        {
            StopPrccess(); 
            FormInput fIn = new FormInput();
            if (fIn.ShowDialog() == DialogResult.OK)
            {
                DocumentCollection docs = null;
               // docs.CloseAll();
                TaskMgr t = new TaskMgr();

                Component rootComp = t.ParseTaskFile(XDocument.Parse(fIn.GetInputString()));
                //if (Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument != null)
                //{
                //    Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument.CloseAndDiscard();
                //}
                // Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument.clo
                //Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument = null;
               var db= CADCommon.GetDatabaseFromFile(rootComp.ModelName);



             //  rootComp.CADDatabase = db;
                //XML解析
              
               int maxLevel=0;
            //   rootComp.Exec(0, ref maxLevel);
               // rootComp.SetLayerVisibility("图层1", true);
               // rootComp.SetLayerVisibility("图层2", false);
              //  rootComp.SetLayerVisibility("图层3", false);
              

              //  Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument.Database = db;

                string fileName = "d:\\tempDr\\" + "temp" + ".dwg";
                string path = Path.GetDirectoryName(fileName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
              //  rootComp.CADDatabase.SaveAs(fileName, DwgVersion.AC1015);
               // Application.DocumentManager.MdiActiveDocument = docs.Add(fileName);
         
 
            }
        }

        [CommandMethod("TestXml", CommandFlags.Session)]
        public void TestXml()
        {
            StopPrccess();
            FormInput fIn = new FormInput();
            string PdsPath = "D:\\Drawing1\\testModel";
            string WorkPath = "D:\\CADWorkDir_AutoCAD\\testTask";
            string curTaskFoldName = DateTime.Now.ToString("yyyyMMddhhmmss");
            if (fIn.ShowDialog() == DialogResult.OK)
            {
                RTEXT(XDocument.Parse(fIn.GetInputString()), PdsPath, true, WorkPath, curTaskFoldName);

            }
        }

        [CommandMethod("XmlDriveBlock", CommandFlags.Modal)]
        public void XmlDriveBlock()
        {

            FormInput fIn = new FormInput();
            Component component = new Component();
            
            if (fIn.ShowDialog() == DialogResult.OK)
            {
                var functions = ParseFunctions(fIn.GetInputString());
                List<Function> dimFunctions = functions.Where(o => o.Name == "SetVariable").ToList();
                component.Functions = dimFunctions;
                
            }
            
            
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            component.HisDB = doc.Database;
            TypedValue[] filListBlock = new TypedValue[1];
            filListBlock[0] = new TypedValue(0, "Insert");
            SelectionFilter filterBlock = new SelectionFilter(filListBlock);
            PromptSelectionResult resBlock = ed.GetSelection(filterBlock);
            if (resBlock.Status != PromptStatus.OK)
                return;
            using (Transaction tran = doc.Database.TransactionManager.StartTransaction())
            {
                foreach (ObjectId id in resBlock.Value.GetObjectIds())
                {
                    component.DriveDynamicBlock(id);
                }
                
            }
            
        }

        public List<Function> ParseFunctions(string xmlContent)
        {
            // 加载 XML 文档
            XDocument doc = XDocument.Parse(xmlContent);

            // 使用 Descendants 获取所有 Function 节点（忽略命名空间）
            var functions = doc.Descendants()
                .Where(e => e.Name.LocalName == "Function")
                .Select(func => new Function {
                    Name = func.Element("Name")?.Value,
                    OperObject = func.Element("OperObject")?.Value,
                    Value = func.Element("Value")?.Value
                })
                .ToList();

            return functions;
        }
        [CommandMethod("BlockAll", CommandFlags.Modal)]
          public void BlockAll()
          {
              BlockTools.BlockSelectAndExplode();
          }
         [CommandMethod("SelectBlockRef")]
          public void SelectBlockRef()
          {
              Document doc = Application.DocumentManager.MdiActiveDocument;
              Editor ed = doc.Editor;
              TypedValue[] filListBlock = new TypedValue[1];
              filListBlock[0] = new TypedValue(0, "Insert");
              SelectionFilter filterBlock = new SelectionFilter(filListBlock);
              //选择对象
              PromptSelectionResult resBlock = ed.SelectAll(filterBlock);
          }
         [CommandMethod("ClearBlock", CommandFlags.Modal)]
         public void ClearBlock()
        {
             //.acedCommand(RTSTR, _T("-purge"), RTSTR, _T("A"), RTSTR, _T("*"), RTSTR, _T("N"), RTNONE);

            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;

            //  per
            //  -purge 表示使用命令的“静默模式”，不会提示用户确认操作
            // "A"表示删除所有未使用的命名对象
            // "*" 表示扫描所有的命名对象
            // "N" 表示不删除重复的命名对象
            ed.Command("-purge", "A", "*", "N");


        }

     

        public static void StopPrccess()
          {
              var processNames = GetProcessName();
              foreach (var processName in processNames)
              {
                  if (string.IsNullOrWhiteSpace(processName))
                      continue;
                  //"查找进程名为‘{0}’的进程"
                  Palettes.WriteLog(string.Format(LanguageHelper.GetString("FindProcessByName"), processName));
                  foreach (Process process in Process.GetProcessesByName(processName))
                  {
                    //"开始关闭进程名为‘{0}’'{1}'的进程"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("StartingCloseProcessByName"), processName, process.Id));
                      process.Kill();
                      process.WaitForExit();
                    //"已关闭进程名为‘{0}’'{1}'的进程的进程"
                    Palettes.WriteLog(string.Format(LanguageHelper.GetString("HasCloseProcessByName"), processName, process.Id));
                  }
              }
          }
          private static string[] GetProcessName()
          {
              var path = System.IO.Path.GetDirectoryName(typeof(WSCommand).Assembly.CodeBase.Replace("file:///", ""));
              var fileName = Path.Combine(path, "DeleteProcess.txt");
              string config = string.Empty;
              if(File.Exists(fileName))
                  config=File.ReadAllText(Path.Combine(path, "DeleteProcess.txt"));
              return config.Split(',');
          }

          private string GetINIPath()
          {
              var path = Path.GetDirectoryName(this.GetType().Assembly.Location);
              var  iniFilepath = Path.Combine(path, "cad3dproe.ini");
              return iniFilepath;
             
          }
          private string GetSaveVar()
          {
              System.Text.StringBuilder temp = new System.Text.StringBuilder(5);
              GetPrivateProfileString("Control", "SaveVer", "", temp, 5, GetINIPath());
              return temp.ToString();
          }

          [DllImport("kernel32")]
          private static extern int GetPrivateProfileString(string section, string key, string def, System.Text.StringBuilder retVal, int size, string filePath);


          [CommandMethod("MyDim", CommandFlags.Modal)]
          public void DimTest()
          {
              DimensionTidy dimTidy = new DimensionTidy();
              dimTidy.Execute();
 
          }
          [CommandMethod("MyDim2", CommandFlags.Modal)]
          public void DimTest2()
          {
              Document doc = Application.DocumentManager.MdiActiveDocument;
              DimMgr dimMgr = new DimMgr(doc.Database,false);
              dimMgr.DimTidy();

          }
          [CommandMethod("BlockExp", CommandFlags.Modal)]
          public void BlockExp()
          {
              BlockExp be = new BlockExp();
              be.Execute(true);
          }

          [CommandMethod("DwgInfo")]
          public void DwgInfo()
          {
              FormDwgInfo dInfo = new FormDwgInfo();
              Application.ShowModalDialog(dInfo);
          }
          [CommandMethod("SelectBlock", CommandFlags.Modal)]
          public void Select()
          {

              Document doc = Application.DocumentManager.MdiActiveDocument;
              Editor ed = doc.Editor;
              TypedValue[] filListBlock = new TypedValue[1];
              filListBlock[0] = new TypedValue(0, "Insert");
              //  DxfCode.BlockName
              SelectionFilter filterBlock = new SelectionFilter(filListBlock);
              // SelectionFilter
              //选择对象
              PromptSelectionResult resBlock = ed.SelectAll(filterBlock);

              if (resBlock.Status == PromptStatus.OK)
              {
                  //  Autodesk.AutoCAD.Internal.Utils.SelectObjects(resBlock.Value);
                 // ed.SetImpliedSelection(resBlock.Value);
                  List<ObjectId> selectObj = new List<ObjectId>();
                  foreach (ObjectId id in resBlock.Value.GetObjectIds())
                  {



                      if (id.ObjectClass.DxfName != "ACAD_TABLE")//当块参照为表格时，不炸开。
                      {
                          selectObj.Add(id);
                      }
                  }
                  if (selectObj.Count > 0)
                  {
                      ed.SetImpliedSelection(selectObj.ToArray());
                     Application.DocumentManager.MdiActiveDocument.SendCommand("EXPLODE ");
                  }
              }





              // ed.SetImpliedSelection

              //  PromptSelectionOptions promptSelOpt = new PromptSelectionOptions();
              //  promptSelOpt.MessageForAdding = "请选择块";
              // PromptSelectionResult promptSelResult = ed.GetSelection(promptSelOpt, filterBlock);

              //SelectionFilter
          }
        /// <summary>
        /// 模型参数提取
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public string GetModleData(string file)
        {
            Document doc = DocumentMgr.OpenDocument(file, true);
            Database db = doc.Database;
            string returnDate = null;
            List<string> blockNamels = new List<string>();//块名列表
            List<string> parameterNamels = new List<string>();//变量列表（含变量所在块名）
            List<string> tableNamels = new List<string>();//表格列表
            List<string> textNamels = new List<string>();//文本列表
            List<string> textStyleNamels = new List<string>();//文本样式列表
            List<string> dimStyleNamels = new List<string>();//标注样式列表
            TextOper to = new TextOper();
            textNamels = to.GetMText(textNamels);
            dimStyleNamels = DimStyleTools.GetDimStyleName(db);
            textStyleNamels = TextStyleOper.GetTextStyleName(db);
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {
                    if (id.ObjectClass.DxfName == "INSERT")
                    {
                        blockNamels.Add(BlockTools.GetBlockName(id));
                        var props = id.GetDynProperties();
                        foreach (DynamicBlockReferenceProperty prop in props)
                        {
                            if (prop.PropertyName != "Origin")
                            {
                                parameterNamels.Add(prop.PropertyName + "@" + BlockTools.GetBlockName(id));
                            }
                        }
                    }
                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForRead) as Table);
                        tableNamels.Add(table.GetTableTitle());
                    }
                }
            }
            for (int i = 0; i < parameterNamels.Count; i++)
            {
                returnDate += parameterNamels[i] + ";";
            }
            return returnDate;
        }
        /// <summary>
        /// 模型参数提取测试
        /// </summary>
        [CommandMethod("GetData")]
        public void GetData()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            List<string> blockNamels = new List<string>();//块名列表
            List<string> parameterNamels = new List<string>();//变量列表（含变量所在块名）
            List<string> tableNamels = new List<string>();//表格列表
            List<string> textNamels = new List<string>();//文本列表
            List<string> textStyleNamels = new List<string>();//文本样式列表
            List<string> dimStyleNamels = new List<string>();//标注样式列表
            TextOper to = new TextOper();
            textNamels = to.GetMText(textNamels);
            dimStyleNamels = DimStyleTools.GetDimStyleName(db);
            textStyleNamels = TextStyleOper.GetTextStyleName(db);
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {
                    if (id.ObjectClass.DxfName == "INSERT")
                    {
                        blockNamels.Add(BlockTools.GetBlockName(id));
                        var props = id.GetDynProperties();
                        foreach (DynamicBlockReferenceProperty prop in props)
                        {
                            if (prop.PropertyName != "Origin")
                            {
                                parameterNamels.Add(prop.PropertyName + "@" + BlockTools.GetBlockName(id));
                            }
                        }
                    }
                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForRead) as Table);
                        tableNamels.Add(table.GetTableTitle());
                    }
                }
            }
        }
        [CommandMethod("gde")]
        public void TestBlock()
        {
            var database = Application.DocumentManager.MdiActiveDocument.Database;
            Transaction transaction = database.TransactionManager.StartTransaction();
            using (transaction)
            {
                Entity entity = null;
                BlockTable bt = (BlockTable)database.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = (BlockTableRecord)transaction.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);
                List<BlockReference> temp = new List<BlockReference>();
                foreach (ObjectId id in btr)
                {
                    entity = (Entity)transaction.GetObject(id, OpenMode.ForWrite);
                    if (entity is BlockReference)
                    {
                        if (entity.ObjectId.ObjectClass.DxfName != "ACAD_TABLE")//当块参照为表格时，不炸开。
                        {

                            BlockReference br = (BlockReference)entity;
                            if (!br.IsDynamicBlock) continue;
                            temp.Add(br);

                            //br.ExplodeToOwnerSpace();
                            // br.Erase();

                        }
                    }
                }
                foreach (var item in temp)
                {
                    item.ExplodeToOwnerSpace();
                }
                transaction.Commit();
            }
        }
        /// <summary>
        /// 标注样式替换测试
        /// </summary>
        //[CommandMethod("TestDimStyle")]
        public void TestDimStyle(TaskMgr t)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                for (int i = 0; i < t.DimSourceStylels.Count; i++)
                {
                    DimStyleTools.ChangeDimStyle(db, t.DimSourceStylels[i], t.DimTargetStylels[i]);
                }
                trans.Commit();
            }
        
        }
        [CommandMethod("TestDim")]
        public void TestDim()
        {
            
            try
            {
                using (Transaction trans = Application.DocumentManager.MdiActiveDocument.TransactionManager.StartTransaction())
                {
                   
                        foreach (Entity ent in Application.DocumentManager.MdiActiveDocument.Database.GetEntsInDatabase())
                        {
                            if (ent.GetRXClass().DxfName == "DIMENSION")
                            {
                                Dimension dim = (Dimension)ent;
                              dim.GenerateLayout();
                            }
                        }
                      
                    trans.Commit();
                }
            }
            catch
            {
            }
        }
        
        /// <summary>
        /// 文字样式替换测试
        /// </summary>
        //[CommandMethod("TestTextStyle")]
        public void TestTextStyle(TaskMgr t)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                for (int i = 0; i < t.TextSourceStylels.Count; i++)
                {
                    TextStyleOper.ChangeTextStyle(db, t.TextSourceStylels[i], t.TextTargetStylels[i]);
                }
                trans.Commit();
            }
        }
        /// <summary>
        /// 标注测试
        /// </summary>
        //[CommandMethod("testDim")]
        public void TestDim(TaskMgr t)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            for (int z = 0; z < t.DimValuels.Count(); z++)
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    //List<string> CenterPointls = DimStyleTools.xmlDimList("CenterPoint");
                    //List<string> StartPointls = DimStyleTools.xmlDimList("StartPoint");
                    //List<string> EndPointls = DimStyleTools.xmlDimList("EndPoint");
                    //List<string> ReferenceStartPointls = DimStyleTools.xmlDimList("ReferenceStartPoint");
                    //List<string> ReferenceEndPointls = DimStyleTools.xmlDimList("ReferenceEndPoint");
                    //List<string> LayerNumberls = DimStyleTools.xmlDimList("LayerNumber");
                    //List<string> LayerDistancels = DimStyleTools.xmlDimList("LayerDistance");
                    //List<string> Heightls = DimStyleTools.xmlDimList("Height");
                    //List<string> Typels = DimStyleTools.xmlDimList("Type");
                    //List<string> Textls = DimStyleTools.xmlDimList("Text");
                    //List<string> Stylels = DimStyleTools.xmlDimList("Style");
                    List<string> ls = new List<string>();
                    ls = new TableOper(null).htmlTable(t.DimValuels[z]);
                    List<string> CenterPointls= new List<string>();
                    List<string> StartPointls = new List<string>();
                    List<string> EndPointls = new List<string>();
                    List<string> ReferenceStartPointls = new List<string>();
                    List<string> ReferenceEndPointls = new List<string>();
                    List<string> LayerNumberls = new List<string>();
                    List<string> LayerDistancels = new List<string>();
                    List<string> Heightls = new List<string>();
                    List<string> Typels = new List<string>();
                    List<string> Textls =new List<string>();
                    List<string> Stylels = new List<string>();
                    for (int c = 0; c < ls.Count/11;c++)
                    {
                        CenterPointls.Add(ls[c]);
                        StartPointls.Add(ls[c + ls.Count/11]);
                        EndPointls.Add(ls[c + 2 * ls.Count / 11]);
                        ReferenceStartPointls.Add(ls[c + 3 * ls.Count / 11]);
                        ReferenceEndPointls.Add(ls[c + 4 * ls.Count / 11]);
                        LayerNumberls.Add(ls[c + 5 * ls.Count / 11]);
                        LayerDistancels.Add(ls[c + 6 * ls.Count / 11]);
                        Heightls.Add(ls[c + 7 * ls.Count / 11]);
                        Typels.Add(ls[c + 8 * ls.Count / 11]);
                        Textls.Add(ls[c + 9 * ls.Count / 11]);
                        Stylels.Add(ls[c + 10 * ls.Count / 11]);
                    }
                    //创建一个列表，用于存储标注对象
                    List<Dimension> dims = new List<Dimension>();
                    DimStyleTable dst = (DimStyleTable)db.DimStyleTableId.GetObject(OpenMode.ForRead);
                    for (int i = 0; i < StartPointls.Count; i++)
                    {
                        Point3d CenterPoint = Tools.Tools.GetPoint(db, CenterPointls[i]);
                        Point3d StartPoint = new Point3d();
                        Point3d EndPoint = new Point3d();
                        int height = new int();
                        int count = new int();
                        if (ReferenceStartPointls[i] == "-")
                        {
                            height = Convert.ToInt32(Heightls[i].Trim());
                            StartPoint = Tools.Tools.GetPoint(db, StartPointls[i]);
                            EndPoint = Tools.Tools.GetPoint(db, EndPointls[i]);
                        }
                        else
                        {
                            count = Convert.ToInt32(LayerNumberls[i].Trim());
                            height = Convert.ToInt32(LayerDistancels[i].Trim()) * count;
                            StartPoint = Tools.Tools.ArrangementPoint(db, ReferenceStartPointls[i], ReferenceEndPointls[i], StartPointls[i], Typels[i], height, count);
                            EndPoint = Tools.Tools.ArrangementPoint(db, ReferenceStartPointls[i], ReferenceEndPointls[i], EndPointls[i], Typels[i], height, count);
                        }
                        switch (Typels[i])
                        {
                            case "X":
                                // 创建转角标注（水平）
                                RotatedDimension dimRotated1 = new RotatedDimension();
                                //指定第一条尺寸界线的附着位置
                                dimRotated1.XLine1Point = StartPoint;
                                //指定第二条尺寸界线的附着位置
                                dimRotated1.XLine2Point = EndPoint;
                                //指定尺寸线的位置
                                if (count != 0)
                                {
                                    if (StartPointls[i].Contains("$"))
                                    {
                                        dimRotated1.DimLinePoint = StartPoint.PolarPoint(Math.PI / 2, height);
                                    }
                                    else dimRotated1.DimLinePoint = StartPoint.PolarPoint(Math.PI / 2, height / count);
                                }
                                dimRotated1.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimRotated1.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimRotated1);//将水平转角标注添加到列表中
                                break;
                            case "Y":
                                //创建转角标注(垂直）
                                RotatedDimension dimRotated2 = new RotatedDimension();
                                dimRotated2.Rotation = Math.PI / 2;//转角标注角度为90度，表示垂直方向
                                //指定两条尺寸界线的附着位置和尺寸线的位置
                                dimRotated2.XLine1Point = StartPoint;
                                dimRotated2.XLine2Point = EndPoint;
                                if (StartPointls[i].Contains("$"))
                                {
                                    dimRotated2.DimLinePoint = StartPoint.PolarPoint(0, height);
                                }
                                else dimRotated2.DimLinePoint = StartPoint.PolarPoint(0, height / count);
                                dimRotated2.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimRotated2.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimRotated2);//将垂直转角标注添加到列表中
                                break;
                            case "D":
                                // 创建对齐标注
                                AlignedDimension dimAligned = new AlignedDimension();
                                //指定两条尺寸界线的附着位置和尺寸线的位置
                                dimAligned.XLine1Point = StartPoint;
                                dimAligned.XLine2Point = EndPoint;
                                dimAligned.DimLinePoint = StartPoint.PolarPoint(Math.Abs(Math.Atan((StartPoint.X - EndPoint.X) / (StartPoint.Y - EndPoint.Y))), height);
                                dimAligned.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimAligned.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimAligned);//将对齐标注添加到列表中
                                break;
                            case "RAD":
                                // 创建半径标注
                                RadialDimension dimRadial = new RadialDimension();
                                dimRadial.Center = StartPoint;//圆或圆弧的圆心
                                //用于附着引线的圆或圆弧上的点
                                dimRadial.ChordPoint = EndPoint;
                                dimRadial.LeaderLength = height;//引线长度
                                dimRadial.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimRadial.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimRadial);//将半径标注添加到列表中
                                break;
                            case "DIA":
                                // 创建直径标注
                                DiametricDimension dimDiametric = new DiametricDimension();
                                //圆或圆弧上第一个直径点的坐标
                                dimDiametric.ChordPoint = StartPoint;
                                //圆或圆弧上第二个直径点的坐标
                                dimDiametric.FarChordPoint = EndPoint;
                                dimDiametric.LeaderLength = height;//引线长度;
                                dimDiametric.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimDiametric.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimDiametric);//将直径标注添加到列表中
                                break;
                            case "ANG":
                                // 创建角度标注
                                Point3AngularDimension dimLineAngular = new Point3AngularDimension();
                                //圆或圆弧的圆心、或两尺寸界线间的共有顶点的坐标
                                dimLineAngular.CenterPoint = CenterPoint;
                                //指定两条尺寸界线的附着位置
                                dimLineAngular.XLine1Point = StartPoint;
                                dimLineAngular.XLine2Point = EndPoint;
                                //设置角度标志圆弧线上的点
                                dimLineAngular.ArcPoint = StartPoint.PolarPoint(GeTools.DegreeToRadian(0), height);
                                dimLineAngular.DimensionText = Textls[i].Replace("-", "<>");//设置标注的文字为传递值
                                dimLineAngular.DimensionStyle = dst[Stylels[i]];//设置标注样式
                                dims.Add(dimLineAngular);//将角度标注添加到列表中
                                break;
                        }
                    }
                    foreach (Dimension dim in dims)//遍历标注列表
                    {

                        db.AddToModelSpace(dim);//将标注添加到模型空间中
                    }
                    trans.Commit();//提交更改
                }
            }
        }
        /// <summary>
        /// 表格测试
        /// </summary>
        //[CommandMethod("testTable")]
        public void TestTable(TaskMgr t)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                foreach (ObjectId id in btr)
                {
                    if (id.ObjectClass.DxfName == "ACAD_TABLE")
                    {
                        TableOper table = new TableOper(id.GetObject(OpenMode.ForWrite) as Table);
                        for (int z = 0; z < t.TableNamels.Count; z++)
                        {
                            if (table.GetTableTitle().Contains(t.TableNamels[z]))
                            {
                                List<string> ls = null;
                                ls = new TableOper(null).htmlTable(t.TableValuels[z]);
                                //table.InsertData(ls);
                                if (ls.Count != 0 && table.GetColumnsCount() != 0)
                                {
                                    for (int i = 0; i < ls.Count; )
                                    {
                                        List<string> lstable = new List<string>();
                                        for (int j = 0; j < table.GetColumnsCount(); j++)
                                        {
                                            lstable.Add(ls[j * ((ls.Count + j) / table.GetColumnsCount()) - j]);
                                            ls.Remove(ls[j * ((ls.Count + j) / table.GetColumnsCount()) - j]);
                                        }
                                        if (table.GetRow(2)[0] != "")
                                        {
                                            table.InsertData(lstable);
                                        }
                                        else table.UpdateData(2, lstable);
                                    }
                                }
                            }
                        }
                    }
                }
                trans.Commit();
            }

        }
        /// <summary>
        /// 文字测试
        /// </summary>
        //[CommandMethod("testT")]
        public void TestT(TaskMgr t)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                //List<string> SourceTextls = TextStyleOper.xmlTextList("SourceText");
                //List<string> TargetTextls = TextStyleOper.xmlTextList("TargetText");
                TextOper tO = new TextOper();
                for (int i = 0; i < t.SourceTextls.Count; i++)
                {
                    tO.ReplaceMText(t.SourceTextls[i], t.TargetTextls[i]);
                }
                trans.Commit();
            }
        }
        /// <summary>
        /// 图层测试
        /// </summary>
        //[CommandMethod("testLayer")]
        public void TestLayer(TaskMgr t)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            DocOper Layer = new DocOper(doc);
            for (int i = 0; i < t.LayerNamels.Count; i++)
            {
                if (t.LayerValuels[i] == "Y")
                    Layer.SetLayerVisibility(t.LayerNamels[i], true);
                else
                    Layer.SetLayerVisibility(t.LayerNamels[i], false);
            }
        }
        /// <summary>
        /// 显示扩展数据
        /// </summary>
        [CommandMethod("ShowX")]
        public void ShowX()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            //提示用户选择一个表示董事长的多行文本
            //请选择要显示扩展数据的多行文本
            PromptEntityOptions opt = new PromptEntityOptions("\n"+LanguageHelper.GetString("Msg_PleaseSelectMTextDisplayExtData"));
            //您选择的不是多行文本，请重新选择
            opt.SetRejectMessage("\n"+ LanguageHelper.GetString("Msg_PleaseSelectMTextAgain"));
            opt.AddAllowedClass(typeof(DBPoint), true);
            PromptEntityResult entResult = ed.GetEntity(opt);
            ObjectId id = entResult.ObjectId;//用户选择的多行文本的ObjectId
            string employeeInfo = "";
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                TypedValueList xdata = entResult.ObjectId.GetXData("NAME");
                if (xdata != null)
                {
                    employeeInfo += "NAME：" + xdata[1].Value.ToString();
                }
                ed.WriteMessage(employeeInfo);
                trans.Commit();
            }
        }
        /// <summary>
        /// 添加扩展数据
        /// </summary>
        [CommandMethod("AddX")]
        public void AddX()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            //提示用户选择一个要命名的点
            //请选择要命名的点
            PromptEntityOptions opt = new PromptEntityOptions("\n"+LanguageHelper.GetString("Msg_PleaseSelectAPointToName"));
            //您选择的不是点，请重新选择
            opt.SetRejectMessage("\n"+ LanguageHelper.GetString("Msg_PleaseSelectAPointAgain"));
            opt.AddAllowedClass(typeof(DBPoint), true);
            PromptEntityResult entResult = ed.GetEntity(opt);
            if (entResult.Status != PromptStatus.OK) return;
            ObjectId id = entResult.ObjectId;//用户选择的点的ObjectId
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                TypedValueList values = new TypedValueList();//定义一个TypedValue列表
                //添加整型（表示员工编号）和字符串（表示职位）扩展数据项
                values.Add(DxfCode.ExtendedDataInteger32, 1002);
                values.Add(DxfCode.ExtendedDataAsciiString, "董事长");
                //为实体添加应用程序名为"NAME"的扩展数据
                id.AddXData("NAME", values);
                trans.Commit();
            }
        }
        /// <summary>
        /// 修改扩展数据
        /// </summary>
        [CommandMethod("ModX")]
        public void ModX()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            //提示用户选择一个多行文本
            //请选择点
            PromptEntityOptions opt = new PromptEntityOptions("\n"+ LanguageHelper.GetString("Msg_PleaseSelectPoint"));
            //您选择的不是点，请重新选择
            opt.SetRejectMessage("\n"+ LanguageHelper.GetString("Msg_PleaseSelectAPointAgain"));
            opt.AddAllowedClass(typeof(DBPoint), true);
            PromptEntityResult entResult = ed.GetEntity(opt);
            if (entResult.Status != PromptStatus.OK) return;
            ObjectId id = entResult.ObjectId;//用户选择的多行文本的ObjectId
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                //如果扩展数据项（员工编号）为1002，则将其修改为1001
                id.ModXData("NAME", DxfCode.ExtendedDataInteger32, 1002, 1001);
                trans.Commit();
            }
        }
        /// <summary>
        /// 删除扩展数据
        /// </summary>
        [CommandMethod("DelX")]
        public void DelX()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            //提示用户选择一个多行文本
            //请选择点
            PromptEntityOptions opt = new PromptEntityOptions("\n" + LanguageHelper.GetString("Msg_PleaseSelectPoint"));
            //您选择的不是点，请重新选择
            opt.SetRejectMessage("\n"+ LanguageHelper.GetString("Msg_PleaseSelectAPointAgain"));
            opt.AddAllowedClass(typeof(DBPoint), true);
            PromptEntityResult entResult = ed.GetEntity(opt);
            if (entResult.Status != PromptStatus.OK) return;
            ObjectId id = entResult.ObjectId;//用户选择的多行文本的ObjectId
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                id.RemoveXData("NAME"); // 删除EMPLOYEE扩展数据
                trans.Commit();
            }
        }
        [CommandMethod("StartMonitor")]
        public void StartMonitor()
        {
            Editor ed = Application.DocumentManager.MdiActiveDocument.Editor;
            //开始鼠标监控事件
            ed.PointMonitor += new PointMonitorEventHandler(ed_PointMonitor);
        }

        void ed_PointMonitor(object sender, PointMonitorEventArgs e)
        {
            string employeeInfo = "";//用于存储员工的信息：编号和职位
            //获取命令行对象（鼠标监视事件的发起者），用于获取文档对象
            Editor ed = (Editor)sender;
            Document doc = ed.Document;
            //获取鼠标停留处的实体
            FullSubentityPath[] paths = e.Context.GetPickedEntities();
            using (Transaction trans = doc.TransactionManager.StartTransaction())
            {
                //如果鼠标停留处有实体
                if (paths.Length > 0)
                {
                    //获取鼠标停留处的实体
                    FullSubentityPath path = paths[0];
                    MText mtext = trans.GetObject(path.GetObjectIds()[0], OpenMode.ForRead) as MText;
                    if (mtext != null)//如果鼠标停留处为多行文本
                    {
                        //获取多行文本中应用程序名为“EMPLOYEE”的扩展数据
                        TypedValueList xdata = mtext.ObjectId.GetXData("NAME");
                        if (xdata != null)
                        {
                            //员工编号 职位
                            employeeInfo += LanguageHelper.GetString("EmployeeNo") + "：" + xdata[1].Value.ToString() + "\n"+ LanguageHelper.GetString("Position") + "：" + xdata[2].Value.ToString();
                        }
                    }
                }
                trans.Commit();
            }
            if (employeeInfo != "")
            {
                e.AppendToolTipText(employeeInfo);//在鼠标停留处显示提示信息
            }
        }

        [CommandMethod("StopMonitor")]
        public void StopMonitor()
        {
            Editor ed = Application.DocumentManager.MdiActiveDocument.Editor;
            //停止鼠标监控事件
            ed.PointMonitor -= new PointMonitorEventHandler(ed_PointMonitor);
        }
        [CommandMethod("AddMyPaletteSet")]
        public void AddMyPaletteSet()
        {
           // Autodesk.AutoCAD.Windows.PaletteSet myPaletteSet = new Autodesk.AutoCAD.Windows.PaletteSet("特性");
            // myPaletteSet.Add("我的工具集", new System.Windows.Forms.Control("我的工具集"));
            //myPaletteSet.Add("我的颜色", new System.Windows.Forms.Control("我的颜色"));
            //myPaletteSet.Add("我的控件", new System.Windows.Forms.Control("我的控件"));
           // myPaletteSet.Visible = true;
        }

        //[CommandMethod("printDxf")]
        //public void printDxf()
        //{
        //    var curDoc = Application.DocumentManager.MdiActiveDocument;
        //    if (curDoc == null) return;

        //    var file = "e:\\dddd\\a.dxf";
        //    if(File.Exists(file)) File.Delete(file);
        //    curDoc.Database.DxfOut(file, 16, false);
        //}

        //[CommandMethod("aaCombineDwg")]
        //public void aaCombineDwg()
        //{
        //    var cmd = new DwgCombiner(200, true, false);

        //    var dir = @"D:\bbb\aaa";
        //    var outDwg1 = @"D:\bbb\mergeDwgsaa.dwg";
        //    var files = Directory.GetFiles(dir, "*.dwg", SearchOption.TopDirectoryOnly).ToList();

        //    if (File.Exists(outDwg1)) File.Delete(outDwg1);
        //    cmd.MergeDwgFiles(files, outDwg1);
        //}        
        [CommandMethod("printDimensionInfo")]
        public void printDimensionInfo()
        {
            var curDoc = Application.DocumentManager.MdiActiveDocument;
            if (curDoc == null) return;

            var propNames = new List<string>() {
                "AlternatePrefix",
                "AlternateSuffix",
                "AltSuppressLeadingZeros",
                "AltSuppressTrailingZeros",
                "AltSuppressZeroFeet",
                "AltSuppressZeroInches",
                "AltToleranceSuppressLeadingZeros",
                "AltToleranceSuppressTrailingZeros",
                "AltToleranceSuppressZeroFeet",
                "AltToleranceSuppressZeroInches",
                "CenterMarkSize",
                "Dimadec",
                "Dimalt",
                "Dimaltd",
                "Dimaltf",
                "Dimaltmzf",
                "Dimaltmzs",
                "Dimaltrnd",
                "Dimalttd",
                "Dimalttz",
                "Dimaltu",
                "Dimaltz",
                "Dimapost",
                "Dimarcsym",
                "Dimasz",
                "Dimatfit",
                "Dimaunit",
                "Dimazin",
                "Dimcen",
                "Dimdec",
                "Dimdle",
                "Dimdli",
                "Dimdsep",
                "DimensionStyleName",
                "DimensionText",
                "Dimexe",
                "Dimexo",
                "Dimfrac",
                "Dimfxlen",
                "DimfxlenOn",
                "Dimgap",
                "Dimjogang",
                "Dimjust",
                "Dimlfac",
                "Dimlim",
                "Dimlunit",
                "Dimmzf",
                "Dimmzs",
                "Dimpost",
                "Dimrnd",
                "Dimsah",
                "Dimscale",
                "Dimsd1",
                "Dimsd2",
                "Dimse1",
                "Dimse2",
                "Dimsoxd",
                "Dimtad",
                "Dimtdec",
                "Dimtfac",
                "Dimtfill",
                "Dimtih",
                "Dimtix",
                "Dimtm",
                "Dimtmove",
                "Dimtofl",
                "Dimtoh",
                "Dimtol",
                "Dimtolj",
                "Dimtp",
                "Dimtsz",
                "Dimtvp",
                "Dimtxt",
                "Dimtxtdirection",
                "Dimtzin",
                "Dimupt",
                "Dimzin",
                "DynamicDimension",
                "Elevation",
                "HorizontalRotation",
                "Measurement",
                "Prefix",
                "Suffix",
                "SuppressAngularLeadingZeros",
                "SuppressAngularTrailingZeros",
                "SuppressLeadingZeros",
                "SuppressTrailingZeros",
                "SuppressZeroFeet",
                "SuppressZeroInches",
                "TextLineSpacingFactor",
                "TextRotation",
                "ToleranceSuppressLeadingZeros",
                "ToleranceSuppressTrailingZeros",
                "ToleranceSuppressZeroFeet",
                "ToleranceSuppressZeroInches",
                "UsingDefaultTextPosition",
            };

            var sb = new StringBuilder();
            var ext = ".txt";
            var file = "D:\\bbb\\dimInfo";
            var path = file + ext;

            var db = curDoc.Database;
            using (Transaction tran = db.TransactionManager.StartTransaction())
            {
                BlockTableRecord btr = null;
                BlockTable bt = tran.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;

                btr = tran.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(RotatedDimension)))) PrintDimensionInfo(propNames, sb, tran, objId);
                    else if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(MText))))
                    {
                        sb.AppendLine("***************************************************************");
                        MText mt = tran.GetObject(objId, OpenMode.ForRead) as MText;

                        sb.AppendLine(string.Format("MText: {0}, location: ({1}, {2}), w: {3}, h: {4}", mt.Text, mt.Location.X, mt.Location.Y, mt.ActualWidth, mt.ActualHeight));                        
                    }
                    else if (objId.ObjectClass.Equals(RXClass.GetClass(typeof(DBText))))
                    {
                        sb.AppendLine("***************************************************************");
                        DBText mt = tran.GetObject(objId, OpenMode.ForRead) as DBText;

                        sb.AppendLine(string.Format("DBText: {0}, location: ({1}, {2}), w: {3}, h: {4}", mt.TextString, mt.Position.X, mt.Position.Y, mt.WidthFactor, mt.Height));
                    }
                }


            }

            int i = 1;
            while (File.Exists(path)) path = string.Format("{0}{1}{2}", file, ++i, ext);

            File.AppendAllText(path, sb.ToString());
        }

        private static void PrintDimensionInfo(List<string> propNames, StringBuilder sb, Transaction tran, ObjectId objId)
        {
            sb.AppendLine("//////////////////////////////////////////////////////////////");
            RotatedDimension dim = tran.GetObject(objId, OpenMode.ForRead) as RotatedDimension;
            foreach (var it in propNames) { var v = dim.GetType().InvokeMember(it, System.Reflection.BindingFlags.GetProperty, null, dim, null); sb.AppendLine(string.Format("'{0}' = '{1}'", it, v == null ? "null" : v.ToString())); }

            sb.AppendLine(".............................................................");
            sb.AppendLine(string.Format("'{0}' = ({1}, {2})", "TextPosition", dim.TextPosition.X, dim.TextPosition.Y));
            //sb.AppendLine(string.Format("'{0}' = ({1}, {2})", "TextDefinedSize", dim.TextDefinedSize.X, dim.TextDefinedSize.Y));
            sb.AppendLine(string.Format("'{0}' = ({1}, {2})", "DimBlockPosition", dim.DimBlockPosition.X, dim.DimBlockPosition.Y));

            var tt = dim.GeometricExtents;
            sb.AppendLine(string.Format("Rangle: (l: {0}, b: {1}, r: {2}, t: {3})", tt.MinPoint.X, tt.MinPoint.Y, tt.MaxPoint.X, tt.MaxPoint.Y));

            if (!dim.Dimblk.IsNull)
            {
                var obj1 = tran.GetObject(dim.Dimblk, OpenMode.ForRead);
                if (obj1 != null) sb.AppendLine(string.Format("Dimblk: {0}", obj1.GetRXClass().DxfName));
            }
            if (!dim.Dimblk1.IsNull)
            {
                var obj2 = tran.GetObject(dim.Dimblk1, OpenMode.ForRead);
                if (obj2 != null) sb.AppendLine(string.Format("Dimblk1: {0}", obj2.GetRXClass().DxfName));
            }
            if (!dim.Dimblk2.IsNull)
            {
                var obj3 = tran.GetObject(dim.Dimblk2, OpenMode.ForRead);
                if (obj3 != null) sb.AppendLine(string.Format("Dimblk2: {0}", obj3.GetRXClass().DxfName));
            }
            if (!dim.DimBlockId.IsNull)
            {
                var obj4 = tran.GetObject(dim.DimBlockId, OpenMode.ForRead);
                if (obj4 != null) sb.AppendLine(string.Format("DimBlockId: {0}", obj4.GetRXClass().DxfName));
            }
            if (!dim.Dimblk.IsNull)
            {
                var obj5 = tran.GetObject(dim.DimensionStyle, OpenMode.ForRead);
                if (obj5 != null) sb.AppendLine(string.Format("DimensionStyle: {0}", obj5.GetRXClass().DxfName));
            }
            if (!dim.Dimldrblk.IsNull)
            {
                var obj6 = tran.GetObject(dim.Dimldrblk, OpenMode.ForRead);
                if (obj6 != null) sb.AppendLine(string.Format("Dimldrblk: {0}", obj6.GetRXClass().DxfName));
            }

            if (!dim.Dimltex1.IsNull)
            {
                var obj7 = tran.GetObject(dim.Dimltex1, OpenMode.ForRead);
                if (obj7 != null) sb.AppendLine(string.Format("Dimltex1: {0}", obj7.GetRXClass().DxfName));
            }
            if (!dim.Dimltex2.IsNull)
            {
                var obj8 = tran.GetObject(dim.Dimltex2, OpenMode.ForRead);
                if (obj8 != null) sb.AppendLine(string.Format("Dimltex2: {0}", obj8.GetRXClass().DxfName));
            }
            if (!dim.Dimltype.IsNull)
            {
                var obj9 = tran.GetObject(dim.Dimltype, OpenMode.ForRead);
                if (obj9 != null) sb.AppendLine(string.Format("Dimltype: {0}", obj9.GetRXClass().DxfName));
            }
        }

        private bool IsTextEntity(Transaction tran, ObjectId objId)
        {
            var obj = tran.GetObject(objId, OpenMode.ForRead);
            return obj is MText || obj is DBText;
        }
        }
}
