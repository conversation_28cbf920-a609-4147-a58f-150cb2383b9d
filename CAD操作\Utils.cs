﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;

using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.ApplicationServices;

using Autodesk.AutoCAD.Geometry;
using CADWSAddin.Tools;

namespace CADWSAddin
{
    public class Utils
    {
        public static DwgVersion GetVersion(string DWGVer)
        {
            DwgVersion dwgVersion;
            switch (DWGVer)
            {
                case Utility.CAD2000:
                    dwgVersion = DwgVersion.AC1015;
                    break;
                case Utility.CAD2004:
                    dwgVersion = DwgVersion.AC1800;
                    break;
                case Utility.CAD2007:
                    dwgVersion = DwgVersion.AC1021;
                    break;
                case Utility.CAD2010:
                    dwgVersion = DwgVersion.AC1024;
                    break;
                case Utility.CAD2013:
                    dwgVersion = DwgVersion.AC1027;
                    break;
                case Utility.CAD2014:
                    dwgVersion = DwgVersion.AC1027;
                    break;
                //case Utility.CAD2018:
                    //dwgVersion = DwgVersion.AC1032;
                    //break;
                default:
                    dwgVersion = DwgVersion.Current;
                    break;
            }

            return dwgVersion;
        }

        public static void AddItemToList(List<int> list, int val)
        {
            if (list.Contains(val)) return;

            list.Add(val);
        }
        public static Database LoadWBlock(string blkFile)
        {
            Database blkDb = new Database(false, true);
            try
            {
                blkDb.ReadDwgFile(blkFile, System.IO.FileShare.Read, true, null);
                blkDb.CloseInput(true);
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(string.Format("LoadWBlock() failed, file: {0}", blkFile));
                Palettes.WriteLog(ex.ToString());
                //Application.ShowAlertDialog(ex.ToString());
            }
            return blkDb;
        }


        public static DrawingSpace GetDrawingSpace(Database db)
        {
            return new DrawingSpace()
            {
                LeftTop = new Point3d(db.Extmin.X, db.Extmax.Y, 0),
                Width = db.Extmax.X - db.Extmin.X,
                Heigth = db.Extmax.Y - db.Extmin.Y
            };
        }

        //判断当前文档是否存在指定块
        public BlockTableRecord GetBlockTableRecord(string blkName, Document doc = null)
        {
            BlockTableRecord btr = null;
            if (doc == null) doc = Application.DocumentManager.MdiActiveDocument;

            using (DocumentLock doclck = doc.LockDocument())
            {
                using (Transaction tran = doc.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tran.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    foreach (ObjectId id in bt)
                    {
                        BlockTableRecord btr1 = tran.GetObject(id, OpenMode.ForRead) as BlockTableRecord;
                        if (string.Compare(btr1.Name, blkName, true) == 0)
                        {
                            btr = btr1;
                            break;
                        }
                    }
                }
            }
            return btr;
        }

        public static void SaveDocument(string newName = "", string ver = "")
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            SaveDocument(doc, newName, ver);            
        }
        public static void SaveDocument(Document doc, string newName = "", string ver = "")
        {
            if (doc == null) return;

            string path = Path.GetDirectoryName(newName);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            using (DocumentLock doclock = doc.LockDocument())
            {

                if (newName == "" || string.Compare(doc.Name, newName, true) == 0)
                {
                    //doc.Database.Save();
                    Palettes.WriteLog(string.Format("doc save failed, newName: '{0}'", newName));
                }
                else
                {
                    try
                    {
                        doc.Database.SaveAs(newName, GetVersion(ver));
                        Palettes.WriteLog(string.Format("save version '{0}'", ver));
                    }
                    catch (Exception)
                    {

                        doc.SendStringToExecute("_qsave", false, false, true);
                    }

                }


            }
        }


        public static Polyline CreateDashedFrame(Point2d upperLeft, Point2d upperRight, Point2d lowerLeft, Point2d lowerRight)
        {
            Polyline pl = new Polyline(4);
            pl.Closed = true;
            pl.AddVertexAt(0, upperLeft, 0, 0, 0);
            pl.AddVertexAt(1, upperRight, 0, 0, 0);
            pl.AddVertexAt(2, lowerRight, 0, 0, 0);
            pl.AddVertexAt(3, lowerLeft, 0, 0, 0);

            return pl;            
            
        }


        public static void SetPropertyValue(DynamicBlockReferenceProperty prop, string value)
        {
            try
            {
                double pi = 3.1415926;
                if (prop.Value is double)
                {
                    if (prop.UnitsType == DynamicBlockReferencePropertyUnitsType.Angular)
                    {
                        prop.Value = double.Parse(value) * pi / 180;
                    }
                    else
                    {
                        prop.Value = double.Parse(value);
                    }
                }
                else if (prop.Value is int)
                {
                    prop.Value = int.Parse(value);
                }
                else if (prop.Value is string)
                {
                    prop.Value = value;
                }
            }
            catch (System.Exception ex)
            {
                Palettes.WriteLog(ex.Message, 2);
                Palettes.WriteLog(ex.StackTrace, 2);
            }
        }



    }
}
